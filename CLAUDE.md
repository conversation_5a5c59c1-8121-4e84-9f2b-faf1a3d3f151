# Portfolio Tracker - Custom Instructions

## Tech Stack
- **Mobile**: React Native + Expo (SDK 50)
- **Web**: Next.js 14 with App Router
- **Backend**: Supabase (PostgreSQL + Auth + Realtime)
- **State Management**: Zustand
- **UI**: NativeWind (mobile) + Tailwind CSS (web)
- **Charts**: Victory Native (mobile) + Recharts (web)
- **Forms**: React Hook Form + Zod validation

## PROJECT CONTEXT
You are building a cross-platform personal finance app with the following priorities:
1. Code reusability between web and mobile (utilities and database functions) - UI needs to be separated in different files inside apps/mobile and apps/web
2. Type safety throughout the application (TypeScript strict mode)
3. Offline-first architecture with sync capabilities
4. Security-first approach for all financial data
5. Concentrate on web app development once all the features are completed - then develop UI for mobile app

## CODING STANDARDS
- Use TypeScript with strict mode enabled
- Follow React Native best practices for cross-platform code
- Implement proper error boundaries and fallbacks
- Use environment variables for all sensitive data
- Write self-documenting code with JSD<PERSON> comments
- Follow atomic design principles for components
- Implement proper loading and error states for all async operations

## NAMING CONVENTIONS
- Components: PascalCase (ExpenseCard.tsx)
- Utilities: camelCase (formatCurrency.ts)
- Types/Interfaces: PascalCase with 'I' or 'T' prefix (IExpense, TCategory)
- Constants: UPPER_SNAKE_CASE (MAX_BUDGET_AMOUNT)
- Database tables: snake_case (expense_categories)
- API endpoints: kebab-case (/api/get-expenses)

## SECURITY REQUIREMENTS
- Never store sensitive data in plain text
- Implement rate limiting on all API endpoints
- Use prepared statements for all database queries
- Validate all user inputs on both client and server
- Implement proper session management with refresh tokens
- Use HTTPS for all network requests
- Encrypt sensitive data at rest using AES-256

## PERFORMANCE TARGETS
- First Contentful Paint < 1.5s
- Time to Interactive < 3s
- Bundle size < 2MB for initial load
- 60 FPS animations on mobile
- Offline capability for core features

## SESSION WORKFLOW INSTRUCTIONS
Each time a new session is created, follow this workflow:

### 1. Phase Detection
- Read `DEVELOPMENT_PLAN.md` to understand project structure
- Check `PHASES_OVERVIEW.md` to identify current active phase
- Load the appropriate `PHASE_X.md` file dynamically based on current phase status

### 2. Task Synchronization
- Read `TODO.md` to see current task list
- Use `TodoRead` to check TodoWrite system status
- Sync TODO.md tasks with TodoWrite system if they don't match
- Ensure completion criteria from active phase file are properly tracked

### 3. Current Phase Tracking
- **Active Phase**: Determined dynamically from PHASES_OVERVIEW.md status
- **Phase File**: Load `PHASE_X.md` where X matches current phase number
- **Completion Criteria**: Extract from active phase file for each task
- **Next Steps**: Follow tasks in current phase file, respecting priority order

### 4. Task Status Management
- Mark tasks as completed ONLY when completion criteria are fully met
- Update task status in real-time across all systems:
  - TodoWrite system status
  - TODO.md checkbox status
  - **PHASE_X.md completion criteria checkboxes** (CRITICAL)
- Progress to next phase only when all current phase tasks complete
- Update PHASES_OVERVIEW.md status when phase changes

### 4a. Completion Criteria Updates (MANDATORY)
When marking a task as completed, ALWAYS update the completion criteria in the active phase file:
- Find the task's "Completion Criteria" section in PHASE_X.md
- Change all `- [ ]` to `- [x]` and add ✅ emoji
- This ensures phase file accurately reflects current progress
- Future sessions will see completed status in phase documentation

### 5. Commands to Remember
After implementing each session, these commands should be known:
- Project setup: Check for package.json scripts
- Testing: Look for test commands in package.json or project docs
- Building: Identify build commands from project configuration
- Linting: Find linting/formatting commands to run before commits

This workflow ensures all documentation files stay in sync and task progress is accurately tracked across sessions.