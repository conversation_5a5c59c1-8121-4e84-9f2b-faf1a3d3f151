# Category Duplication Debug Report

## Issue Description
Categories are showing up 4 times in the Add Transaction page.

## Root Cause Analysis

### Multiple Category Dropdowns Found:
1. **ExpenseForm** - Main category selection (lines 216-230)
2. **TransactionTemplates** - Template creation form (lines 214-226) 
3. **TransactionList.web** - Filter dropdown (lines 147-164)
4. **BudgetForm** - Budget category selection (lines 157-172)

### Different Data Fetching Methods:
1. **ExpenseService.getCategories()** - User + default categories via `.or()` query
2. **TransactionTemplates.fetchCategories()** - Only user categories via `.eq('user_id')`

### Key Issues Identified:

#### 1. Multiple Dropdowns on Same Page
The Add Transaction page contains:
- ExpenseForm (with category dropdown)
- TransactionTemplates (embedded in ExpenseForm, with its own category dropdown)
- TransactionList.web (with category filter dropdown)

#### 2. Inconsistent Data Fetching
- ExpenseForm uses categories passed from parent (fetched via ExpenseService.getCategories())
- TransactionTemplates fetches its own categories directly from Supabase
- TransactionList also fetches its own categories

#### 3. Potential Database Query Issue
ExpenseService.getCategories() query:
```sql
.or(`user_id.eq.${user.id},and(is_default.eq.true,user_id.is.null)`)
```
Could return duplicates if there are data inconsistencies.

## Debugging Steps Needed:

### 1. Check Database Query Results
- Verify if ExpenseService.getCategories() returns duplicates
- Check if default categories exist with proper is_default flags
- Ensure no duplicate user categories in database

### 2. Check Component Rendering
- Verify how many category dropdowns are actually visible on Add Transaction page
- Check if any components are being rendered multiple times
- Inspect DOM to count actual dropdowns

### 3. Data Flow Analysis
- Trace how categories flow from database to each component
- Check if any components are concatenating or merging category arrays incorrectly

## Immediate Fixes to Implement:

### 1. Centralize Category Data Fetching
Create a single source of truth for category data using React Context or a custom hook.

### 2. Fix TransactionTemplates Data Fetching
Make TransactionTemplates use the same category data as ExpenseForm instead of fetching separately.

### 3. Add Data Deduplication
Ensure all category arrays are deduplicated before rendering.

### 4. Database Query Optimization
Review and fix the ExpenseService.getCategories() query to prevent duplicates.

## Files to Check/Modify:
- `/packages/shared/src/components/ExpenseForm.tsx` (lines 216-230)
- `/packages/shared/src/components/TransactionTemplates.tsx` (lines 67-84, 214-226)
- `/packages/shared/src/components/TransactionList.web.tsx` (lines 147-164)
- `/packages/shared/src/lib/expenses.ts` (lines 160-195)
- `/apps/web/src/app/expenses/page.tsx` (category fetching and passing)