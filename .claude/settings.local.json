{"permissions": {"allow": ["Bash(npx create-turbo:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx create-expo-app:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(rmdir:*)", "Bash(npx create-next-app:*)", "Bash(npm install)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run build:*)", "mcp__supabase-mcp-server__list_projects", "mcp__supabase-mcp-server__get_project", "mcp__supabase-mcp-server__get_project_url", "mcp__supabase-mcp-server__get_anon_key", "mcp__supabase-mcp-server__list_tables", "mcp__supabase-mcp-server__apply_migration", "mcp__supabase-mcp-server__generate_typescript_types", "mcp__supabase-mcp-server__execute_sql", "Bash(npm install:*)", "Bash(rm:*)", "Bash(npm run type-check:*)", "Bash(find:*)", "Bash(npm run dev:*)", "Bash(npx tsc:*)", "Bash(npm run lint)", "Bash(ls:*)", "Bash(timeout 10s npm run dev)", "Bash(npx next dev --port 3001)", "Bash(npx turbo build:*)", "Bash(rg:*)", "Bash(npm run typecheck:*)", "Bash(grep:*)", "mcp__supabase-mcp-server__list_migrations", "Bash(cp:*)", "mcp__ide__getDiagnostics", "Bash(npm ls:*)", "mcp__supabase-mcp-server__get_logs"], "deny": []}}