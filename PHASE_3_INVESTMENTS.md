# Phase 3: Investment Portfolio Tracking (Months 5-6)

## Overview
Add comprehensive investment tracking for stocks, ETFs, mutual funds, and cryptocurrencies with real-time market data and performance analytics.

## Prerequisites
- Phase 1 MVP deployed and stable
- Phase 2 banking integration functional
- Market data API accounts established
- Investment tracking user demand validated

## Month 5: Stock Market Integration

### Task 3.1: Build Portfolio Management System
**Description**: Create core investment tracking infrastructure
**Timeline**: 2 weeks
**Priority**: High

**Steps**:
1. Create portfolio database schema
2. Build "Add Investment" flow
3. Implement stock search API
4. Create portfolio dashboard
5. Add transaction tracking (buy/sell)
6. Implement dividend tracking
7. Build portfolio sharing feature

**Database Schema**:
```sql
-- Portfolios
CREATE TABLE portfolios (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  is_public BOOLEAN DEFAULT false,
  total_value DECIMAL(15,2) DEFAULT 0,
  total_cost DECIMAL(15,2) DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Holdings
CREATE TABLE holdings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  portfolio_id UUID REFERENCES portfolios(id) ON DELETE CASCADE,
  symbol VARCHAR(10) NOT NULL,
  quantity DECIMAL(15,4) NOT NULL,
  average_price DECIMAL(10,2) NOT NULL,
  current_price DECIMAL(10,2),
  market_value DECIMAL(15,2),
  unrealized_gain_loss DECIMAL(15,2),
  last_updated TIMESTAMPTZ,
  UNIQUE(portfolio_id, symbol)
);

-- Investment Transactions
CREATE TABLE investment_transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  portfolio_id UUID REFERENCES portfolios(id) ON DELETE CASCADE,
  symbol VARCHAR(10) NOT NULL,
  transaction_type VARCHAR(20) CHECK (transaction_type IN ('buy', 'sell', 'dividend', 'split', 'merger')),
  quantity DECIMAL(15,4),
  price DECIMAL(10,2) NOT NULL,
  fees DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(15,2) NOT NULL,
  transaction_date DATE NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Market Data Cache
CREATE TABLE market_data (
  symbol VARCHAR(10) PRIMARY KEY,
  current_price DECIMAL(10,2),
  change_amount DECIMAL(10,2),
  change_percent DECIMAL(5,2),
  volume BIGINT,
  market_cap BIGINT,
  pe_ratio DECIMAL(8,2),
  dividend_yield DECIMAL(5,2),
  last_updated TIMESTAMPTZ
);
```

**Investment Form Components**:
```typescript
interface IInvestmentTransaction {
  symbol: string;
  transaction_type: 'buy' | 'sell' | 'dividend';
  quantity: number;
  price: number;
  fees: number;
  transaction_date: Date;
  notes?: string;
}

interface IPortfolio {
  name: string;
  description?: string;
  is_public: boolean;
  holdings: IHolding[];
}
```

**Completion Criteria**:
- [ ] Multiple portfolios can be created per user
- [ ] Stock search returns accurate results with company info
- [ ] Buy/sell transactions are tracked with cost basis
- [ ] Current prices update automatically
- [ ] P&L calculations are accurate and real-time
- [ ] Dividend tracking with reinvestment options
- [ ] Portfolio performance metrics displayed

### Task 3.2: Integrate Market Data APIs
**Description**: Fetch real-time and historical market data
**Timeline**: 2 weeks
**Priority**: High

**Steps**:
1. Evaluate and integrate data providers
2. Implement price fetching service
3. Add historical data retrieval
4. Create price caching system
5. Build market hours checker
6. Add price alerts feature
7. Implement batch updates

**API Providers**:
```typescript
interface IMarketDataProvider {
  name: string;
  getRealTimePrice(symbol: string): Promise<StockPrice>;
  getHistoricalData(symbol: string, period: string): Promise<HistoricalData[]>;
  searchSymbols(query: string): Promise<SearchResult[]>;
  getBatchPrices(symbols: string[]): Promise<StockPrice[]>;
}

// Primary: Alpha Vantage (free tier)
// Secondary: Yahoo Finance API  
// Tertiary: Twelve Data
// Regional: NSE/BSE APIs for Indian stocks
```

**Caching Strategy**:
- Real-time prices: 1-minute cache during market hours
- Historical data: 24-hour cache
- Company info: 7-day cache
- Market closed: 4-hour cache

**Price Alert System**:
```typescript
interface IPriceAlert {
  symbol: string;
  user_id: string;
  alert_type: 'above' | 'below' | 'change_percent';
  target_value: number;
  is_active: boolean;
  notification_methods: ('email' | 'push' | 'sms')[];
}
```

**Completion Criteria**:
- [ ] Real-time prices during market hours (delay <5 minutes)
- [ ] Historical charts display correctly (1D, 1W, 1M, 1Y, 5Y)
- [ ] API rate limits handled gracefully with fallbacks
- [ ] Price alerts notify users via preferred method
- [ ] Batch updates are efficient (100+ symbols in <30 seconds)
- [ ] Fallback providers configured for 99.9% uptime
- [ ] Cache reduces API calls by 70%+

## Month 6: Cryptocurrency Integration

### Task 3.3: Add Cryptocurrency Portfolio
**Description**: Track crypto holdings and DeFi positions
**Timeline**: 2 weeks
**Priority**: Medium

**Steps**:
1. Extend portfolio schema for crypto
2. Integrate crypto price APIs
3. Add wallet address tracking
4. Implement DeFi position tracking
5. Create crypto-specific metrics
6. Add gas fee tracking
7. Build tax report generation

**Extended Database Schema**:
```sql
-- Crypto Wallets
CREATE TABLE crypto_wallets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_address VARCHAR(255) NOT NULL,
  blockchain VARCHAR(50) NOT NULL, -- ethereum, bitcoin, polygon, etc.
  wallet_name VARCHAR(100),
  is_tracked BOOLEAN DEFAULT true,
  last_sync TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- DeFi Positions
CREATE TABLE defi_positions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  protocol_name VARCHAR(100) NOT NULL,
  position_type VARCHAR(50) NOT NULL, -- staking, lending, liquidity
  token_symbols TEXT[], -- ['ETH', 'USDC']
  amounts DECIMAL(20,8)[],
  apy DECIMAL(8,4),
  start_date DATE,
  current_value DECIMAL(15,2),
  rewards_earned DECIMAL(15,2),
  last_updated TIMESTAMPTZ
);
```

**Crypto API Integration**:
```typescript
interface ICryptoProvider {
  getCurrentPrices(symbols: string[]): Promise<CryptoPrice[]>;
  getWalletBalance(address: string, blockchain: string): Promise<WalletBalance>;
  getTransactionHistory(address: string): Promise<CryptoTransaction[]>;
  getDeFiPositions(address: string): Promise<DeFiPosition[]>;
}

// Primary: CoinGecko API
// Secondary: CoinMarketCap API
// Blockchain: Moralis API, Alchemy
```

**Supported Features**:
- Multi-chain support (Ethereum, Bitcoin, Polygon, BSC)
- Staking rewards tracking
- DeFi yield farming positions
- NFT collection support (basic)
- Gas fee analytics
- Cross-chain bridge tracking

**Completion Criteria**:
- [ ] Major cryptocurrencies supported (BTC, ETH, top 100)
- [ ] Wallet balances sync automatically
- [ ] Transaction history imported from blockchain
- [ ] Staking rewards tracked with APY calculations
- [ ] Tax reports include all crypto transactions
- [ ] Multiple exchanges can be connected
- [ ] DeFi positions calculated correctly with impermanent loss

### Task 3.4: Build Unified Investment Dashboard
**Description**: Combine all investments in comprehensive view
**Timeline**: 2 weeks
**Priority**: High

**Steps**:
1. Create unified portfolio view
2. Build asset allocation chart
3. Add performance comparison
4. Implement risk metrics
5. Create investment insights
6. Add export functionality
7. Build sharing features

**Dashboard Components**:
```typescript
interface IUnifiedPortfolio {
  total_value: number;
  total_cost: number;
  unrealized_gain_loss: number;
  realized_gain_loss: number;
  asset_allocation: {
    stocks: number;
    crypto: number;
    cash: number;
    other: number;
  };
  performance: {
    day_change: number;
    week_change: number;
    month_change: number;
    year_change: number;
    all_time_change: number;
  };
  risk_metrics: {
    beta: number;
    sharpe_ratio: number;
    volatility: number;
    max_drawdown: number;
  };
}
```

**Key Metrics**:
- Total portfolio value with real-time updates
- Asset allocation breakdown with rebalancing suggestions
- Performance vs major indices (S&P 500, NASDAQ, Bitcoin)
- Risk assessment score with explanations
- Correlation analysis between holdings
- Dividend yield and income projections

**Insights Engine**:
```typescript
interface IInvestmentInsight {
  type: 'rebalance' | 'opportunity' | 'risk' | 'tax';
  title: string;
  description: string;
  recommended_action: string;
  potential_impact: number;
  confidence: number;
}
```

**Completion Criteria**:
- [ ] All asset types displayed in unified view
- [ ] Asset allocation charts with drill-down capability
- [ ] Performance metrics accurate and benchmarked
- [ ] Risk score methodology clear and actionable
- [ ] Insights are personalized and valuable
- [ ] Export includes all positions and performance data
- [ ] Sharing respects privacy settings and regulations

## Technical Implementation

### Real-time Updates
- WebSocket connections for live price updates
- Server-sent events for portfolio changes
- Background sync every 15 minutes during market hours
- Push notifications for significant price movements

### Performance Optimization
- Lazy loading for historical charts
- Pagination for large portfolios (1000+ holdings)
- Caching layer for expensive calculations
- CDN for static market data

### Security & Compliance
- Read-only API keys for market data
- Encrypted storage for wallet addresses
- Compliance with financial data regulations
- Audit trail for all investment transactions

## Integration Points

### Phase 2 Dependencies
- Bank connection for investment account sync
- Transaction categorization system
- Automated data import capabilities

### Future Phase Benefits
- Capital gains/losses for tax reporting (Phase 4)
- Family investment education (Phase 5)
- Advanced portfolio optimization

## Success Metrics
- 40%+ of users track at least one investment
- 95%+ price data accuracy
- <3 second dashboard load time
- 85% user satisfaction with insights
- 50% of users set up price alerts
- 99.5% uptime for market data feeds