{"name": "portfolio-tracker", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "test": "turbo test", "clean": "turbo clean", "type-check": "turbo type-check"}, "devDependencies": {"@turbo/gen": "^1.13.4", "@types/react": "^19.1.8", "@types/react-native": "^0.72.8", "eslint": "^8.57.0", "ignore-loader": "^0.1.2", "prettier": "^3.3.2", "turbo": "^2.0.0", "typescript": "^5.5.0"}, "packageManager": "npm@10.0.0", "engines": {"node": ">=18"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/native": "^7.1.11", "@react-navigation/native-stack": "^7.3.16", "@supabase/supabase-js": "^2.50.0", "@types/uuid": "^10.0.0", "dotenv": "^16.5.0", "expo-crypto": "^14.1.5", "expo-local-authentication": "^16.0.4", "expo-secure-store": "^14.2.3", "react": "^19.1.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "recharts": "^2.15.3", "uuid": "^11.1.0", "zod": "^3.25.67", "zustand": "^5.0.5"}}