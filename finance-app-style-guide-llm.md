# Personal Finance App Style Guide - LLM Reference

## Project Context
You are building a cross-platform personal finance app with React Native (mobile) and Next.js (web). This style guide defines all design tokens, patterns, and components to ensure consistency.

## Design Tokens

### Colors
```css
/* Primary Colors */
--primary-blue: #3B82F6;
--primary-purple: #8B5CF6;
--primary-gradient: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);

/* Semantic Colors */
--success-green: #10B981;     /* Income, positive changes */
--error-red: #EF4444;         /* Expenses, negative values */
--warning-orange: #F59E0B;    /* Budget alerts */
--info-blue: #3B82F6;         /* Information */

/* Light Mode */
--background: #FFFFFF;
--surface: #F9FAFB;
--surface-elevated: #FFFFFF;
--text-primary: #111827;
--text-secondary: #6B7280;
--text-tertiary: #9CA3AF;
--border: #E5E7EB;
--border-light: #F3F4F6;

/* Dark Mode */
--dark-background: #0A0A0B;
--dark-surface: #18181B;
--dark-surface-elevated: #27272A;
--dark-text-primary: #F9FAFB;
--dark-text-secondary: #A1A1AA;
--dark-text-tertiary: #71717A;
--dark-border: #27272A;
--dark-border-light: #18181B;

/* Gradient Cards */
--gradient-purple: linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%);
--gradient-blue: linear-gradient(135deg, #3B82F6 0%, #06B6D4 100%);
--gradient-green: linear-gradient(135deg, #10B981 0%, #34D399 100%);
--gradient-orange: linear-gradient(135deg, #F59E0B 0%, #FB923C 100%);
```

### Typography
```css
/* Font Family */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
--font-mono: 'Roboto Mono', monospace;

/* Font Sizes */
--text-xs: 0.75rem;     /* 12px - small labels */
--text-sm: 0.875rem;    /* 14px - secondary text */
--text-base: 1rem;      /* 16px - body text */
--text-lg: 1.125rem;    /* 18px - section headers */
--text-xl: 1.25rem;     /* 20px - card titles */
--text-2xl: 1.5rem;     /* 24px - page headers */
--text-3xl: 1.875rem;   /* 30px - large numbers */
--text-4xl: 2.25rem;    /* 36px - hero text */
--text-5xl: 3rem;       /* 48px - display text */

/* Font Weights */
--font-normal: 400;     /* Body text */
--font-medium: 500;     /* Buttons, labels */
--font-semibold: 600;   /* Headings */
--font-bold: 700;       /* Emphasis, amounts */
```

### Spacing
```css
--space-1: 0.25rem;    /* 4px - tight spacing */
--space-2: 0.5rem;     /* 8px - compact elements */
--space-3: 0.75rem;    /* 12px - form padding */
--space-4: 1rem;       /* 16px - standard padding */
--space-5: 1.25rem;    /* 20px - section spacing */
--space-6: 1.5rem;     /* 24px - card padding */
--space-8: 2rem;       /* 32px - large gaps */
--space-10: 2.5rem;    /* 40px - section margins */
```

### Border Radius
```css
--radius-sm: 0.125rem;    /* 2px - subtle rounding */
--radius-base: 0.25rem;   /* 4px - minimal rounding */
--radius-md: 0.375rem;    /* 6px - small elements */
--radius-lg: 0.5rem;      /* 8px - buttons */
--radius-xl: 0.75rem;     /* 12px - cards */
--radius-2xl: 1rem;       /* 16px - modals */
--radius-3xl: 1.5rem;     /* 24px - large cards */
--radius-full: 9999px;    /* Pills, avatars */
```

### Shadows
```css
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
```

## Component Specifications

### Card Component
```jsx
/* Base Card */
className="bg-surface-elevated rounded-xl border border-border-light p-6 shadow-sm hover:shadow-md transition-all duration-200"

/* Gradient Card (bank cards, premium) */
className="bg-gradient-to-br from-primary-blue to-primary-purple text-white rounded-xl p-6 shadow-lg relative overflow-hidden"

/* Glass Card (overlays) */
className="bg-white/80 dark:bg-dark-surface/80 backdrop-blur-lg rounded-xl border border-white/20 p-6"
```

### Button Component
```jsx
/* Primary Button */
className="bg-gradient-to-r from-primary-blue to-primary-purple text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5"

/* Secondary Button */
className="bg-surface text-text-primary border border-border px-6 py-3 rounded-lg font-medium hover:bg-surface-elevated transition-colors"

/* Icon Button */
className="w-12 h-12 rounded-full bg-surface flex items-center justify-center shadow-sm hover:shadow-md transition-all"

/* Text Button */
className="text-primary-blue font-medium hover:underline transition-all"
```

### Input Component
```jsx
/* Text Input */
className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-4 focus:ring-primary-blue/10"

/* Amount Input */
className="text-3xl font-bold text-center bg-transparent border-none focus:outline-none w-full"

/* Select Dropdown */
className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 appearance-none cursor-pointer"
```

### Navigation Patterns
```jsx
/* Bottom Navigation (Mobile) */
className="fixed bottom-0 left-0 right-0 bg-surface-elevated border-t border-border-light flex justify-around items-center py-2 backdrop-blur-lg"

/* Navigation Item */
className="flex flex-col items-center gap-1 p-2 text-text-tertiary [&.active]:text-primary-blue"

/* Sidebar (Desktop) */
className="w-64 bg-surface border-r border-border-light h-full p-6"

/* Sidebar Item */
className="flex items-center gap-3 px-4 py-3 rounded-lg text-text-secondary hover:bg-surface-elevated hover:text-text-primary transition-all [&.active]:bg-gradient-to-r [&.active]:from-primary-blue [&.active]:to-primary-purple [&.active]:text-white"
```

### List Items
```jsx
/* Transaction Item */
className="flex items-center justify-between p-4 border-b border-border-light hover:bg-surface transition-colors"

/* Transaction Icon Container */
className="w-12 h-12 rounded-lg bg-surface flex items-center justify-center"

/* Amount Positive */
className="text-success-green font-semibold"

/* Amount Negative */
className="text-error-red font-semibold"
```

### Data Display
```jsx
/* Stat Card */
className="bg-surface-elevated rounded-xl p-6 border border-border-light"

/* Progress Bar Container */
className="w-full h-2 bg-border-light rounded-full overflow-hidden"

/* Progress Bar Fill */
className="h-full bg-gradient-to-r from-primary-blue to-primary-purple rounded-full transition-all duration-300"

/* Chart Container */
className="bg-surface-elevated rounded-xl p-6 border border-border-light"
```

### Modal/Sheet
```jsx
/* Modal Backdrop */
className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"

/* Modal Content */
className="fixed bottom-0 left-0 right-0 bg-surface-elevated rounded-t-3xl p-6 max-h-[90vh] overflow-y-auto z-50 animate-slide-up"

/* Desktop Modal */
className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-surface-elevated rounded-2xl p-8 max-w-md w-full shadow-xl z-50"
```

## Icon Usage
- **Icon Library**: Lucide React
- **Default Size**: 20px (w-5 h-5)
- **Medium Size**: 24px (w-6 h-6)
- **Large Size**: 32px (w-8 h-8)
- **Stroke Width**: 2px

### Common Icons Mapping
```
Dashboard: LayoutDashboard
Transactions: Receipt
Budget: PiggyBank
Investment: TrendingUp
Cards: CreditCard
Settings: Settings
Profile: User
Add: Plus
Delete: Trash2
Edit: Pencil
Search: Search
Filter: Filter
Calendar: Calendar
Notification: Bell
Security: Shield
Export: Download
Import: Upload
Income: TrendingUp
Expense: TrendingDown
Category: Tag
Wallet: Wallet
Bank: Building
Crypto: Bitcoin
Stocks: LineChart
Close: X
Menu: Menu
Back: ChevronLeft
Forward: ChevronRight
```

## Animation Classes
```css
/* Transitions */
transition-all duration-200 ease-in-out
transition-colors duration-150
transition-transform duration-200
transition-shadow duration-200

/* Hover Effects */
hover:scale-105
hover:-translate-y-0.5
hover:shadow-lg

/* Loading States */
animate-pulse
animate-spin
animate-shimmer

/* Page Transitions */
animate-fade-in
animate-slide-up
animate-slide-in-right
```

## Responsive Breakpoints
```css
/* Mobile First */
sm: 640px   /* Tablet */
md: 768px   /* Small laptop */
lg: 1024px  /* Desktop */
xl: 1280px  /* Large desktop */
2xl: 1536px /* Extra large */
```

## Platform-Specific Rules

### iOS
- Use safe-area-inset-* for notch/home indicator
- Implement haptic feedback for interactions
- Use iOS-style bottom sheets
- Support iOS gestures

### Android
- Use elevation instead of shadows where possible
- Implement Material Design ripple effects
- Support back gesture navigation
- Use FAB for primary actions

### Web
- Implement keyboard navigation
- Add focus-visible states
- Support mouse hover states
- Ensure responsive design

## Financial UI Patterns

### Amount Display
```jsx
/* Large Amount */
<span className="text-3xl font-bold text-text-primary">$12,129.91</span>

/* Small Amount with Sign */
<span className="text-sm font-medium text-success-green">+$250.00</span>
<span className="text-sm font-medium text-error-red">-$45.99</span>

/* Percentage Change */
<span className="text-xs font-medium text-success-green flex items-center gap-1">
  <TrendingUp className="w-3 h-3" /> 3.2%
</span>
```

### Budget Indicator
```jsx
<div className="space-y-2">
  <div className="flex justify-between text-sm">
    <span className="text-text-secondary">Spent</span>
    <span className="font-medium">$750 / $1,000</span>
  </div>
  <div className="w-full h-2 bg-border-light rounded-full overflow-hidden">
    <div className="h-full bg-warning-orange rounded-full" style={{width: '75%'}} />
  </div>
</div>
```

### Category Badge
```jsx
<div className="inline-flex items-center gap-2 px-3 py-1.5 bg-primary-blue/10 text-primary-blue rounded-full">
  <Coffee className="w-4 h-4" />
  <span className="text-sm font-medium">Food & Dining</span>
</div>
```

## Best Practices for Implementation

1. **Always use semantic color variables** instead of hardcoded values
2. **Maintain consistent spacing** using the spacing scale
3. **Use gradients sparingly** for emphasis and premium features
4. **Implement loading states** for all async operations
5. **Provide haptic/visual feedback** for all interactions
6. **Ensure text remains readable** with proper contrast ratios
7. **Use consistent animation timing** (200ms for most transitions)
8. **Apply hover states** only on web with @media (hover: hover)
9. **Test dark mode** thoroughly for all components
10. **Optimize for performance** with lazy loading and code splitting

## Accessibility Requirements
- Minimum contrast ratio: 4.5:1 for normal text
- Touch targets: minimum 44x44px
- Focus indicators: 2px outline with 2px offset
- ARIA labels for all icon-only buttons
- Semantic HTML structure
- Keyboard navigation support

## State Management
- Loading: Show skeleton screens or spinners
- Empty: Display helpful empty states with actions
- Error: Show clear error messages with retry options
- Success: Provide positive feedback for completed actions
- Disabled: Reduce opacity to 0.5 and remove interactions