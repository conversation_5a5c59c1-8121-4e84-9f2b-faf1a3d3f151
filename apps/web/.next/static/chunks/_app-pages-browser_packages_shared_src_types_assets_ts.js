"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_packages_shared_src_types_assets_ts"],{

/***/ "(app-pages-browser)/../../packages/shared/src/types/assets.ts":
/*!*************************************************!*\
  !*** ../../packages/shared/src/types/assets.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ASSET_CLASSES: () => (/* binding */ DEFAULT_ASSET_CLASSES)\n/* harmony export */ });\n// Asset class types and interfaces\n// Predefined asset classes with Indian tax implications\nconst DEFAULT_ASSET_CLASSES = [\n    {\n        name: 'Equity Shares',\n        class: 'stocks',\n        sub_class: 'large_cap',\n        description: 'Listed equity shares on stock exchanges',\n        risk_level: 'high',\n        liquidity: 'high',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'equity',\n        ltcg_period_months: 12,\n        ltcg_tax_rate: 10,\n        stcg_tax_rate: 15,\n        dividend_tax_rate: 0 // Dividend income taxed as per slab\n    },\n    {\n        name: 'Equity Mutual Funds',\n        class: 'mutual_funds',\n        sub_class: 'equity_funds',\n        description: 'Mutual funds investing primarily in equity',\n        risk_level: 'high',\n        liquidity: 'high',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'equity',\n        ltcg_period_months: 12,\n        ltcg_tax_rate: 10,\n        stcg_tax_rate: 15\n    },\n    {\n        name: 'Debt Mutual Funds',\n        class: 'mutual_funds',\n        sub_class: 'debt_funds',\n        description: 'Mutual funds investing primarily in debt instruments',\n        risk_level: 'medium',\n        liquidity: 'high',\n        typical_holding_period: 'medium_term',\n        tax_treatment: 'debt',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30 // As per income tax slab\n    },\n    {\n        name: 'Government Bonds',\n        class: 'bonds',\n        sub_class: 'government_bonds',\n        description: 'Government issued bonds and securities',\n        risk_level: 'low',\n        liquidity: 'medium',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'debt',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30\n    },\n    {\n        name: 'Corporate Bonds',\n        class: 'bonds',\n        sub_class: 'corporate_bonds',\n        description: 'Corporate issued bonds and debentures',\n        risk_level: 'medium',\n        liquidity: 'medium',\n        typical_holding_period: 'medium_term',\n        tax_treatment: 'debt',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30\n    },\n    {\n        name: 'Exchange Traded Funds',\n        class: 'etfs',\n        sub_class: 'equity_etfs',\n        description: 'Exchange traded funds tracking various indices',\n        risk_level: 'medium',\n        liquidity: 'high',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'equity',\n        ltcg_period_months: 12,\n        ltcg_tax_rate: 10,\n        stcg_tax_rate: 15\n    },\n    {\n        name: 'Real Estate Investment Trusts',\n        class: 'real_estate',\n        sub_class: 'reits',\n        description: 'REITs investing in real estate properties',\n        risk_level: 'medium',\n        liquidity: 'medium',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'other',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30\n    },\n    {\n        name: 'Gold ETFs',\n        class: 'commodities',\n        sub_class: 'precious_metals',\n        description: 'Gold exchange traded funds',\n        risk_level: 'medium',\n        liquidity: 'high',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'other',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/types/assets.ts\n"));

/***/ })

}]);