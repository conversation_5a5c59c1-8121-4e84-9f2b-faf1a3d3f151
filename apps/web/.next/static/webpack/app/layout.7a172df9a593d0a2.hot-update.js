"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/../../packages/shared/src/index.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/index.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccountService: () => (/* reexport safe */ _lib_accounts__WEBPACK_IMPORTED_MODULE_12__.AccountService),\n/* harmony export */   AnalyticsService: () => (/* reexport safe */ _lib_analytics__WEBPACK_IMPORTED_MODULE_7__.AnalyticsService),\n/* harmony export */   AssetClassService: () => (/* reexport safe */ _lib_assets__WEBPACK_IMPORTED_MODULE_14__.AssetClassService),\n/* harmony export */   AssetService: () => (/* reexport safe */ _lib_assets__WEBPACK_IMPORTED_MODULE_14__.AssetService),\n/* harmony export */   BiometricService: () => (/* reexport safe */ _lib_biometric_web__WEBPACK_IMPORTED_MODULE_17__.BiometricService),\n/* harmony export */   BudgetService: () => (/* reexport safe */ _lib_budget__WEBPACK_IMPORTED_MODULE_6__.BudgetService),\n/* harmony export */   CategoryService: () => (/* reexport safe */ _lib_categories__WEBPACK_IMPORTED_MODULE_13__.CategoryService),\n/* harmony export */   Constants: () => (/* reexport safe */ _database_types__WEBPACK_IMPORTED_MODULE_3__.Constants),\n/* harmony export */   DEFAULT_ASSET_CLASSES: () => (/* reexport safe */ _types_assets__WEBPACK_IMPORTED_MODULE_23__.DEFAULT_ASSET_CLASSES),\n/* harmony export */   ExpenseService: () => (/* reexport safe */ _lib_expenses__WEBPACK_IMPORTED_MODULE_5__.ExpenseService),\n/* harmony export */   HoldingService: () => (/* reexport safe */ _lib_assets__WEBPACK_IMPORTED_MODULE_14__.HoldingService),\n/* harmony export */   InvestmentService: () => (/* reexport safe */ _lib_investments__WEBPACK_IMPORTED_MODULE_11__.InvestmentService),\n/* harmony export */   ProfitLossService: () => (/* reexport safe */ _lib_profit_loss__WEBPACK_IMPORTED_MODULE_15__.ProfitLossService),\n/* harmony export */   RecurringTransactionService: () => (/* reexport safe */ _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__.RecurringTransactionService),\n/* harmony export */   TaxCalculatorService: () => (/* reexport safe */ _lib_tax_calculator__WEBPACK_IMPORTED_MODULE_16__.TaxCalculatorService),\n/* harmony export */   TransactionService: () => (/* reexport safe */ _lib_transactions__WEBPACK_IMPORTED_MODULE_9__.TransactionService),\n/* harmony export */   TransferService: () => (/* reexport safe */ _lib_transfers__WEBPACK_IMPORTED_MODULE_10__.TransferService),\n/* harmony export */   accountSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.accountSchema),\n/* harmony export */   assetClassEnum: () => (/* reexport safe */ _schemas_assets__WEBPACK_IMPORTED_MODULE_22__.assetClassEnum),\n/* harmony export */   assetClassFormInputSchema: () => (/* reexport safe */ _schemas_assets__WEBPACK_IMPORTED_MODULE_22__.assetClassFormInputSchema),\n/* harmony export */   assetClassFormSchema: () => (/* reexport safe */ _schemas_assets__WEBPACK_IMPORTED_MODULE_22__.assetClassFormSchema),\n/* harmony export */   assetClassSchema: () => (/* reexport safe */ _schemas_assets__WEBPACK_IMPORTED_MODULE_22__.assetClassSchema),\n/* harmony export */   assetFormInputSchema: () => (/* reexport safe */ _schemas_assets__WEBPACK_IMPORTED_MODULE_22__.assetFormInputSchema),\n/* harmony export */   assetFormSchema: () => (/* reexport safe */ _schemas_assets__WEBPACK_IMPORTED_MODULE_22__.assetFormSchema),\n/* harmony export */   assetSchema: () => (/* reexport safe */ _schemas_assets__WEBPACK_IMPORTED_MODULE_22__.assetSchema),\n/* harmony export */   assetSubClassEnum: () => (/* reexport safe */ _schemas_assets__WEBPACK_IMPORTED_MODULE_22__.assetSubClassEnum),\n/* harmony export */   budgetFormInputSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_20__.budgetFormInputSchema),\n/* harmony export */   budgetFormSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_20__.budgetFormSchema),\n/* harmony export */   budgetSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_20__.budgetSchema),\n/* harmony export */   calculateBudgetProgress: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.calculateBudgetProgress),\n/* harmony export */   categorySchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.categorySchema),\n/* harmony export */   expenseFormInputSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_19__.expenseFormInputSchema),\n/* harmony export */   expenseFormSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_19__.expenseFormSchema),\n/* harmony export */   expenseSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_19__.expenseSchema),\n/* harmony export */   formatCurrency: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency),\n/* harmony export */   formatDate: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatDate),\n/* harmony export */   holdingSchema: () => (/* reexport safe */ _schemas_assets__WEBPACK_IMPORTED_MODULE_22__.holdingSchema),\n/* harmony export */   investmentFormInputSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__.investmentFormInputSchema),\n/* harmony export */   investmentFormSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__.investmentFormSchema),\n/* harmony export */   investmentSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.investmentSchema),\n/* harmony export */   investmentTransactionWithAssetSchema: () => (/* reexport safe */ _schemas_assets__WEBPACK_IMPORTED_MODULE_22__.investmentTransactionWithAssetSchema),\n/* harmony export */   legacyTransactionSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.legacyTransactionSchema),\n/* harmony export */   mainTransactionFormInputSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__.mainTransactionFormInputSchema),\n/* harmony export */   mainTransactionFormSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__.mainTransactionFormSchema),\n/* harmony export */   mainTransactionSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__.mainTransactionSchema),\n/* harmony export */   mainTransactionValidatedSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__.mainTransactionValidatedSchema),\n/* harmony export */   resetPasswordSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_18__.resetPasswordSchema),\n/* harmony export */   signInSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_18__.signInSchema),\n/* harmony export */   signUpSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_18__.signUpSchema),\n/* harmony export */   supabase: () => (/* reexport safe */ _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase),\n/* harmony export */   supabaseMobile: () => (/* reexport safe */ _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_25__.supabase),\n/* harmony export */   taxCalculationResultSchema: () => (/* reexport safe */ _schemas_assets__WEBPACK_IMPORTED_MODULE_22__.taxCalculationResultSchema),\n/* harmony export */   taxCalculationSchema: () => (/* reexport safe */ _schemas_assets__WEBPACK_IMPORTED_MODULE_22__.taxCalculationSchema),\n/* harmony export */   transactionFormInputSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__.transactionFormInputSchema),\n/* harmony export */   transactionFormSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__.transactionFormSchema),\n/* harmony export */   transactionSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__.transactionSchema),\n/* harmony export */   transactionValidatedSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__.transactionValidatedSchema),\n/* harmony export */   transferFormInputSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__.transferFormInputSchema),\n/* harmony export */   transferFormSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__.transferFormSchema),\n/* harmony export */   transferSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.transferSchema),\n/* harmony export */   useCurrencyStore: () => (/* reexport safe */ _stores_currencyStore__WEBPACK_IMPORTED_MODULE_24__.useCurrencyStore)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/../../packages/shared/src/types.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/../../packages/shared/src/utils.ts\");\n/* harmony import */ var _validators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./validators */ \"(app-pages-browser)/../../packages/shared/src/validators.ts\");\n/* harmony import */ var _database_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./database.types */ \"(app-pages-browser)/../../packages/shared/src/database.types.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _lib_expenses__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/expenses */ \"(app-pages-browser)/../../packages/shared/src/lib/expenses.ts\");\n/* harmony import */ var _lib_budget__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/budget */ \"(app-pages-browser)/../../packages/shared/src/lib/budget.ts\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/analytics */ \"(app-pages-browser)/../../packages/shared/src/lib/analytics.ts\");\n/* harmony import */ var _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/recurring-transactions */ \"(app-pages-browser)/../../packages/shared/src/lib/recurring-transactions.ts\");\n/* harmony import */ var _lib_transactions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/transactions */ \"(app-pages-browser)/../../packages/shared/src/lib/transactions.ts\");\n/* harmony import */ var _lib_transfers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./lib/transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\");\n/* harmony import */ var _lib_investments__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./lib/investments */ \"(app-pages-browser)/../../packages/shared/src/lib/investments.ts\");\n/* harmony import */ var _lib_accounts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./lib/accounts */ \"(app-pages-browser)/../../packages/shared/src/lib/accounts.ts\");\n/* harmony import */ var _lib_categories__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./lib/categories */ \"(app-pages-browser)/../../packages/shared/src/lib/categories.ts\");\n/* harmony import */ var _lib_assets__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./lib/assets */ \"(app-pages-browser)/../../packages/shared/src/lib/assets.ts\");\n/* harmony import */ var _lib_profit_loss__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./lib/profit-loss */ \"(app-pages-browser)/../../packages/shared/src/lib/profit-loss.ts\");\n/* harmony import */ var _lib_tax_calculator__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./lib/tax-calculator */ \"(app-pages-browser)/../../packages/shared/src/lib/tax-calculator.ts\");\n/* harmony import */ var _lib_biometric_web__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./lib/biometric.web */ \"(app-pages-browser)/../../packages/shared/src/lib/biometric.web.ts\");\n/* harmony import */ var _schemas_auth__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./schemas/auth */ \"(app-pages-browser)/../../packages/shared/src/schemas/auth.ts\");\n/* harmony import */ var _schemas_expense__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./schemas/expense */ \"(app-pages-browser)/../../packages/shared/src/schemas/expense.ts\");\n/* harmony import */ var _schemas_budget__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./schemas/budget */ \"(app-pages-browser)/../../packages/shared/src/schemas/budget.ts\");\n/* harmony import */ var _schemas_transaction__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./schemas/transaction */ \"(app-pages-browser)/../../packages/shared/src/schemas/transaction.ts\");\n/* harmony import */ var _schemas_assets__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./schemas/assets */ \"(app-pages-browser)/../../packages/shared/src/schemas/assets.ts\");\n/* harmony import */ var _types_assets__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./types/assets */ \"(app-pages-browser)/../../packages/shared/src/types/assets.ts\");\n/* harmony import */ var _stores_currencyStore__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./stores/currencyStore */ \"(app-pages-browser)/../../packages/shared/src/stores/currencyStore.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./lib/supabase.mobile */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.mobile.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(_lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_25__);\n// Shared business logic and utilities\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Platform-specific biometric exports\n\n\n\n\n\n\n\n\n// Platform-specific exports (explicit re-export to avoid naming conflicts)\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/assets.ts":
/*!***********************************************!*\
  !*** ../../packages/shared/src/lib/assets.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetClassService: () => (/* binding */ AssetClassService),\n/* harmony export */   AssetService: () => (/* binding */ AssetService),\n/* harmony export */   HoldingService: () => (/* binding */ HoldingService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n\nclass AssetClassService {\n    /**\n   * Get all asset classes\n   */ static async getAssetClasses() {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('asset_classes').select('*').eq('is_active', true).order('name');\n        if (error) {\n            throw new Error(\"Failed to fetch asset classes: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get a specific asset class by ID\n   */ static async getAssetClass(id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('asset_classes').select('*').eq('id', id).single();\n        if (error) {\n            throw new Error(\"Failed to fetch asset class: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Create a new asset class\n   */ static async createAssetClass(assetClassData) {\n        const insertData = {\n            ...assetClassData,\n            is_active: true\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('asset_classes').insert(insertData).select('*').single();\n        if (error) {\n            throw new Error(\"Failed to create asset class: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update an existing asset class\n   */ static async updateAssetClass(id, updates) {\n        const updateData = {\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('asset_classes').update(updateData).eq('id', id).select('*').single();\n        if (error) {\n            throw new Error(\"Failed to update asset class: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Initialize default asset classes\n   */ static async initializeDefaultAssetClasses() {\n        const { DEFAULT_ASSET_CLASSES } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../types/assets */ \"(app-pages-browser)/../../packages/shared/src/types/assets.ts\"));\n        for (const assetClass of DEFAULT_ASSET_CLASSES){\n            try {\n                await this.createAssetClass(assetClass);\n            } catch (error) {\n                // Skip if already exists\n                console.log(\"Asset class \".concat(assetClass.name, \" may already exist\"));\n            }\n        }\n    }\n}\nclass AssetService {\n    /**\n   * Get all assets\n   */ static async getAssets(options) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('assets').select(\"\\n        *,\\n        asset_class:asset_classes(*)\\n      \").order('symbol');\n        if (options === null || options === void 0 ? void 0 : options.asset_class_id) {\n            query = query.eq('asset_class_id', options.asset_class_id);\n        }\n        if ((options === null || options === void 0 ? void 0 : options.is_active) !== undefined) {\n            query = query.eq('is_active', options.is_active);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch assets: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get a specific asset by ID\n   */ static async getAsset(id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('assets').select(\"\\n        *,\\n        asset_class:asset_classes(*)\\n      \").eq('id', id).single();\n        if (error) {\n            throw new Error(\"Failed to fetch asset: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get asset by symbol\n   */ static async getAssetBySymbol(symbol) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('assets').select(\"\\n        *,\\n        asset_class:asset_classes(*)\\n      \").eq('symbol', symbol.toUpperCase()).eq('is_active', true).single();\n        if (error && error.code !== 'PGRST116') {\n            throw new Error(\"Failed to fetch asset: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Create a new asset\n   */ static async createAsset(assetData) {\n        const insertData = {\n            ...assetData,\n            symbol: assetData.symbol.toUpperCase(),\n            is_active: true\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('assets').insert(insertData).select(\"\\n        *,\\n        asset_class:asset_classes(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to create asset: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update an existing asset\n   */ static async updateAsset(id, updates) {\n        const updateData = {\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        if (updates.symbol) {\n            updateData.symbol = updates.symbol.toUpperCase();\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('assets').update(updateData).eq('id', id).select(\"\\n        *,\\n        asset_class:asset_classes(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to update asset: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Search assets by symbol or name\n   */ static async searchAssets(query) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('assets').select(\"\\n        *,\\n        asset_class:asset_classes(*)\\n      \").or(\"symbol.ilike.%\".concat(query.toUpperCase(), \"%,name.ilike.%\").concat(query, \"%\")).eq('is_active', true).limit(10);\n        if (error) {\n            throw new Error(\"Failed to search assets: \".concat(error.message));\n        }\n        return data;\n    }\n}\nclass HoldingService {\n    /**\n   * Get all holdings for the current user\n   */ static async getHoldings(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('holdings').select(\"\\n        *,\\n        asset:assets(*,asset_class:asset_classes(*)),\\n        account:accounts(*)\\n      \").eq('user_id', user.id).gt('quantity', 0).order('created_at', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            query = query.eq('account_id', options.account_id);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch holdings: \".concat(error.message));\n        }\n        // Filter by asset class if specified\n        let holdings = data;\n        if (options === null || options === void 0 ? void 0 : options.asset_class_id) {\n            holdings = holdings.filter((h)=>{\n                var _h_asset;\n                return ((_h_asset = h.asset) === null || _h_asset === void 0 ? void 0 : _h_asset.asset_class_id) === options.asset_class_id;\n            });\n        }\n        return holdings;\n    }\n    /**\n   * Get holding for a specific asset in an account\n   */ static async getHolding(accountId, assetId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('holdings').select(\"\\n        *,\\n        asset:assets(*,asset_class:asset_classes(*)),\\n        account:accounts(*)\\n      \").eq('user_id', user.id).eq('account_id', accountId).eq('asset_id', assetId).single();\n        if (error && error.code !== 'PGRST116') {\n            throw new Error(\"Failed to fetch holding: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update or create holding after a transaction\n   */ static async updateHolding(accountId, assetId, transactionType, quantity, pricePerUnit) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const existingHolding = await this.getHolding(accountId, assetId);\n        if (transactionType === 'buy') {\n            if (existingHolding) {\n                // Update existing holding\n                const newQuantity = existingHolding.quantity + quantity;\n                const newTotalInvested = existingHolding.total_invested + quantity * pricePerUnit;\n                const newAverageCost = newTotalInvested / newQuantity;\n                const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('holdings').update({\n                    quantity: newQuantity,\n                    average_cost: newAverageCost,\n                    total_invested: newTotalInvested,\n                    last_updated: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                }).eq('id', existingHolding.id).select(\"\\n            *,\\n            asset:assets(*,asset_class:asset_classes(*)),\\n            account:accounts(*)\\n          \").single();\n                if (error) {\n                    throw new Error(\"Failed to update holding: \".concat(error.message));\n                }\n                return data;\n            } else {\n                // Create new holding\n                const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('holdings').insert({\n                    user_id: user.id,\n                    account_id: accountId,\n                    asset_id: assetId,\n                    quantity,\n                    average_cost: pricePerUnit,\n                    total_invested: quantity * pricePerUnit,\n                    last_updated: new Date().toISOString()\n                }).select(\"\\n            *,\\n            asset:assets(*,asset_class:asset_classes(*)),\\n            account:accounts(*)\\n          \").single();\n                if (error) {\n                    throw new Error(\"Failed to create holding: \".concat(error.message));\n                }\n                return data;\n            }\n        } else {\n            // Sell transaction\n            if (!existingHolding) {\n                throw new Error('Cannot sell asset that is not held');\n            }\n            if (existingHolding.quantity < quantity) {\n                throw new Error('Cannot sell more than current holding');\n            }\n            const newQuantity = existingHolding.quantity - quantity;\n            const soldValue = quantity * existingHolding.average_cost;\n            const newTotalInvested = existingHolding.total_invested - soldValue;\n            if (newQuantity === 0) {\n                // Delete holding if quantity becomes zero\n                const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('holdings').delete().eq('id', existingHolding.id);\n                if (error) {\n                    throw new Error(\"Failed to delete holding: \".concat(error.message));\n                }\n                return {\n                    ...existingHolding,\n                    quantity: 0,\n                    total_invested: 0\n                };\n            } else {\n                // Update holding\n                const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('holdings').update({\n                    quantity: newQuantity,\n                    total_invested: newTotalInvested,\n                    last_updated: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                }).eq('id', existingHolding.id).select(\"\\n            *,\\n            asset:assets(*,asset_class:asset_classes(*)),\\n            account:accounts(*)\\n          \").single();\n                if (error) {\n                    throw new Error(\"Failed to update holding: \".concat(error.message));\n                }\n                return data;\n            }\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/assets.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/investments.ts":
/*!****************************************************!*\
  !*** ../../packages/shared/src/lib/investments.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvestmentService: () => (/* binding */ InvestmentService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _tax_calculator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tax-calculator */ \"(app-pages-browser)/../../packages/shared/src/lib/tax-calculator.ts\");\n/* harmony import */ var _assets__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./assets */ \"(app-pages-browser)/../../packages/shared/src/lib/assets.ts\");\n/* harmony import */ var _profit_loss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./profit-loss */ \"(app-pages-browser)/../../packages/shared/src/lib/profit-loss.ts\");\n\n\n\n\nclass InvestmentService {\n    /**\n   * Create an investment transaction (buy/sell)\n   * This also creates a transfer from a funding account for buy transactions\n   */ static async createInvestmentTransaction(investmentData, fundingAccountId// Required for buy transactions\n    ) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Validate investment account exists and is an investment account\n        const { data: investmentAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', investmentData.account_id).eq('user_id', user.id).single();\n        if (!investmentAccount || investmentAccount.account_type !== 'investment') {\n            throw new Error('Invalid investment account specified');\n        }\n        // For buy transactions, validate funding account and check balance\n        if (investmentData.transaction_type === 'investment_buy') {\n            if (!fundingAccountId) {\n                throw new Error('Funding account required for investment purchases');\n            }\n            const { data: fundingAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', fundingAccountId).eq('user_id', user.id).single();\n            if (!fundingAccount) {\n                throw new Error('Invalid funding account specified');\n            }\n            const totalCost = investmentData.amount + (investmentData.fees || 0);\n            if (fundingAccount.current_balance < totalCost) {\n                throw new Error('Insufficient balance in funding account');\n            }\n        }\n        // Get investment category\n        const categoryName = investmentData.transaction_type === 'investment_buy' ? 'Investment Purchase' : 'Investment Sale';\n        const { data: category } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', categoryName).eq('is_system', true).single();\n        if (!category) {\n            throw new Error(\"\".concat(categoryName, \" category not found\"));\n        }\n        // Create the investment transaction\n        const transactionData = {\n            amount: investmentData.amount,\n            description: investmentData.description || \"\".concat(investmentData.transaction_type === 'investment_buy' ? 'Buy' : 'Sell', \" \").concat(investmentData.investment_quantity, \" shares of \").concat(investmentData.investment_symbol),\n            category_id: category.id,\n            account_id: investmentData.account_id,\n            transaction_type: investmentData.transaction_type,\n            transaction_date: investmentData.transaction_date,\n            transaction_status: 'completed',\n            fees: investmentData.fees || 0,\n            investment_symbol: investmentData.investment_symbol,\n            investment_quantity: investmentData.investment_quantity,\n            investment_price: investmentData.investment_price,\n            user_id: user.id\n        };\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \").single();\n        if (transactionError) {\n            throw new Error(\"Failed to create investment transaction: \".concat(transactionError.message));\n        }\n        // For buy transactions, create a transfer from funding account to investment account\n        if (investmentData.transaction_type === 'investment_buy' && fundingAccountId) {\n            const { TransferService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\"));\n            try {\n                await TransferService.createTransfer({\n                    amount: investmentData.amount + (investmentData.fees || 0),\n                    description: \"Investment purchase: \".concat(investmentData.investment_symbol),\n                    from_account_id: fundingAccountId,\n                    to_account_id: investmentData.account_id,\n                    transaction_date: investmentData.transaction_date,\n                    fees: 0\n                });\n            } catch (error) {\n                // If transfer fails, rollback the investment transaction\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', transaction.id);\n                throw new Error(\"Failed to create funding transfer: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n            }\n        }\n        return transaction;\n    }\n    /**\n   * Create investment transaction with enhanced asset tracking and tax calculations\n   */ static async createInvestmentTransactionWithAssets(investmentData, fundingAccountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Get asset information\n        const asset = await _assets__WEBPACK_IMPORTED_MODULE_2__.AssetService.getAsset(investmentData.asset_id);\n        if (!asset) {\n            throw new Error('Asset not found');\n        }\n        // For sell transactions, calculate tax implications\n        let taxCalculation = null;\n        let realizedGainLoss = null;\n        if (investmentData.transaction_type === 'investment_sell') {\n            // Get the holding to determine purchase details\n            const holding = await _assets__WEBPACK_IMPORTED_MODULE_2__.HoldingService.getHolding(investmentData.account_id, investmentData.asset_id);\n            if (!holding) {\n                throw new Error('No holding found for this asset');\n            }\n            if (holding.quantity < investmentData.investment_quantity) {\n                throw new Error('Cannot sell more than current holding');\n            }\n            // Calculate tax implications\n            taxCalculation = await _tax_calculator__WEBPACK_IMPORTED_MODULE_1__.TaxCalculatorService.calculateCapitalGainsTax({\n                asset_class_id: asset.asset_class_id,\n                purchase_date: holding.created_at,\n                sale_date: investmentData.transaction_date,\n                purchase_price: holding.average_cost,\n                sale_price: investmentData.investment_price,\n                quantity: investmentData.investment_quantity,\n                fees: investmentData.fees\n            });\n            // Calculate realized gain/loss\n            realizedGainLoss = await _profit_loss__WEBPACK_IMPORTED_MODULE_3__.ProfitLossService.calculateRealizedGainLoss(investmentData.asset_id, investmentData.investment_quantity, investmentData.investment_price, investmentData.transaction_date, holding.average_cost, holding.created_at);\n        }\n        // Create the investment transaction using the existing method\n        const transaction = await this.createInvestmentTransaction(investmentData, fundingAccountId);\n        // Update holdings\n        const holding = await _assets__WEBPACK_IMPORTED_MODULE_2__.HoldingService.updateHolding(investmentData.account_id, investmentData.asset_id, investmentData.transaction_type === 'investment_buy' ? 'buy' : 'sell', investmentData.investment_quantity, investmentData.investment_price);\n        return {\n            transaction,\n            holding,\n            taxCalculation,\n            realizedGainLoss\n        };\n    }\n    /**\n   * Get investment transactions for a user or specific account\n   */ static async getInvestmentTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \", {\n            count: 'exact'\n        }).eq('user_id', user.id).in('transaction_type', [\n            'investment_buy',\n            'investment_sell'\n        ]).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            query = query.eq('account_id', options.account_id);\n        }\n        if (options === null || options === void 0 ? void 0 : options.symbol) {\n            query = query.eq('investment_symbol', options.symbol);\n        }\n        if (options === null || options === void 0 ? void 0 : options.transaction_type) {\n            query = query.eq('transaction_type', options.transaction_type);\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 20) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch investment transactions: \".concat(error.message));\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    /**\n   * Get investment holdings for a user or specific account\n   */ static async getInvestmentHoldings(accountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_holdings').select(\"\\n        *,\\n        account:accounts(*)\\n      \").order('symbol');\n        if (accountId) {\n            query = query.eq('account_id', accountId);\n        } else {\n            // Filter by user's accounts\n            const { data: userAccounts } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('id').eq('user_id', user.id).eq('account_type', 'investment');\n            if (userAccounts && userAccounts.length > 0) {\n                const accountIds = userAccounts.map((acc)=>acc.id);\n                query = query.in('account_id', accountIds);\n            } else {\n                return [];\n            }\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch investment holdings: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get portfolio summary for all investment accounts\n   */ static async getPortfolioSummary() {\n        const holdings = await this.getInvestmentHoldings();\n        let totalValue = 0;\n        let totalCost = 0;\n        const holdingsBySymbol = {};\n        holdings.forEach((holding)=>{\n            const symbol = holding.symbol;\n            const quantity = holding.quantity;\n            const avgCost = holding.average_cost;\n            const currentPrice = holding.current_price || avgCost;\n            const marketValue = quantity * currentPrice;\n            const costBasis = quantity * avgCost;\n            const gainLoss = marketValue - costBasis;\n            const gainLossPercent = costBasis > 0 ? gainLoss / costBasis * 100 : 0;\n            if (!holdingsBySymbol[symbol]) {\n                holdingsBySymbol[symbol] = {\n                    symbol,\n                    totalQuantity: 0,\n                    averageCost: 0,\n                    currentPrice,\n                    marketValue: 0,\n                    gainLoss: 0,\n                    gainLossPercent: 0\n                };\n            }\n            // Aggregate holdings for the same symbol across accounts\n            const existing = holdingsBySymbol[symbol];\n            const newTotalQuantity = existing.totalQuantity + quantity;\n            const newTotalCost = existing.totalQuantity * existing.averageCost + quantity * avgCost;\n            holdingsBySymbol[symbol] = {\n                ...existing,\n                totalQuantity: newTotalQuantity,\n                averageCost: newTotalQuantity > 0 ? newTotalCost / newTotalQuantity : 0,\n                marketValue: existing.marketValue + marketValue,\n                gainLoss: existing.gainLoss + gainLoss\n            };\n            // Recalculate percentage\n            const totalCostBasis = holdingsBySymbol[symbol].totalQuantity * holdingsBySymbol[symbol].averageCost;\n            holdingsBySymbol[symbol].gainLossPercent = totalCostBasis > 0 ? holdingsBySymbol[symbol].gainLoss / totalCostBasis * 100 : 0;\n            totalValue += marketValue;\n            totalCost += costBasis;\n        });\n        const totalGainLoss = totalValue - totalCost;\n        const totalGainLossPercent = totalCost > 0 ? totalGainLoss / totalCost * 100 : 0;\n        return {\n            totalValue,\n            totalCost,\n            totalGainLoss,\n            totalGainLossPercent,\n            holdingsBySymbol\n        };\n    }\n    /**\n   * Update current prices for holdings (would typically be called by a background job)\n   */ static async updateHoldingPrices(priceUpdates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        for (const update of priceUpdates){\n            const marketValue = update.price // Will be calculated by trigger\n            ;\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_holdings').update({\n                current_price: update.price,\n                market_value: marketValue,\n                last_updated: new Date().toISOString()\n            }).eq('symbol', update.symbol);\n            if (error) {\n                console.error(\"Failed to update price for \".concat(update.symbol, \":\"), error);\n            }\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/investments.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/profit-loss.ts":
/*!****************************************************!*\
  !*** ../../packages/shared/src/lib/profit-loss.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfitLossService: () => (/* binding */ ProfitLossService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./assets */ \"(app-pages-browser)/../../packages/shared/src/lib/assets.ts\");\n\n\nclass ProfitLossService {\n    /**\n   * Calculate realized gain/loss for a sell transaction\n   */ static async calculateRealizedGainLoss(assetId, quantitySold, salePrice, saleDate, averageCost, purchaseDate) {\n        // Get asset information\n        const { AssetService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./assets */ \"(app-pages-browser)/../../packages/shared/src/lib/assets.ts\"));\n        const asset = await AssetService.getAsset(assetId);\n        if (!asset.asset_class) {\n            throw new Error('Asset class information not found');\n        }\n        // Calculate holding period\n        const purchaseDateObj = new Date(purchaseDate);\n        const saleDateObj = new Date(saleDate);\n        const holdingPeriodDays = Math.floor((saleDateObj.getTime() - purchaseDateObj.getTime()) / (1000 * 60 * 60 * 24));\n        const holdingPeriodMonths = Math.floor(holdingPeriodDays / 30.44) // Average days per month\n        ;\n        // Determine if it's long-term based on asset class\n        const isLongTerm = holdingPeriodMonths >= asset.asset_class.ltcg_period_months;\n        // Calculate gross gain/loss\n        const grossGainLoss = (salePrice - averageCost) * quantitySold;\n        // Determine applicable tax rate\n        let applicableTaxRate = 0;\n        if (grossGainLoss > 0) {\n            applicableTaxRate = isLongTerm ? asset.asset_class.ltcg_tax_rate || 0 : asset.asset_class.stcg_tax_rate || 0;\n        }\n        // Calculate tax amount\n        const taxAmount = Math.max(0, grossGainLoss * (applicableTaxRate / 100));\n        // Calculate net gain/loss\n        const netGainLoss = grossGainLoss - taxAmount;\n        return {\n            transaction_id: '',\n            asset_id: assetId,\n            asset_symbol: asset.symbol,\n            asset_name: asset.name,\n            quantity_sold: quantitySold,\n            purchase_price: averageCost,\n            sale_price: salePrice,\n            purchase_date: purchaseDate,\n            sale_date: saleDate,\n            holding_period_days: holdingPeriodDays,\n            holding_period_months: holdingPeriodMonths,\n            is_long_term: isLongTerm,\n            gross_gain_loss: grossGainLoss,\n            applicable_tax_rate: applicableTaxRate,\n            tax_amount: taxAmount,\n            net_gain_loss: netGainLoss,\n            asset_class: asset.asset_class\n        };\n    }\n    /**\n   * Calculate unrealized gain/loss for current holdings\n   */ static async calculateUnrealizedGainLoss(holdings) {\n        const unrealizedGainLoss = [];\n        for (const holding of holdings){\n            if (!holding.asset || !holding.asset.asset_class) {\n                continue;\n            }\n            // Get current price (for now, use the stored current_price or average_cost as fallback)\n            const currentPrice = holding.asset.current_price || holding.average_cost;\n            // Calculate values\n            const currentValue = holding.quantity * currentPrice;\n            const unrealizedGain = currentValue - holding.total_invested;\n            const unrealizedPercentage = holding.total_invested > 0 ? unrealizedGain / holding.total_invested * 100 : 0;\n            // Calculate holding period\n            const purchaseDate = new Date(holding.created_at);\n            const currentDate = new Date();\n            const daysHeld = Math.floor((currentDate.getTime() - purchaseDate.getTime()) / (1000 * 60 * 60 * 24));\n            const monthsHeld = Math.floor(daysHeld / 30.44);\n            unrealizedGainLoss.push({\n                holding_id: holding.id,\n                asset_id: holding.asset.id,\n                asset_symbol: holding.asset.symbol,\n                asset_name: holding.asset.name,\n                quantity: holding.quantity,\n                average_cost: holding.average_cost,\n                current_price: currentPrice,\n                total_invested: holding.total_invested,\n                current_value: currentValue,\n                unrealized_gain_loss: unrealizedGain,\n                unrealized_percentage: unrealizedPercentage,\n                days_held: daysHeld,\n                months_held: monthsHeld,\n                asset_class: holding.asset.asset_class\n            });\n        }\n        return unrealizedGainLoss;\n    }\n    /**\n   * Get portfolio summary with realized and unrealized gains\n   */ static async getPortfolioSummary(accountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Get current holdings\n        const holdings = await _assets__WEBPACK_IMPORTED_MODULE_1__.HoldingService.getHoldings(accountId ? {\n            account_id: accountId\n        } : undefined);\n        // Calculate unrealized gains\n        const unrealizedGains = await this.calculateUnrealizedGainLoss(holdings);\n        // Get realized gains from completed transactions\n        let realizedQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_transactions').select(\"\\n        *,\\n        asset:assets(*,asset_class:asset_classes(*))\\n      \").eq('user_id', user.id).eq('transaction_type', 'investment_sell');\n        if (accountId) {\n            realizedQuery = realizedQuery.eq('account_id', accountId);\n        }\n        const { data: sellTransactions, error } = await realizedQuery;\n        if (error) {\n            throw new Error(\"Failed to fetch sell transactions: \".concat(error.message));\n        }\n        // Calculate totals\n        const totalInvested = holdings.reduce((sum, h)=>sum + h.total_invested, 0);\n        const currentValue = unrealizedGains.reduce((sum, u)=>sum + u.current_value, 0);\n        const totalUnrealizedGainLoss = unrealizedGains.reduce((sum, u)=>sum + u.unrealized_gain_loss, 0);\n        const totalUnrealizedPercentage = totalInvested > 0 ? totalUnrealizedGainLoss / totalInvested * 100 : 0;\n        // Calculate realized gains (simplified - would need more complex logic for FIFO/LIFO)\n        const totalRealizedGainLoss = (sellTransactions || []).reduce((sum, t)=>{\n            // This is a simplified calculation - in reality, you'd need to track specific lots\n            return sum + ((t.investment_price || 0) - (t.average_cost || 0)) * (t.investment_quantity || 0);\n        }, 0);\n        // Calculate asset class breakdown\n        const assetClassesBreakdown = {};\n        unrealizedGains.forEach((u)=>{\n            const className = u.asset_class.name;\n            if (!assetClassesBreakdown[className]) {\n                assetClassesBreakdown[className] = {\n                    invested: 0,\n                    current_value: 0,\n                    gain_loss: 0,\n                    percentage: 0\n                };\n            }\n            assetClassesBreakdown[className].invested += u.total_invested;\n            assetClassesBreakdown[className].current_value += u.current_value;\n            assetClassesBreakdown[className].gain_loss += u.unrealized_gain_loss;\n        });\n        // Calculate percentages for each asset class\n        Object.keys(assetClassesBreakdown).forEach((className)=>{\n            const breakdown = assetClassesBreakdown[className];\n            breakdown.percentage = breakdown.invested > 0 ? breakdown.gain_loss / breakdown.invested * 100 : 0;\n        });\n        return {\n            total_invested: totalInvested,\n            current_value: currentValue,\n            total_unrealized_gain_loss: totalUnrealizedGainLoss,\n            total_unrealized_percentage: totalUnrealizedPercentage,\n            total_realized_gain_loss: totalRealizedGainLoss,\n            total_tax_paid: 0,\n            net_portfolio_gain_loss: totalUnrealizedGainLoss + totalRealizedGainLoss,\n            holdings_count: holdings.length,\n            asset_classes_breakdown: assetClassesBreakdown\n        };\n    }\n    /**\n   * Get detailed profit/loss report\n   */ static async getProfitLossReport(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Get holdings for unrealized gains\n        const holdings = await _assets__WEBPACK_IMPORTED_MODULE_1__.HoldingService.getHoldings((options === null || options === void 0 ? void 0 : options.account_id) ? {\n            account_id: options.account_id\n        } : undefined);\n        const unrealizedGains = await this.calculateUnrealizedGainLoss(holdings);\n        // Get realized gains from sell transactions\n        let realizedQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_transactions').select(\"\\n        *,\\n        asset:assets(*,asset_class:asset_classes(*))\\n      \").eq('user_id', user.id).eq('transaction_type', 'investment_sell');\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            realizedQuery = realizedQuery.eq('account_id', options.account_id);\n        }\n        if (options === null || options === void 0 ? void 0 : options.start_date) {\n            realizedQuery = realizedQuery.gte('transaction_date', options.start_date);\n        }\n        if (options === null || options === void 0 ? void 0 : options.end_date) {\n            realizedQuery = realizedQuery.lte('transaction_date', options.end_date);\n        }\n        const { data: sellTransactions, error } = await realizedQuery;\n        if (error) {\n            throw new Error(\"Failed to fetch transactions: \".concat(error.message));\n        }\n        // Filter by asset class if specified\n        const filteredUnrealized = (options === null || options === void 0 ? void 0 : options.asset_class_id) ? unrealizedGains.filter((u)=>u.asset_class.id === options.asset_class_id) : unrealizedGains;\n        const filteredRealized = (options === null || options === void 0 ? void 0 : options.asset_class_id) ? (sellTransactions || []).filter((t)=>{\n            var _t_asset;\n            return ((_t_asset = t.asset) === null || _t_asset === void 0 ? void 0 : _t_asset.asset_class_id) === options.asset_class_id;\n        }) : sellTransactions || [];\n        return {\n            unrealized_gains: filteredUnrealized,\n            realized_transactions: filteredRealized,\n            summary: await this.getPortfolioSummary(options === null || options === void 0 ? void 0 : options.account_id)\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/profit-loss.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/tax-calculator.ts":
/*!*******************************************************!*\
  !*** ../../packages/shared/src/lib/tax-calculator.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TaxCalculatorService: () => (/* binding */ TaxCalculatorService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./assets */ \"(app-pages-browser)/../../packages/shared/src/lib/assets.ts\");\n\n\nclass TaxCalculatorService {\n    /**\n   * Calculate capital gains tax for a transaction\n   */ static async calculateCapitalGainsTax(input) {\n        // Get asset class information\n        const assetClass = await _assets__WEBPACK_IMPORTED_MODULE_1__.AssetClassService.getAssetClass(input.asset_class_id);\n        // Calculate holding period\n        const purchaseDate = new Date(input.purchase_date);\n        const saleDate = new Date(input.sale_date);\n        const holdingPeriodDays = Math.floor((saleDate.getTime() - purchaseDate.getTime()) / (1000 * 60 * 60 * 24));\n        const holdingPeriodMonths = Math.floor(holdingPeriodDays / 30.44);\n        // Determine if it's long-term\n        const isLongTerm = holdingPeriodMonths >= assetClass.ltcg_period_months;\n        // Calculate basic values\n        const purchaseValue = input.purchase_price * input.quantity;\n        const saleValue = input.sale_price * input.quantity;\n        const totalFees = input.fees || 0;\n        const grossCapitalGainLoss = saleValue - purchaseValue - totalFees;\n        // Calculate indexation benefit for debt instruments (if applicable)\n        let indexationBenefit = 0;\n        let indexedCost = purchaseValue;\n        if (isLongTerm && assetClass.tax_treatment === 'debt') {\n            // Simplified indexation calculation\n            // In reality, this would use Cost Inflation Index (CII) from Income Tax Department\n            const inflationRate = 0.04 // 4% annual inflation assumption\n            ;\n            const years = holdingPeriodDays / 365.25;\n            indexationBenefit = purchaseValue * (Math.pow(1 + inflationRate, years) - 1);\n            indexedCost = purchaseValue + indexationBenefit;\n        }\n        // Calculate taxable capital gain/loss\n        const taxableCapitalGainLoss = isLongTerm && assetClass.tax_treatment === 'debt' ? saleValue - indexedCost - totalFees : grossCapitalGainLoss;\n        // Determine applicable tax rate\n        let applicableTaxRate = 0;\n        if (taxableCapitalGainLoss > 0) {\n            applicableTaxRate = isLongTerm ? assetClass.ltcg_tax_rate || 0 : assetClass.stcg_tax_rate || 0;\n        }\n        // Apply exemption for LTCG on equity (₹1 lakh exemption)\n        let exemptionApplied = 0;\n        let taxableAmount = Math.max(0, taxableCapitalGainLoss);\n        if (isLongTerm && assetClass.tax_treatment === 'equity' && taxableAmount > 0) {\n            const exemptionLimit = 100000 // ₹1 lakh\n            ;\n            exemptionApplied = Math.min(taxableAmount, exemptionLimit);\n            taxableAmount = Math.max(0, taxableAmount - exemptionApplied);\n        }\n        // Calculate tax amount\n        const taxAmount = taxableAmount * (applicableTaxRate / 100);\n        // Calculate cess (4% on tax amount for income above certain threshold)\n        const cessRate = 0.04 // 4% cess\n        ;\n        const cessAmount = taxAmount * cessRate;\n        // Total tax liability\n        const totalTaxLiability = taxAmount + cessAmount;\n        // Net proceeds after tax\n        const netProceeds = saleValue - totalFees - totalTaxLiability;\n        // Effective tax rate\n        const effectiveTaxRate = grossCapitalGainLoss > 0 ? totalTaxLiability / grossCapitalGainLoss * 100 : 0;\n        return {\n            holding_period_days: holdingPeriodDays,\n            holding_period_months: holdingPeriodMonths,\n            is_long_term: isLongTerm,\n            purchase_value: purchaseValue,\n            sale_value: saleValue,\n            total_fees: totalFees,\n            gross_capital_gain_loss: grossCapitalGainLoss,\n            indexation_benefit: indexationBenefit > 0 ? indexationBenefit : undefined,\n            indexed_cost: indexationBenefit > 0 ? indexedCost : undefined,\n            taxable_capital_gain_loss: taxableCapitalGainLoss,\n            applicable_tax_rate: applicableTaxRate,\n            tax_amount: taxAmount,\n            cess_amount: cessAmount,\n            total_tax_liability: totalTaxLiability,\n            net_proceeds: netProceeds,\n            effective_tax_rate: effectiveTaxRate,\n            asset_class: assetClass\n        };\n    }\n    /**\n   * Get tax summary for a financial year\n   */ static async getTaxSummary(financialYear, accountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Financial year in India runs from April 1 to March 31\n        const startDate = \"\".concat(financialYear, \"-04-01\");\n        const endDate = \"\".concat(parseInt(financialYear) + 1, \"-03-31\");\n        // Get all sell transactions for the financial year\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_transactions').select(\"\\n        *,\\n        asset:assets(*,asset_class:asset_classes(*))\\n      \").eq('user_id', user.id).eq('transaction_type', 'investment_sell').gte('transaction_date', startDate).lte('transaction_date', endDate);\n        if (accountId) {\n            query = query.eq('account_id', accountId);\n        }\n        const { data: sellTransactions, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch transactions: \".concat(error.message));\n        }\n        let totalSTCG = 0;\n        let totalLTCG = 0;\n        let totalSTCGTax = 0;\n        let totalLTCGTax = 0;\n        let totalTaxLiability = 0;\n        let exemptionUsed = 0;\n        // Calculate tax for each transaction\n        for (const transaction of sellTransactions || []){\n            var _transaction_asset;\n            if (!((_transaction_asset = transaction.asset) === null || _transaction_asset === void 0 ? void 0 : _transaction_asset.asset_class)) continue;\n            // Get corresponding buy transaction (simplified - assumes FIFO)\n            const { data: buyTransaction } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_transactions').select('*').eq('user_id', user.id).eq('account_id', transaction.account_id).eq('asset_id', transaction.asset_id).eq('transaction_type', 'investment_buy').lt('transaction_date', transaction.transaction_date).order('transaction_date', {\n                ascending: true\n            }).limit(1).single();\n            if (!buyTransaction) continue;\n            const taxCalculation = await this.calculateCapitalGainsTax({\n                asset_class_id: transaction.asset.asset_class_id,\n                purchase_date: buyTransaction.transaction_date,\n                sale_date: transaction.transaction_date,\n                purchase_price: buyTransaction.investment_price || 0,\n                sale_price: transaction.investment_price || 0,\n                quantity: transaction.investment_quantity || 0,\n                fees: (buyTransaction.fees || 0) + (transaction.fees || 0)\n            });\n            if (taxCalculation.is_long_term) {\n                totalLTCG += taxCalculation.taxable_capital_gain_loss;\n                totalLTCGTax += taxCalculation.total_tax_liability;\n            } else {\n                totalSTCG += taxCalculation.taxable_capital_gain_loss;\n                totalSTCGTax += taxCalculation.total_tax_liability;\n            }\n            totalTaxLiability += taxCalculation.total_tax_liability;\n            // Track exemption usage for equity LTCG\n            if (taxCalculation.is_long_term && taxCalculation.asset_class.tax_treatment === 'equity' && taxCalculation.taxable_capital_gain_loss > 0) {\n                exemptionUsed += Math.min(100000, taxCalculation.taxable_capital_gain_loss);\n            }\n        }\n        // Calculate available exemption\n        const exemptionAvailable = Math.max(0, 100000 - exemptionUsed);\n        return {\n            financial_year: financialYear,\n            total_stcg: totalSTCG,\n            total_ltcg: totalLTCG,\n            total_stcg_tax: totalSTCGTax,\n            total_ltcg_tax: totalLTCGTax,\n            total_tax_liability: totalTaxLiability,\n            exemption_used: exemptionUsed,\n            exemption_available: exemptionAvailable,\n            transactions_count: (sellTransactions === null || sellTransactions === void 0 ? void 0 : sellTransactions.length) || 0\n        };\n    }\n    /**\n   * Get detailed tax report for multiple financial years\n   */ static async getTaxReport(options) {\n        const currentYear = new Date().getFullYear();\n        const startYear = (options === null || options === void 0 ? void 0 : options.start_year) ? parseInt(options.start_year) : currentYear - 2;\n        const endYear = (options === null || options === void 0 ? void 0 : options.end_year) ? parseInt(options.end_year) : currentYear;\n        const taxSummaries = [];\n        for(let year = startYear; year <= endYear; year++){\n            const summary = await this.getTaxSummary(year.toString(), options === null || options === void 0 ? void 0 : options.account_id);\n            taxSummaries.push(summary);\n        }\n        // Calculate totals across all years\n        const totalSTCG = taxSummaries.reduce((sum, s)=>sum + s.total_stcg, 0);\n        const totalLTCG = taxSummaries.reduce((sum, s)=>sum + s.total_ltcg, 0);\n        const totalTax = taxSummaries.reduce((sum, s)=>sum + s.total_tax_liability, 0);\n        const totalTransactions = taxSummaries.reduce((sum, s)=>sum + s.transactions_count, 0);\n        return {\n            yearly_summaries: taxSummaries,\n            overall_summary: {\n                total_stcg: totalSTCG,\n                total_ltcg: totalLTCG,\n                total_tax_liability: totalTax,\n                total_transactions: totalTransactions,\n                average_tax_rate: totalSTCG + totalLTCG > 0 ? totalTax / (totalSTCG + totalLTCG) * 100 : 0\n            }\n        };\n    }\n    /**\n   * Get current financial year\n   */ static getCurrentFinancialYear() {\n        const now = new Date();\n        const currentYear = now.getFullYear();\n        const currentMonth = now.getMonth() + 1 // JavaScript months are 0-indexed\n        ;\n        // Financial year in India starts from April\n        if (currentMonth >= 4) {\n            return currentYear.toString();\n        } else {\n            return (currentYear - 1).toString();\n        }\n    }\n    /**\n   * Validate asset class tax configuration\n   */ static validateAssetClassTaxConfig(assetClass) {\n        const errors = [];\n        if (!assetClass.ltcg_period_months || assetClass.ltcg_period_months < 0) {\n            errors.push('LTCG period must be specified and non-negative');\n        }\n        if (assetClass.tax_treatment === 'equity') {\n            if (!assetClass.ltcg_tax_rate && assetClass.ltcg_tax_rate !== 0) {\n                errors.push('LTCG tax rate must be specified for equity assets');\n            }\n            if (!assetClass.stcg_tax_rate && assetClass.stcg_tax_rate !== 0) {\n                errors.push('STCG tax rate must be specified for equity assets');\n            }\n        }\n        if (assetClass.tax_treatment === 'debt') {\n            if (!assetClass.ltcg_tax_rate && assetClass.ltcg_tax_rate !== 0) {\n                errors.push('LTCG tax rate must be specified for debt assets');\n            }\n            if (!assetClass.stcg_tax_rate && assetClass.stcg_tax_rate !== 0) {\n                errors.push('STCG tax rate must be specified for debt assets');\n            }\n        }\n        return errors;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/tax-calculator.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/transactions.ts":
/*!*****************************************************!*\
  !*** ../../packages/shared/src/lib/transactions.ts ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionService: () => (/* binding */ TransactionService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _investments__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./investments */ \"(app-pages-browser)/../../packages/shared/src/lib/investments.ts\");\n\n\nclass TransactionService {\n    /**\n   * Create a transaction of any type\n   */ static async createTransaction(data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Handle different transaction types\n        switch(data.transaction_type){\n            case 'transfer':\n                if (!data.account_id || !data.to_account_id) {\n                    throw new Error('Source and destination accounts are required for transfers');\n                }\n                const { TransferService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\"));\n                const transfer = await TransferService.createTransfer({\n                    amount: data.amount,\n                    description: data.description,\n                    from_account_id: data.account_id,\n                    to_account_id: data.to_account_id,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees\n                });\n                // Return the outgoing transaction as the primary transaction\n                const { data: transferTransactions } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n            *,\\n            category:categories(*),\\n            account:accounts(*),\\n            to_account:accounts!transactions_to_account_id_fkey(*)\\n          \").eq('transfer_id', transfer.transfer_id).eq('account_id', data.account_id).single();\n                return transferTransactions;\n            case 'investment_buy':\n            case 'investment_sell':\n                if (!data.account_id || !data.investment_symbol || !data.investment_quantity || !data.investment_price) {\n                    throw new Error('Investment account, symbol, quantity, and price are required for investment transactions');\n                }\n                const investment = await _investments__WEBPACK_IMPORTED_MODULE_1__.InvestmentService.createInvestmentTransaction({\n                    amount: data.amount,\n                    description: data.description,\n                    account_id: data.account_id,\n                    investment_symbol: data.investment_symbol,\n                    investment_quantity: data.investment_quantity,\n                    investment_price: data.investment_price,\n                    transaction_type: data.transaction_type,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees\n                }, data.funding_account_id);\n                return investment;\n            case 'dividend':\n                // Handle dividend as a transfer from investment account to bank account\n                if (!data.account_id || !data.to_account_id) {\n                    throw new Error('Investment account and receiving account are required for dividend transactions');\n                }\n                const dividendTransfer = await TransferService.createTransfer({\n                    amount: data.amount,\n                    description: data.description || \"Dividend payment: \".concat(data.investment_symbol || 'Investment'),\n                    from_account_id: data.account_id,\n                    to_account_id: data.to_account_id,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees || 0\n                });\n                return dividendTransfer;\n            case 'income':\n            case 'expense':\n                if (!data.category_id || !data.account_id) {\n                    throw new Error('Category and account are required for income/expense transactions');\n                }\n                const transactionData = {\n                    amount: data.amount,\n                    description: data.description || null,\n                    category_id: data.category_id,\n                    account_id: data.account_id,\n                    transaction_type: data.transaction_type,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    transaction_status: 'completed',\n                    fees: data.fees || 0,\n                    user_id: user.id\n                };\n                const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(\"\\n            *,\\n            category:categories(*),\\n            account:accounts(*)\\n          \").single();\n                if (error) {\n                    throw new Error(\"Failed to create transaction: \".concat(error.message));\n                }\n                return transaction;\n            default:\n                throw new Error(\"Unsupported transaction type: \".concat(data.transaction_type));\n        }\n    }\n    /**\n   * Get all transactions for the current user with enhanced filtering\n   */ static async getTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \", {\n            count: 'exact'\n        }).eq('user_id', user.id).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        // Apply filters\n        if (options === null || options === void 0 ? void 0 : options.categoryId) {\n            query = query.eq('category_id', options.categoryId);\n        }\n        if (options === null || options === void 0 ? void 0 : options.accountId) {\n            query = query.or(\"account_id.eq.\".concat(options.accountId, \",to_account_id.eq.\").concat(options.accountId));\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.transactionType) {\n            if (Array.isArray(options.transactionType)) {\n                query = query.in('transaction_type', options.transactionType);\n            } else {\n                query = query.eq('transaction_type', options.transactionType);\n            }\n        }\n        if (options === null || options === void 0 ? void 0 : options.transactionStatus) {\n            query = query.eq('transaction_status', options.transactionStatus);\n        }\n        // Filter out transfers and investments if not explicitly requested\n        if (!(options === null || options === void 0 ? void 0 : options.includeTransfers) && !(options === null || options === void 0 ? void 0 : options.includeInvestments)) {\n            query = query.in('transaction_type', [\n                'income',\n                'expense',\n                'dividend'\n            ]);\n        } else if (!(options === null || options === void 0 ? void 0 : options.includeTransfers)) {\n            query = query.neq('transaction_type', 'transfer');\n        } else if (!(options === null || options === void 0 ? void 0 : options.includeInvestments)) {\n            query = query.not('transaction_type', 'in', '(investment_buy,investment_sell)');\n        }\n        if (options === null || options === void 0 ? void 0 : options.searchQuery) {\n            // Search in description, category name, and investment symbol\n            query = query.or(\"description.ilike.%\".concat(options.searchQuery, \"%,\") + \"investment_symbol.ilike.%\".concat(options.searchQuery, \"%\"));\n        }\n        // Apply pagination\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch transactions: \".concat(error.message));\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    /**\n   * Get a specific transaction by ID\n   */ static async getTransaction(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            throw new Error(\"Failed to fetch transaction: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update a transaction (limited to basic transactions, not transfers or investments)\n   */ static async updateTransaction(id, updates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // First check if this is a basic transaction (not transfer or investment)\n        const { data: existingTransaction } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, transfer_id').eq('id', id).eq('user_id', user.id).single();\n        if (!existingTransaction) {\n            throw new Error('Transaction not found');\n        }\n        if (existingTransaction.transaction_type === 'transfer' || existingTransaction.transaction_type === 'investment_buy' || existingTransaction.transaction_type === 'investment_sell') {\n            throw new Error('Cannot update transfer or investment transactions through this method');\n        }\n        const updateData = {\n            updated_at: new Date().toISOString()\n        };\n        if (updates.amount !== undefined) updateData.amount = updates.amount;\n        if (updates.description !== undefined) updateData.description = updates.description;\n        if (updates.category_id !== undefined) updateData.category_id = updates.category_id;\n        if (updates.fees !== undefined) updateData.fees = updates.fees;\n        if (updates.transaction_date !== undefined) {\n            updateData.transaction_date = updates.transaction_date.toISOString().split('T')[0];\n        }\n        const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').update(updateData).eq('id', id).eq('user_id', user.id).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to update transaction: \".concat(error.message));\n        }\n        return transaction;\n    }\n    /**\n   * Delete a transaction\n   */ static async deleteTransaction(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if this is a transfer transaction\n        const { data: transaction } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, transfer_id').eq('id', id).eq('user_id', user.id).single();\n        if (!transaction) {\n            throw new Error('Transaction not found');\n        }\n        if (transaction.transaction_type === 'transfer' && transaction.transfer_id) {\n            // Delete all transactions with the same transfer_id\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('transfer_id', transaction.transfer_id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete transfer: \".concat(error.message));\n            }\n        } else {\n            // Delete single transaction\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete transaction: \".concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get transaction summary for a date range\n   */ static async getTransactionSummary(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, amount').eq('user_id', user.id).eq('transaction_status', 'completed');\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.accountId) {\n            query = query.or(\"account_id.eq.\".concat(options.accountId, \",to_account_id.eq.\").concat(options.accountId));\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch transaction summary: \".concat(error.message));\n        }\n        const summary = {\n            totalIncome: 0,\n            totalExpenses: 0,\n            totalTransfers: 0,\n            totalInvestments: 0,\n            netFlow: 0,\n            transactionCount: (data === null || data === void 0 ? void 0 : data.length) || 0\n        };\n        data === null || data === void 0 ? void 0 : data.forEach((transaction)=>{\n            switch(transaction.transaction_type){\n                case 'income':\n                case 'dividend':\n                    summary.totalIncome += transaction.amount;\n                    break;\n                case 'expense':\n                    summary.totalExpenses += transaction.amount;\n                    break;\n                case 'transfer':\n                    summary.totalTransfers += transaction.amount;\n                    break;\n                case 'investment_buy':\n                case 'investment_sell':\n                    summary.totalInvestments += transaction.amount;\n                    break;\n            }\n        });\n        summary.netFlow = summary.totalIncome - summary.totalExpenses;\n        return summary;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/transactions.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/schemas/assets.ts":
/*!***************************************************!*\
  !*** ../../packages/shared/src/schemas/assets.ts ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assetClassEnum: () => (/* binding */ assetClassEnum),\n/* harmony export */   assetClassFormInputSchema: () => (/* binding */ assetClassFormInputSchema),\n/* harmony export */   assetClassFormSchema: () => (/* binding */ assetClassFormSchema),\n/* harmony export */   assetClassSchema: () => (/* binding */ assetClassSchema),\n/* harmony export */   assetFormInputSchema: () => (/* binding */ assetFormInputSchema),\n/* harmony export */   assetFormSchema: () => (/* binding */ assetFormSchema),\n/* harmony export */   assetSchema: () => (/* binding */ assetSchema),\n/* harmony export */   assetSubClassEnum: () => (/* binding */ assetSubClassEnum),\n/* harmony export */   holdingSchema: () => (/* binding */ holdingSchema),\n/* harmony export */   investmentTransactionWithAssetSchema: () => (/* binding */ investmentTransactionWithAssetSchema),\n/* harmony export */   taxCalculationResultSchema: () => (/* binding */ taxCalculationResultSchema),\n/* harmony export */   taxCalculationSchema: () => (/* binding */ taxCalculationSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/../../node_modules/zod/dist/esm/index.js\");\n\n// Asset class enums\nconst assetClassEnum = zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n    'stocks',\n    'bonds',\n    'mutual_funds',\n    'etfs',\n    'real_estate',\n    'commodities',\n    'crypto',\n    'cash',\n    'other'\n]);\nconst assetSubClassEnum = zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n    // Stock subcategories\n    'large_cap',\n    'mid_cap',\n    'small_cap',\n    'international',\n    'emerging_markets',\n    // Bond subcategories\n    'government_bonds',\n    'corporate_bonds',\n    'municipal_bonds',\n    'treasury_bills',\n    'high_yield_bonds',\n    // Mutual fund subcategories\n    'equity_funds',\n    'debt_funds',\n    'hybrid_funds',\n    'index_funds',\n    'sector_funds',\n    // ETF subcategories\n    'equity_etfs',\n    'bond_etfs',\n    'commodity_etfs',\n    'international_etfs',\n    // Real estate subcategories\n    'reits',\n    'real_estate_funds',\n    // Commodity subcategories\n    'precious_metals',\n    'energy',\n    'agriculture',\n    // Crypto subcategories\n    'bitcoin',\n    'altcoins',\n    'stablecoins'\n]);\n// Asset class schema\nconst assetClassSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid(),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Asset class name is required'),\n    class: assetClassEnum,\n    sub_class: assetSubClassEnum.optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    risk_level: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'low',\n        'medium',\n        'high',\n        'very_high'\n    ]),\n    liquidity: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'high',\n        'medium',\n        'low'\n    ]),\n    typical_holding_period: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'short_term',\n        'medium_term',\n        'long_term'\n    ]),\n    tax_treatment: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'equity',\n        'debt',\n        'other'\n    ]),\n    ltcg_period_months: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0, 'LTCG period must be non-negative'),\n    ltcg_tax_rate: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0).max(100).optional(),\n    stcg_tax_rate: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0).max(100).optional(),\n    dividend_tax_rate: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0).max(100).optional(),\n    created_at: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    updated_at: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n});\n// Asset class form schemas\nconst assetClassFormInputSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Asset class name is required'),\n    class: assetClassEnum,\n    sub_class: assetSubClassEnum.optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    risk_level: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'low',\n        'medium',\n        'high',\n        'very_high'\n    ]),\n    liquidity: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'high',\n        'medium',\n        'low'\n    ]),\n    typical_holding_period: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'short_term',\n        'medium_term',\n        'long_term'\n    ]),\n    tax_treatment: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'equity',\n        'debt',\n        'other'\n    ]),\n    ltcg_period_months: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'LTCG period is required'),\n    ltcg_tax_rate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    stcg_tax_rate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    dividend_tax_rate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n});\nconst assetClassFormSchema = assetClassFormInputSchema.transform((data)=>({\n        ...data,\n        ltcg_period_months: parseInt(data.ltcg_period_months),\n        ltcg_tax_rate: data.ltcg_tax_rate ? parseFloat(data.ltcg_tax_rate) : undefined,\n        stcg_tax_rate: data.stcg_tax_rate ? parseFloat(data.stcg_tax_rate) : undefined,\n        dividend_tax_rate: data.dividend_tax_rate ? parseFloat(data.dividend_tax_rate) : undefined\n    }));\n// Asset schema\nconst assetSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid(),\n    symbol: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Symbol is required'),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Asset name is required'),\n    asset_class_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Valid asset class is required'),\n    exchange: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    currency: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Currency is required'),\n    isin: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    sector: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    industry: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    market_cap: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive().optional(),\n    current_price: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive().optional(),\n    last_updated: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    is_active: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean(),\n    created_at: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    updated_at: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n});\n// Asset form schemas\nconst assetFormInputSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    symbol: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Symbol is required').max(20, 'Symbol too long'),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Asset name is required'),\n    asset_class_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Please select an asset class'),\n    exchange: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    currency: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Currency is required'),\n    isin: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    sector: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    industry: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    market_cap: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    current_price: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n});\nconst assetFormSchema = assetFormInputSchema.transform((data)=>({\n        ...data,\n        market_cap: data.market_cap ? parseFloat(data.market_cap) : undefined,\n        current_price: data.current_price ? parseFloat(data.current_price) : undefined\n    }));\n// Holding schema\nconst holdingSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid(),\n    user_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid(),\n    account_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid(),\n    asset_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid(),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Quantity must be positive'),\n    average_cost: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Average cost must be positive'),\n    current_value: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n    unrealized_gain_loss: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n    unrealized_gain_loss_percentage: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n    total_invested: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Total invested must be positive'),\n    last_updated: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    created_at: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    updated_at: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n});\n// Enhanced investment transaction schema with asset information\nconst investmentTransactionWithAssetSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid(),\n    user_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid(),\n    account_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid(),\n    asset_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid(),\n    transaction_type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'investment_buy',\n        'investment_sell'\n    ]),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Quantity must be positive'),\n    price_per_unit: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Price must be positive'),\n    total_amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Total amount must be positive'),\n    fees: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0, 'Fees cannot be negative'),\n    transaction_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    realized_gain_loss: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n    tax_implications: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        holding_period_months: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0),\n        is_long_term: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean(),\n        applicable_tax_rate: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0).max(100),\n        tax_amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0)\n    }).optional(),\n    created_at: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    updated_at: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n});\n// Tax calculation schema\nconst taxCalculationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    asset_class_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid(),\n    purchase_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    sale_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    purchase_price: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive(),\n    sale_price: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive(),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive()\n});\nconst taxCalculationResultSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    holding_period_months: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n    is_long_term: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean(),\n    capital_gain_loss: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n    applicable_tax_rate: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n    tax_amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n    net_proceeds: zod__WEBPACK_IMPORTED_MODULE_0__.z.number()\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/schemas/assets.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/types/assets.ts":
/*!*************************************************!*\
  !*** ../../packages/shared/src/types/assets.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ASSET_CLASSES: () => (/* binding */ DEFAULT_ASSET_CLASSES)\n/* harmony export */ });\n// Asset class types and interfaces\n// Predefined asset classes with Indian tax implications\nconst DEFAULT_ASSET_CLASSES = [\n    {\n        name: 'Equity Shares',\n        class: 'stocks',\n        sub_class: 'large_cap',\n        description: 'Listed equity shares on stock exchanges',\n        risk_level: 'high',\n        liquidity: 'high',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'equity',\n        ltcg_period_months: 12,\n        ltcg_tax_rate: 10,\n        stcg_tax_rate: 15,\n        dividend_tax_rate: 0 // Dividend income taxed as per slab\n    },\n    {\n        name: 'Equity Mutual Funds',\n        class: 'mutual_funds',\n        sub_class: 'equity_funds',\n        description: 'Mutual funds investing primarily in equity',\n        risk_level: 'high',\n        liquidity: 'high',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'equity',\n        ltcg_period_months: 12,\n        ltcg_tax_rate: 10,\n        stcg_tax_rate: 15\n    },\n    {\n        name: 'Debt Mutual Funds',\n        class: 'mutual_funds',\n        sub_class: 'debt_funds',\n        description: 'Mutual funds investing primarily in debt instruments',\n        risk_level: 'medium',\n        liquidity: 'high',\n        typical_holding_period: 'medium_term',\n        tax_treatment: 'debt',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30 // As per income tax slab\n    },\n    {\n        name: 'Government Bonds',\n        class: 'bonds',\n        sub_class: 'government_bonds',\n        description: 'Government issued bonds and securities',\n        risk_level: 'low',\n        liquidity: 'medium',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'debt',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30\n    },\n    {\n        name: 'Corporate Bonds',\n        class: 'bonds',\n        sub_class: 'corporate_bonds',\n        description: 'Corporate issued bonds and debentures',\n        risk_level: 'medium',\n        liquidity: 'medium',\n        typical_holding_period: 'medium_term',\n        tax_treatment: 'debt',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30\n    },\n    {\n        name: 'Exchange Traded Funds',\n        class: 'etfs',\n        sub_class: 'equity_etfs',\n        description: 'Exchange traded funds tracking various indices',\n        risk_level: 'medium',\n        liquidity: 'high',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'equity',\n        ltcg_period_months: 12,\n        ltcg_tax_rate: 10,\n        stcg_tax_rate: 15\n    },\n    {\n        name: 'Real Estate Investment Trusts',\n        class: 'real_estate',\n        sub_class: 'reits',\n        description: 'REITs investing in real estate properties',\n        risk_level: 'medium',\n        liquidity: 'medium',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'other',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30\n    },\n    {\n        name: 'Gold ETFs',\n        class: 'commodities',\n        sub_class: 'precious_metals',\n        description: 'Gold exchange traded funds',\n        risk_level: 'medium',\n        liquidity: 'high',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'other',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/types/assets.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1e4574c47899\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL2FwcHMvd2ViL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxZTQ1NzRjNDc4OTlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ })

});