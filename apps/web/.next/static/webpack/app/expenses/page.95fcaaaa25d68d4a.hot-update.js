"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/components/TransactionList.tsx":
/*!********************************************!*\
  !*** ./src/components/TransactionList.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionListWeb: () => (/* binding */ TransactionListWeb),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction TransactionListWeb(param) {\n    let { onEditTransaction, key, ...props } = param;\n    _s();\n    const { formatCurrency } = (0,_repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore)();\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        searchQuery: '',\n        categoryId: '',\n        accountId: '',\n        startDate: '',\n        endDate: '',\n        transactionType: 'all'\n    });\n    const [offset, setOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasMoreItems, setHasMoreItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ITEMS_PER_PAGE = 20;\n    const loadTransactions = async function() {\n        let reset = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const currentOffset = reset ? 0 : offset;\n            if (reset) {\n                setLoading(true);\n                setError('');\n            } else {\n                setLoadingMore(true);\n            }\n            const options = {\n                limit: ITEMS_PER_PAGE,\n                offset: currentOffset,\n                ...filters.categoryId && {\n                    categoryId: filters.categoryId\n                },\n                ...filters.accountId && {\n                    accountId: filters.accountId\n                },\n                ...filters.startDate && {\n                    startDate: filters.startDate\n                },\n                ...filters.endDate && {\n                    endDate: filters.endDate\n                },\n                ...filters.transactionType !== 'all' && {\n                    transactionType: filters.transactionType\n                },\n                ...filters.searchQuery && {\n                    searchQuery: filters.searchQuery\n                },\n                includeTransfers: true,\n                includeInvestments: true\n            };\n            const result = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.getTransactions(options);\n            if (reset) {\n                setTransactions(result.data);\n                setOffset(ITEMS_PER_PAGE);\n            } else {\n                setTransactions((prev)=>[\n                        ...prev,\n                        ...result.data\n                    ]);\n                setOffset((prev)=>prev + ITEMS_PER_PAGE);\n            }\n            setTotalCount(result.count);\n            setHasMoreItems(result.data.length === ITEMS_PER_PAGE && currentOffset + ITEMS_PER_PAGE < result.count);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load transactions');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to load transactions');\n        } finally{\n            setLoading(false);\n            setLoadingMore(false);\n        }\n    };\n    const loadCategories = async ()=>{\n        try {\n            const categoriesData = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.CategoryService.getCategories({\n                is_active: true\n            });\n            setCategories(categoriesData);\n        } catch (err) {\n            console.error('Failed to load categories:', err);\n        }\n    };\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            loadCategories();\n            loadTransactions(true);\n        }\n    }[\"TransactionListWeb.useEffect\"], []);\n    // Reload when key changes (for refresh from parent)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            if (key !== undefined) {\n                loadTransactions(true);\n            }\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        key\n    ]);\n    // Reload when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            loadTransactions(true);\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        filters\n    ]);\n    const handleRefresh = ()=>{\n        loadTransactions(true);\n    };\n    const handleLoadMore = ()=>{\n        if (!loadingMore && hasMoreItems) {\n            loadTransactions(false);\n        }\n    };\n    const handleSearch = (query)=>{\n        setFilters((prev)=>({\n                ...prev,\n                searchQuery: query\n            }));\n    };\n    const handleFilterChange = (newFilters)=>{\n        setFilters((prev)=>({\n                ...prev,\n                ...newFilters\n            }));\n    };\n    const handleEdit = (transaction)=>{\n        onEditTransaction === null || onEditTransaction === void 0 ? void 0 : onEditTransaction(transaction);\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm('Are you sure you want to delete this transaction?')) {\n            return;\n        }\n        try {\n            await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.deleteTransaction(id);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success('Transaction deleted successfully');\n            loadTransactions(true);\n        } catch (err) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to delete transaction');\n        }\n    };\n    const formatDate = (date)=>new Date(date).toLocaleDateString();\n    // Helper function to get transaction display info\n    const getTransactionDisplayInfo = (transaction)=>{\n        switch(transaction.transaction_type){\n            case 'income':\n                var _transaction_category, _transaction_category1;\n                return {\n                    icon: ((_transaction_category = transaction.category) === null || _transaction_category === void 0 ? void 0 : _transaction_category.icon) || '💰',\n                    title: ((_transaction_category1 = transaction.category) === null || _transaction_category1 === void 0 ? void 0 : _transaction_category1.name) || 'Income',\n                    color: 'text-green-600',\n                    sign: '+',\n                    description: transaction.description\n                };\n            case 'expense':\n                var _transaction_category2, _transaction_category3;\n                return {\n                    icon: ((_transaction_category2 = transaction.category) === null || _transaction_category2 === void 0 ? void 0 : _transaction_category2.icon) || '💸',\n                    title: ((_transaction_category3 = transaction.category) === null || _transaction_category3 === void 0 ? void 0 : _transaction_category3.name) || 'Expense',\n                    color: 'text-red-600',\n                    sign: '-',\n                    description: transaction.description\n                };\n            case 'transfer':\n                var _transaction_account, _transaction_to_account;\n                return {\n                    icon: '🔄',\n                    title: \"Transfer \".concat(((_transaction_account = transaction.account) === null || _transaction_account === void 0 ? void 0 : _transaction_account.name) ? \"from \".concat(transaction.account.name) : '').concat(((_transaction_to_account = transaction.to_account) === null || _transaction_to_account === void 0 ? void 0 : _transaction_to_account.name) ? \" to \".concat(transaction.to_account.name) : ''),\n                    color: 'text-blue-600',\n                    sign: '',\n                    description: transaction.description || 'Account transfer'\n                };\n            case 'investment_buy':\n                return {\n                    icon: '📈',\n                    title: \"Buy \".concat(transaction.investment_symbol),\n                    color: 'text-purple-600',\n                    sign: '-',\n                    description: transaction.description || \"\".concat(transaction.investment_quantity, \" shares at \").concat(formatCurrency(transaction.investment_price || 0))\n                };\n            case 'investment_sell':\n                return {\n                    icon: '📉',\n                    title: \"Sell \".concat(transaction.investment_symbol),\n                    color: 'text-orange-600',\n                    sign: '+',\n                    description: transaction.description || \"\".concat(transaction.investment_quantity, \" shares at \").concat(formatCurrency(transaction.investment_price || 0))\n                };\n            case 'dividend':\n                return {\n                    icon: '💎',\n                    title: \"Dividend from \".concat(transaction.investment_symbol),\n                    color: 'text-green-600',\n                    sign: '+',\n                    description: transaction.description || 'Dividend payment'\n                };\n            default:\n                return {\n                    icon: '💰',\n                    title: 'Transaction',\n                    color: 'text-gray-600',\n                    sign: '',\n                    description: transaction.description\n                };\n        }\n    };\n    // Quick date filter functions\n    const [activeQuickFilter, setActiveQuickFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const setQuickDateFilter = (period)=>{\n        const today = new Date();\n        let startDate;\n        switch(period){\n            case 'week':\n                startDate = new Date(today);\n                startDate.setDate(today.getDate() - 7);\n                break;\n            case 'month':\n                startDate = new Date(today);\n                startDate.setMonth(today.getMonth() - 1);\n                break;\n            case 'year':\n                startDate = new Date(today);\n                startDate.setFullYear(today.getFullYear() - 1);\n                break;\n        }\n        setActiveQuickFilter(period);\n        setFilters((prev)=>({\n                ...prev,\n                startDate: startDate.toISOString().split('T')[0],\n                endDate: today.toISOString().split('T')[0]\n            }));\n    };\n    const clearDateFilter = ()=>{\n        setActiveQuickFilter('all');\n        setFilters((prev)=>({\n                ...prev,\n                startDate: '',\n                endDate: ''\n            }));\n    };\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const loadMoreRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Sync search input with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            setSearchInput(filters.searchQuery);\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        filters.searchQuery\n    ]);\n    // Debounced search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"TransactionListWeb.useEffect.timeoutId\": ()=>{\n                    if (searchInput !== filters.searchQuery) {\n                        setFilters({\n                            \"TransactionListWeb.useEffect.timeoutId\": (prev)=>({\n                                    ...prev,\n                                    searchQuery: searchInput\n                                })\n                        }[\"TransactionListWeb.useEffect.timeoutId\"]);\n                    }\n                }\n            }[\"TransactionListWeb.useEffect.timeoutId\"], 500);\n            return ({\n                \"TransactionListWeb.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"TransactionListWeb.useEffect\"];\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        searchInput,\n        filters.searchQuery\n    ]);\n    // Infinite scroll implementation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"TransactionListWeb.useEffect\": (entries)=>{\n                    if (entries[0].isIntersecting && hasMoreItems && !loadingMore) {\n                        handleLoadMore();\n                    }\n                }\n            }[\"TransactionListWeb.useEffect\"], {\n                threshold: 0.1\n            });\n            if (loadMoreRef.current) {\n                observer.observe(loadMoreRef.current);\n            }\n            return ({\n                \"TransactionListWeb.useEffect\": ()=>observer.disconnect()\n            })[\"TransactionListWeb.useEffect\"];\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        hasMoreItems,\n        loadingMore,\n        handleLoadMore\n    ]);\n    const handleSearchSubmit = (e)=>{\n        e.preventDefault();\n        handleSearch(searchInput);\n    };\n    const clearFilters = ()=>{\n        const clearedFilters = {\n            searchQuery: '',\n            categoryId: '',\n            accountId: '',\n            startDate: '',\n            endDate: '',\n            transactionType: 'all'\n        };\n        setActiveQuickFilter(null);\n        setFilters(clearedFilters);\n    };\n    const hasActiveFilters = filters.searchQuery || filters.categoryId || filters.accountId || filters.startDate || filters.endDate || filters.transactionType !== 'all';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg border border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-5 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this),\n                                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mt-1\",\n                                        children: [\n                                            totalCount,\n                                            \" transaction\",\n                                            totalCount === 1 ? '' : 's',\n                                            \" found\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSearchSubmit,\n                                        className: \"flex-1 sm:flex-initial\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search by description or category...\",\n                                                    value: searchInput,\n                                                    onChange: (e)=>setSearchInput(e.target.value),\n                                                    className: \"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-5 w-5 text-gray-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFilters(!showFilters),\n                                        className: \"px-4 py-2 rounded-lg font-medium transition-colors \".concat(showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Filters\",\n                                                hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-red-500 text-white rounded-full w-2 h-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRefresh,\n                                        disabled: loading,\n                                        className: \"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors disabled:opacity-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 \".concat(loading ? 'animate-spin' : ''),\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Quick Date Filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('week'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'week' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last 7 Days\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('month'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'month' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last Month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('year'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'year' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last Year\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearDateFilter,\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'all' ? 'bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'),\n                                                children: \"All Time\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.categoryId,\n                                                onChange: (e)=>handleFilterChange({\n                                                        categoryId: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Categories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: category.id,\n                                                            children: [\n                                                                category.icon,\n                                                                \" \",\n                                                                category.name\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.transactionType,\n                                                onChange: (e)=>handleFilterChange({\n                                                        transactionType: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"income\",\n                                                        children: \"Income\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"expense\",\n                                                        children: \"Expense\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"transfer\",\n                                                        children: \"Transfer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"investment_buy\",\n                                                        children: \"Investment Purchase\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"investment_sell\",\n                                                        children: \"Investment Sale\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"dividend\",\n                                                        children: \"Dividend\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"From Date\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: filters.startDate,\n                                                onChange: (e)=>handleFilterChange({\n                                                        startDate: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"To Date\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: filters.endDate,\n                                                onChange: (e)=>handleFilterChange({\n                                                        endDate: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this),\n                            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearFilters,\n                                    className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n                                    children: \"Clear all filters\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 border-l-4 border-red-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                    lineNumber: 528,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 527,\n                columnNumber: 9\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Loading transactions...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 538,\n                columnNumber: 9\n            }, this) : transactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-16 px-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-12 h-12 text-gray-400\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"No transactions found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 max-w-sm mx-auto\",\n                        children: hasActiveFilters ? 'Try adjusting your filters or search terms to find more transactions' : 'Add your first expense or income to get started!'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 550,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 543,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: transactions.map((transaction)=>{\n                            var _transaction_account, _transaction_to_account;\n                            const displayInfo = getTransactionDisplayInfo(transaction);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 hover:bg-gray-50 transition-colors group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl flex-shrink-0\",\n                                                    children: displayInfo.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-900 truncate\",\n                                                                    children: displayInfo.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 ml-2 flex-shrink-0\",\n                                                                    children: formatDate(transaction.transaction_date)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        displayInfo.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 truncate\",\n                                                            children: displayInfo.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        transaction.transaction_type === 'transfer' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 text-xs text-gray-500 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (_transaction_account = transaction.account) === null || _transaction_account === void 0 ? void 0 : _transaction_account.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"→\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (_transaction_to_account = transaction.to_account) === null || _transaction_to_account === void 0 ? void 0 : _transaction_to_account.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        (transaction.transaction_type === 'investment_buy' || transaction.transaction_type === 'investment_sell') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: [\n                                                                transaction.investment_quantity,\n                                                                \" shares \\xd7 \",\n                                                                formatCurrency(transaction.investment_price || 0),\n                                                                transaction.fees && transaction.fees > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2\",\n                                                                    children: [\n                                                                        \"+ \",\n                                                                        formatCurrency(transaction.fees),\n                                                                        \" fees\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base font-semibold \".concat(displayInfo.color),\n                                                            children: [\n                                                                displayInfo.sign,\n                                                                formatCurrency(transaction.amount)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-block px-2 py-1 text-xs rounded-full \".concat(transaction.transaction_type === 'income' ? 'bg-green-100 text-green-800' : transaction.transaction_type === 'expense' ? 'bg-red-100 text-red-800' : transaction.transaction_type === 'transfer' ? 'bg-blue-100 text-blue-800' : transaction.transaction_type === 'investment_buy' ? 'bg-purple-100 text-purple-800' : transaction.transaction_type === 'investment_sell' ? 'bg-orange-100 text-orange-800' : transaction.transaction_type === 'dividend' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                            children: transaction.transaction_type.replace('_', ' ')\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        onEditTransaction && (transaction.transaction_type === 'income' || transaction.transaction_type === 'expense') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleEdit(transaction),\n                                                            className: \"p-1.5 text-gray-400 hover:text-blue-600 transition-colors\",\n                                                            title: \"Edit transaction\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-4 w-4\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleDelete(transaction.id),\n                                                            className: \"p-1.5 text-gray-400 hover:text-red-600 transition-colors\",\n                                                            title: \"Delete transaction\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-4 w-4\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 645,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 19\n                                }, this)\n                            }, transaction.id, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 11\n                    }, this),\n                    hasMoreItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: loadMoreRef,\n                        className: \"p-6 text-center border-t\",\n                        children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-gray-600\",\n                                    children: \"Loading more...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLoadMore,\n                            className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                            children: \"Load More Transactions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, this);\n}\n_s(TransactionListWeb, \"4l0Emjm/S3khbNUhtPxFspR6QXc=\", false, function() {\n    return [\n        _repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore\n    ];\n});\n_c = TransactionListWeb;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TransactionListWeb);\nvar _c;\n$RefreshReg$(_c, \"TransactionListWeb\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TransactionList.tsx\n"));

/***/ })

});