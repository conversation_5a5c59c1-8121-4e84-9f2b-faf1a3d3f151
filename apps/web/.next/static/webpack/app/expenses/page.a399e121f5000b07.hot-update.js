"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/components/TransactionList.tsx":
/*!********************************************!*\
  !*** ./src/components/TransactionList.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionListWeb: () => (/* binding */ TransactionListWeb),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction TransactionListWeb(param) {\n    let { onEditTransaction, key, ...props } = param;\n    _s();\n    const { formatCurrency } = (0,_repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore)();\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        searchQuery: '',\n        categoryId: '',\n        accountId: '',\n        startDate: '',\n        endDate: '',\n        transactionType: 'all'\n    });\n    const [offset, setOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasMoreItems, setHasMoreItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ITEMS_PER_PAGE = 20;\n    const loadTransactions = async function() {\n        let reset = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const currentOffset = reset ? 0 : offset;\n            if (reset) {\n                setLoading(true);\n                setError('');\n            } else {\n                setLoadingMore(true);\n            }\n            const options = {\n                limit: ITEMS_PER_PAGE,\n                offset: currentOffset,\n                ...filters.categoryId && {\n                    categoryId: filters.categoryId\n                },\n                ...filters.accountId && {\n                    accountId: filters.accountId\n                },\n                ...filters.startDate && {\n                    startDate: filters.startDate\n                },\n                ...filters.endDate && {\n                    endDate: filters.endDate\n                },\n                ...filters.transactionType !== 'all' && {\n                    transactionType: filters.transactionType\n                },\n                ...filters.searchQuery && {\n                    searchQuery: filters.searchQuery\n                },\n                includeTransfers: true,\n                includeInvestments: true\n            };\n            const result = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.getTransactions(options);\n            if (reset) {\n                setTransactions(result.data);\n                setOffset(ITEMS_PER_PAGE);\n            } else {\n                setTransactions((prev)=>[\n                        ...prev,\n                        ...result.data\n                    ]);\n                setOffset((prev)=>prev + ITEMS_PER_PAGE);\n            }\n            setTotalCount(result.count);\n            setHasMoreItems(result.data.length === ITEMS_PER_PAGE && currentOffset + ITEMS_PER_PAGE < result.count);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load transactions');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to load transactions');\n        } finally{\n            setLoading(false);\n            setLoadingMore(false);\n        }\n    };\n    const loadCategories = async ()=>{\n        try {\n            const categoriesData = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.CategoryService.getCategories({\n                is_active: true\n            });\n            setCategories(categoriesData);\n        } catch (err) {\n            console.error('Failed to load categories:', err);\n        }\n    };\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            loadCategories();\n            loadTransactions(true);\n        }\n    }[\"TransactionListWeb.useEffect\"], []);\n    // Reload when key changes (for refresh from parent)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            if (key !== undefined) {\n                loadTransactions(true);\n            }\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        key\n    ]);\n    // Reload when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            loadTransactions(true);\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        filters\n    ]);\n    const handleRefresh = ()=>{\n        loadTransactions(true);\n    };\n    const handleLoadMore = ()=>{\n        if (!loadingMore && hasMoreItems) {\n            loadTransactions(false);\n        }\n    };\n    const handleSearch = (query)=>{\n        setFilters((prev)=>({\n                ...prev,\n                searchQuery: query\n            }));\n    };\n    const handleFilterChange = (newFilters)=>{\n        setFilters((prev)=>({\n                ...prev,\n                ...newFilters\n            }));\n    };\n    const handleEdit = (transaction)=>{\n        onEditTransaction === null || onEditTransaction === void 0 ? void 0 : onEditTransaction(transaction);\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm('Are you sure you want to delete this transaction?')) {\n            return;\n        }\n        try {\n            await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.deleteTransaction(id);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success('Transaction deleted successfully');\n            loadTransactions(true);\n        } catch (err) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to delete transaction');\n        }\n    };\n    const formatDate = (date)=>new Date(date).toLocaleDateString();\n    // Quick date filter functions\n    const [activeQuickFilter, setActiveQuickFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const setQuickDateFilter = (period)=>{\n        const today = new Date();\n        let startDate;\n        switch(period){\n            case 'week':\n                startDate = new Date(today);\n                startDate.setDate(today.getDate() - 7);\n                break;\n            case 'month':\n                startDate = new Date(today);\n                startDate.setMonth(today.getMonth() - 1);\n                break;\n            case 'year':\n                startDate = new Date(today);\n                startDate.setFullYear(today.getFullYear() - 1);\n                break;\n        }\n        setActiveQuickFilter(period);\n        setFilters((prev)=>({\n                ...prev,\n                startDate: startDate.toISOString().split('T')[0],\n                endDate: today.toISOString().split('T')[0]\n            }));\n    };\n    const clearDateFilter = ()=>{\n        setActiveQuickFilter('all');\n        setFilters((prev)=>({\n                ...prev,\n                startDate: '',\n                endDate: ''\n            }));\n    };\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const loadMoreRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Sync search input with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            setSearchInput(filters.searchQuery);\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        filters.searchQuery\n    ]);\n    // Debounced search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"TransactionListWeb.useEffect.timeoutId\": ()=>{\n                    if (searchInput !== filters.searchQuery) {\n                        setFilters({\n                            \"TransactionListWeb.useEffect.timeoutId\": (prev)=>({\n                                    ...prev,\n                                    searchQuery: searchInput\n                                })\n                        }[\"TransactionListWeb.useEffect.timeoutId\"]);\n                    }\n                }\n            }[\"TransactionListWeb.useEffect.timeoutId\"], 500);\n            return ({\n                \"TransactionListWeb.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"TransactionListWeb.useEffect\"];\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        searchInput,\n        filters.searchQuery\n    ]);\n    // Infinite scroll implementation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"TransactionListWeb.useEffect\": (entries)=>{\n                    if (entries[0].isIntersecting && hasMoreItems && !loadingMore) {\n                        handleLoadMore();\n                    }\n                }\n            }[\"TransactionListWeb.useEffect\"], {\n                threshold: 0.1\n            });\n            if (loadMoreRef.current) {\n                observer.observe(loadMoreRef.current);\n            }\n            return ({\n                \"TransactionListWeb.useEffect\": ()=>observer.disconnect()\n            })[\"TransactionListWeb.useEffect\"];\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        hasMoreItems,\n        loadingMore,\n        handleLoadMore\n    ]);\n    const handleSearchSubmit = (e)=>{\n        e.preventDefault();\n        handleSearch(searchInput);\n    };\n    const clearFilters = ()=>{\n        const clearedFilters = {\n            searchQuery: '',\n            categoryId: '',\n            startDate: '',\n            endDate: '',\n            transactionType: 'all'\n        };\n        setActiveQuickFilter(null);\n        setFilters(clearedFilters);\n    };\n    const hasActiveFilters = filters.searchQuery || filters.categoryId || filters.startDate || filters.endDate || filters.transactionType !== 'all';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg border border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-5 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this),\n                                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mt-1\",\n                                        children: [\n                                            totalCount,\n                                            \" transaction\",\n                                            totalCount === 1 ? '' : 's',\n                                            \" found\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSearchSubmit,\n                                        className: \"flex-1 sm:flex-initial\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search by description or category...\",\n                                                    value: searchInput,\n                                                    onChange: (e)=>setSearchInput(e.target.value),\n                                                    className: \"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-5 w-5 text-gray-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFilters(!showFilters),\n                                        className: \"px-4 py-2 rounded-lg font-medium transition-colors \".concat(showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Filters\",\n                                                hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-red-500 text-white rounded-full w-2 h-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRefresh,\n                                        disabled: loading,\n                                        className: \"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors disabled:opacity-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 \".concat(loading ? 'animate-spin' : ''),\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this),\n                    showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Quick Date Filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('week'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'week' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last 7 Days\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('month'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'month' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last Month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('year'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'year' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last Year\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearDateFilter,\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'all' ? 'bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'),\n                                                children: \"All Time\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.categoryId,\n                                                onChange: (e)=>handleFilterChange({\n                                                        categoryId: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Categories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: category.id,\n                                                            children: [\n                                                                category.icon,\n                                                                \" \",\n                                                                category.name\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.transactionType,\n                                                onChange: (e)=>handleFilterChange({\n                                                        transactionType: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"income\",\n                                                        children: \"Income\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"expense\",\n                                                        children: \"Expense\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"transfer\",\n                                                        children: \"Transfer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"investment_buy\",\n                                                        children: \"Investment Purchase\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"investment_sell\",\n                                                        children: \"Investment Sale\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"dividend\",\n                                                        children: \"Dividend\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"From Date\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: filters.startDate,\n                                                onChange: (e)=>handleFilterChange({\n                                                        startDate: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"To Date\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: filters.endDate,\n                                                onChange: (e)=>handleFilterChange({\n                                                        endDate: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this),\n                            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearFilters,\n                                    className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n                                    children: \"Clear all filters\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 border-l-4 border-red-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 464,\n                columnNumber: 9\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Loading transactions...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 475,\n                columnNumber: 9\n            }, this) : transactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-16 px-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-12 h-12 text-gray-400\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 481,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"No transactions found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 max-w-sm mx-auto\",\n                        children: hasActiveFilters ? 'Try adjusting your filters or search terms to find more transactions' : 'Add your first expense or income to get started!'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 480,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: transactions.map((transaction)=>{\n                            var _transaction_category, _transaction_category1;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 hover:bg-gray-50 transition-colors group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl flex-shrink-0\",\n                                                    children: ((_transaction_category = transaction.category) === null || _transaction_category === void 0 ? void 0 : _transaction_category.icon) || '💰'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-900 truncate\",\n                                                                    children: ((_transaction_category1 = transaction.category) === null || _transaction_category1 === void 0 ? void 0 : _transaction_category1.name) || 'Uncategorized'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 ml-2 flex-shrink-0\",\n                                                                    children: formatDate(transaction.transaction_date)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        transaction.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 truncate\",\n                                                            children: transaction.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-semibold \".concat(transaction.transaction_type === 'income' ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            transaction.transaction_type === 'income' ? '+' : '-',\n                                                            formatCurrency(transaction.amount)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        onEditTransaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleEdit(transaction),\n                                                            className: \"p-1.5 text-gray-400 hover:text-blue-600 transition-colors\",\n                                                            title: \"Edit transaction\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-4 w-4\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleDelete(transaction.id),\n                                                            className: \"p-1.5 text-gray-400 hover:text-red-600 transition-colors\",\n                                                            title: \"Delete transaction\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-4 w-4\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 17\n                                }, this)\n                            }, transaction.id, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 11\n                    }, this),\n                    hasMoreItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: loadMoreRef,\n                        className: \"p-6 text-center border-t\",\n                        children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-gray-600\",\n                                    children: \"Loading more...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLoadMore,\n                            className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                            children: \"Load More Transactions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n_s(TransactionListWeb, \"4l0Emjm/S3khbNUhtPxFspR6QXc=\", false, function() {\n    return [\n        _repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore\n    ];\n});\n_c = TransactionListWeb;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TransactionListWeb);\nvar _c;\n$RefreshReg$(_c, \"TransactionListWeb\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TransactionList.tsx\n"));

/***/ })

});