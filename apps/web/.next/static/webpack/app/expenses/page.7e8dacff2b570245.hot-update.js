"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/app/expenses/page.tsx":
/*!***********************************!*\
  !*** ./src/app/expenses/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExpensesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var _components_TransactionForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/TransactionForm */ \"(app-pages-browser)/./src/components/TransactionForm.tsx\");\n/* harmony import */ var _components_TransactionList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/TransactionList */ \"(app-pages-browser)/./src/components/TransactionList.tsx\");\n/* harmony import */ var _components_TransactionTemplates__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/TransactionTemplates */ \"(app-pages-browser)/./src/components/TransactionTemplates.tsx\");\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Modal */ \"(app-pages-browser)/./src/components/Modal.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ExpensesPage() {\n    var _editingTransaction_amount, _editingTransaction_fees, _editingTransaction_investment_quantity, _editingTransaction_investment_price;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTemplates, setShowTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTransaction, setEditingTransaction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [templateData, setTemplateData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ExpensesPage.useEffect\": ()=>{\n            loadCategories();\n        }\n    }[\"ExpensesPage.useEffect\"], []);\n    const loadCategories = async ()=>{\n        try {\n            const categoriesData = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.CategoryService.getCategories({\n                is_active: true\n            });\n            setCategories(categoriesData);\n        } catch (error) {\n            console.error('Failed to load categories:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to load categories');\n        }\n    };\n    const handleSubmit = async (data)=>{\n        setSubmitting(true);\n        try {\n            if (editingTransaction) {\n                // For basic transactions, use the update method\n                if (editingTransaction.transaction_type === 'income' || editingTransaction.transaction_type === 'expense') {\n                    await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.updateTransaction(editingTransaction.id, {\n                        amount: data.amount,\n                        description: data.description,\n                        category_id: data.category_id,\n                        transaction_date: data.transaction_date,\n                        fees: data.fees\n                    });\n                } else {\n                    throw new Error('Cannot update transfer or investment transactions');\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.toast.success('Transaction updated successfully!');\n            } else {\n                await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.createTransaction(data);\n                const transactionTypeLabel = {\n                    income: 'Income',\n                    expense: 'Expense',\n                    transfer: 'Transfer',\n                    investment_buy: 'Investment Purchase',\n                    investment_sell: 'Investment Sale',\n                    dividend: 'Dividend'\n                }[data.transaction_type] || 'Transaction';\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(transactionTypeLabel, \" added successfully!\"));\n            }\n            setShowForm(false);\n            setEditingTransaction(null);\n            setTemplateData(null);\n            setRefreshKey((prev)=>prev + 1);\n        } catch (error) {\n            console.error('Failed to save transaction:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to \".concat(editingTransaction ? 'update' : 'add', \" transaction\"));\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleEditTransaction = (transaction)=>{\n        setEditingTransaction(transaction);\n        setShowForm(true);\n    };\n    const handleCancelEdit = ()=>{\n        setEditingTransaction(null);\n        setTemplateData(null);\n        setShowForm(false);\n    };\n    const handleUseTemplate = (template)=>{\n        var _template_amount;\n        setTemplateData({\n            amount: ((_template_amount = template.amount) === null || _template_amount === void 0 ? void 0 : _template_amount.toString()) || '',\n            category_id: template.category_id || '',\n            description: template.description || '',\n            transaction_date: new Date(),\n            transaction_type: template.transaction_type || 'expense',\n            account_id: '',\n            to_account_id: '',\n            fees: '',\n            investment_symbol: '',\n            investment_quantity: '',\n            investment_price: '',\n            funding_account_id: ''\n        });\n        setShowTemplates(false);\n        setShowForm(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    currentPage: \"expenses\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-4xl font-bold text-text-primary\",\n                                                children: \"Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary text-lg mt-2\",\n                                                children: \"Track your income, expenses, transfers, and investments\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            editingTransaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCancelEdit,\n                                                className: \"bg-surface text-text-secondary border border-border px-6 py-3 rounded-lg font-medium hover:bg-surface-elevated transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Cancel Edit\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowTemplates(true),\n                                                className: \"bg-surface text-text-primary border border-border px-6 py-3 rounded-lg font-medium hover:bg-surface-elevated transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Templates\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowForm(true),\n                                                className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 4v16m8-8H4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Add Transaction\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                                isOpen: showForm,\n                                onClose: ()=>{\n                                    setShowForm(false);\n                                    setEditingTransaction(null);\n                                    setTemplateData(null);\n                                },\n                                title: editingTransaction ? 'Edit Transaction' : templateData ? 'New Transaction from Template' : 'Add New Transaction',\n                                size: \"xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionForm__WEBPACK_IMPORTED_MODULE_3__.TransactionForm, {\n                                    onSubmit: handleSubmit,\n                                    loading: submitting,\n                                    compact: true,\n                                    initialData: editingTransaction ? {\n                                        amount: ((_editingTransaction_amount = editingTransaction.amount) === null || _editingTransaction_amount === void 0 ? void 0 : _editingTransaction_amount.toString()) || '',\n                                        category_id: editingTransaction.category_id || '',\n                                        account_id: editingTransaction.account_id || '',\n                                        to_account_id: editingTransaction.to_account_id || '',\n                                        description: editingTransaction.description || '',\n                                        transaction_date: new Date(editingTransaction.transaction_date),\n                                        transaction_type: editingTransaction.transaction_type,\n                                        fees: ((_editingTransaction_fees = editingTransaction.fees) === null || _editingTransaction_fees === void 0 ? void 0 : _editingTransaction_fees.toString()) || '',\n                                        investment_symbol: editingTransaction.investment_symbol || '',\n                                        investment_quantity: ((_editingTransaction_investment_quantity = editingTransaction.investment_quantity) === null || _editingTransaction_investment_quantity === void 0 ? void 0 : _editingTransaction_investment_quantity.toString()) || '',\n                                        investment_price: ((_editingTransaction_investment_price = editingTransaction.investment_price) === null || _editingTransaction_investment_price === void 0 ? void 0 : _editingTransaction_investment_price.toString()) || '',\n                                        funding_account_id: ''\n                                    } : templateData ? templateData : undefined\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                                isOpen: showTemplates,\n                                onClose: ()=>setShowTemplates(false),\n                                title: \"Transaction Templates\",\n                                size: \"lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-text-secondary\",\n                                            children: \"Choose from your saved templates to quickly create new transactions.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionTemplates__WEBPACK_IMPORTED_MODULE_5__.TransactionTemplates, {\n                                            categories: categories,\n                                            onUseTemplate: handleUseTemplate\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                onEditTransaction: handleEditTransaction\n                            }, refreshKey, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_s(ExpensesPage, \"UERJjLKSgP48aUuJiGmfvLSILOk=\");\n_c = ExpensesPage;\nvar _c;\n$RefreshReg$(_c, \"ExpensesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/expenses/page.tsx\n"));

/***/ })

});