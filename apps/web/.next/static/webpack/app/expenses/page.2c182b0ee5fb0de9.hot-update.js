"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/app/expenses/page.tsx":
/*!***********************************!*\
  !*** ./src/app/expenses/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExpensesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var _components_TransactionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/TransactionList */ \"(app-pages-browser)/./src/components/TransactionList.tsx\");\n/* harmony import */ var _components_TransactionTemplates__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/TransactionTemplates */ \"(app-pages-browser)/./src/components/TransactionTemplates.tsx\");\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Modal */ \"(app-pages-browser)/./src/components/Modal.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ExpensesPage() {\n    var _editingTransaction_amount, _editingTransaction_fees, _editingTransaction_investment_quantity, _editingTransaction_investment_price;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showInvestmentForm, setShowInvestmentForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTemplates, setShowTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTransaction, setEditingTransaction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [templateData, setTemplateData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ExpensesPage.useEffect\": ()=>{\n            loadCategories();\n        }\n    }[\"ExpensesPage.useEffect\"], []);\n    const loadCategories = async ()=>{\n        try {\n            const categoriesData = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.CategoryService.getCategories({\n                is_active: true\n            });\n            setCategories(categoriesData);\n        } catch (error) {\n            console.error('Failed to load categories:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Failed to load categories');\n        }\n    };\n    const handleSubmit = async (data)=>{\n        setSubmitting(true);\n        try {\n            if (editingTransaction) {\n                // For basic transactions, use the update method\n                if (editingTransaction.transaction_type === 'income' || editingTransaction.transaction_type === 'expense') {\n                    await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.updateTransaction(editingTransaction.id, {\n                        amount: data.amount,\n                        description: data.description,\n                        category_id: data.category_id,\n                        transaction_date: data.transaction_date,\n                        fees: data.fees\n                    });\n                } else {\n                    throw new Error('Cannot update transfer or investment transactions');\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success('Transaction updated successfully!');\n            } else {\n                await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.createTransaction(data);\n                const transactionTypeLabel = {\n                    income: 'Income',\n                    expense: 'Expense',\n                    transfer: 'Transfer',\n                    investment_buy: 'Investment Purchase',\n                    investment_sell: 'Investment Sale',\n                    dividend: 'Dividend'\n                }[data.transaction_type] || 'Transaction';\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(transactionTypeLabel, \" added successfully!\"));\n            }\n            setShowForm(false);\n            setEditingTransaction(null);\n            setTemplateData(null);\n            setRefreshKey((prev)=>prev + 1);\n        } catch (error) {\n            console.error('Failed to save transaction:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to \".concat(editingTransaction ? 'update' : 'add', \" transaction\"));\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleEditTransaction = (transaction)=>{\n        setEditingTransaction(transaction);\n        setShowForm(true);\n    };\n    const handleCancelEdit = ()=>{\n        setEditingTransaction(null);\n        setTemplateData(null);\n        setShowForm(false);\n    };\n    const handleUseTemplate = (template)=>{\n        var _template_amount;\n        setTemplateData({\n            amount: ((_template_amount = template.amount) === null || _template_amount === void 0 ? void 0 : _template_amount.toString()) || '',\n            category_id: template.category_id || '',\n            description: template.description || '',\n            transaction_date: new Date(),\n            transaction_type: template.transaction_type || 'expense',\n            account_id: '',\n            to_account_id: '',\n            fees: '',\n            investment_symbol: '',\n            investment_quantity: '',\n            investment_price: '',\n            funding_account_id: ''\n        });\n        setShowTemplates(false);\n        setShowForm(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    currentPage: \"expenses\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-4xl font-bold text-text-primary\",\n                                                children: \"Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary text-lg mt-2\",\n                                                children: \"Track your income, expenses, transfers, and investments\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            editingTransaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCancelEdit,\n                                                className: \"bg-surface text-text-secondary border border-border px-6 py-3 rounded-lg font-medium hover:bg-surface-elevated transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Cancel Edit\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowTemplates(true),\n                                                className: \"bg-surface text-text-primary border border-border px-6 py-3 rounded-lg font-medium hover:bg-surface-elevated transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Templates\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowForm(true),\n                                                className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 4v16m8-8H4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Add Transaction\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_5__.Modal, {\n                                isOpen: showForm,\n                                onClose: ()=>{\n                                    setShowForm(false);\n                                    setEditingTransaction(null);\n                                    setTemplateData(null);\n                                },\n                                title: editingTransaction ? 'Edit Transaction' : templateData ? 'New Transaction from Template' : 'Add New Transaction',\n                                size: \"xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TransactionForm, {\n                                    onSubmit: handleSubmit,\n                                    loading: submitting,\n                                    compact: true,\n                                    initialData: editingTransaction ? {\n                                        amount: ((_editingTransaction_amount = editingTransaction.amount) === null || _editingTransaction_amount === void 0 ? void 0 : _editingTransaction_amount.toString()) || '',\n                                        category_id: editingTransaction.category_id || '',\n                                        account_id: editingTransaction.account_id || '',\n                                        to_account_id: editingTransaction.to_account_id || '',\n                                        description: editingTransaction.description || '',\n                                        transaction_date: new Date(editingTransaction.transaction_date),\n                                        transaction_type: editingTransaction.transaction_type,\n                                        fees: ((_editingTransaction_fees = editingTransaction.fees) === null || _editingTransaction_fees === void 0 ? void 0 : _editingTransaction_fees.toString()) || '',\n                                        investment_symbol: editingTransaction.investment_symbol || '',\n                                        investment_quantity: ((_editingTransaction_investment_quantity = editingTransaction.investment_quantity) === null || _editingTransaction_investment_quantity === void 0 ? void 0 : _editingTransaction_investment_quantity.toString()) || '',\n                                        investment_price: ((_editingTransaction_investment_price = editingTransaction.investment_price) === null || _editingTransaction_investment_price === void 0 ? void 0 : _editingTransaction_investment_price.toString()) || '',\n                                        funding_account_id: ''\n                                    } : templateData ? templateData : undefined\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_5__.Modal, {\n                                isOpen: showTemplates,\n                                onClose: ()=>setShowTemplates(false),\n                                title: \"Transaction Templates\",\n                                size: \"lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-text-secondary\",\n                                            children: \"Choose from your saved templates to quickly create new transactions.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionTemplates__WEBPACK_IMPORTED_MODULE_4__.TransactionTemplates, {\n                                            categories: categories,\n                                            onUseTemplate: handleUseTemplate\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onEditTransaction: handleEditTransaction\n                            }, refreshKey, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n_s(ExpensesPage, \"EvXCVCe5kclcffT/0YuzOXm+S4o=\");\n_c = ExpensesPage;\nvar _c;\n$RefreshReg$(_c, \"ExpensesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/expenses/page.tsx\n"));

/***/ })

});