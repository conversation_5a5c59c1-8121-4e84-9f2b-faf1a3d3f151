"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/../../packages/shared/src/lib/investments.ts":
/*!****************************************************!*\
  !*** ../../packages/shared/src/lib/investments.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvestmentService: () => (/* binding */ InvestmentService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _tax_calculator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tax-calculator */ \"(app-pages-browser)/../../packages/shared/src/lib/tax-calculator.ts\");\n/* harmony import */ var _assets__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./assets */ \"(app-pages-browser)/../../packages/shared/src/lib/assets.ts\");\n/* harmony import */ var _profit_loss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./profit-loss */ \"(app-pages-browser)/../../packages/shared/src/lib/profit-loss.ts\");\n\n\n\n\nclass InvestmentService {\n    /**\n   * Create an investment transaction (buy/sell)\n   * This also creates a transfer from a funding account for buy transactions\n   */ static async createInvestmentTransaction(investmentData, fundingAccountId// Required for buy transactions\n    ) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Validate investment account exists and is an investment account\n        const { data: investmentAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', investmentData.account_id).eq('user_id', user.id).single();\n        if (!investmentAccount || investmentAccount.account_type !== 'investment') {\n            throw new Error('Invalid investment account specified');\n        }\n        // For buy transactions, validate funding account and check balance\n        if (investmentData.transaction_type === 'investment_buy') {\n            if (!fundingAccountId) {\n                throw new Error('Funding account required for investment purchases');\n            }\n            const { data: fundingAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', fundingAccountId).eq('user_id', user.id).single();\n            if (!fundingAccount) {\n                throw new Error('Invalid funding account specified');\n            }\n            const totalCost = investmentData.amount + (investmentData.fees || 0);\n            if (fundingAccount.current_balance < totalCost) {\n                throw new Error('Insufficient balance in funding account');\n            }\n        }\n        // Get investment category\n        const categoryName = investmentData.transaction_type === 'investment_buy' ? 'Investment Purchase' : 'Investment Sale';\n        const { data: category } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', categoryName).eq('is_system', true).single();\n        if (!category) {\n            throw new Error(\"\".concat(categoryName, \" category not found\"));\n        }\n        // Create the investment transaction\n        const transactionData = {\n            amount: investmentData.amount,\n            description: investmentData.description || \"\".concat(investmentData.transaction_type === 'investment_buy' ? 'Buy' : 'Sell', \" \").concat(investmentData.investment_quantity, \" shares of \").concat(investmentData.investment_symbol),\n            category_id: category.id,\n            account_id: investmentData.account_id,\n            transaction_type: investmentData.transaction_type,\n            transaction_date: investmentData.transaction_date,\n            transaction_status: 'completed',\n            fees: investmentData.fees || 0,\n            investment_symbol: investmentData.investment_symbol,\n            investment_quantity: investmentData.investment_quantity,\n            investment_price: investmentData.investment_price,\n            user_id: user.id\n        };\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \").single();\n        if (transactionError) {\n            throw new Error(\"Failed to create investment transaction: \".concat(transactionError.message));\n        }\n        // For buy transactions, create a transfer from funding account to investment account\n        if (investmentData.transaction_type === 'investment_buy' && fundingAccountId) {\n            const { TransferService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\"));\n            try {\n                await TransferService.createTransfer({\n                    amount: investmentData.amount + (investmentData.fees || 0),\n                    description: \"Investment purchase: \".concat(investmentData.investment_symbol),\n                    from_account_id: fundingAccountId,\n                    to_account_id: investmentData.account_id,\n                    transaction_date: investmentData.transaction_date,\n                    fees: 0\n                });\n            } catch (error) {\n                // If transfer fails, rollback the investment transaction\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', transaction.id);\n                throw new Error(\"Failed to create funding transfer: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n            }\n        }\n        return transaction;\n    }\n    /**\n   * Create investment transaction with enhanced asset tracking and tax calculations\n   */ static async createInvestmentTransactionWithAssets(investmentData, fundingAccountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Get asset information\n        const asset = await _assets__WEBPACK_IMPORTED_MODULE_2__.AssetService.getAsset(investmentData.asset_id);\n        if (!asset) {\n            throw new Error('Asset not found');\n        }\n        // For sell transactions, calculate tax implications\n        let taxCalculation = null;\n        let realizedGainLoss = null;\n        if (investmentData.transaction_type === 'investment_sell') {\n            // Get the holding to determine purchase details\n            const holding = await _assets__WEBPACK_IMPORTED_MODULE_2__.HoldingService.getHolding(investmentData.account_id, investmentData.asset_id);\n            if (!holding) {\n                throw new Error('No holding found for this asset');\n            }\n            if (holding.quantity < investmentData.investment_quantity) {\n                throw new Error('Cannot sell more than current holding');\n            }\n            // Calculate tax implications\n            taxCalculation = await _tax_calculator__WEBPACK_IMPORTED_MODULE_1__.TaxCalculatorService.calculateCapitalGainsTax({\n                asset_class_id: asset.asset_class_id,\n                purchase_date: holding.created_at,\n                sale_date: investmentData.transaction_date,\n                purchase_price: holding.average_cost,\n                sale_price: investmentData.investment_price,\n                quantity: investmentData.investment_quantity,\n                fees: investmentData.fees\n            });\n            // Calculate realized gain/loss\n            realizedGainLoss = await _profit_loss__WEBPACK_IMPORTED_MODULE_3__.ProfitLossService.calculateRealizedGainLoss(investmentData.asset_id, investmentData.investment_quantity, investmentData.investment_price, investmentData.transaction_date, holding.average_cost, holding.created_at);\n        }\n        // Create the investment transaction using the existing method\n        const transaction = await this.createInvestmentTransaction(investmentData, fundingAccountId);\n        // Update holdings\n        const holding = await _assets__WEBPACK_IMPORTED_MODULE_2__.HoldingService.updateHolding(investmentData.account_id, investmentData.asset_id, investmentData.transaction_type === 'investment_buy' ? 'buy' : 'sell', investmentData.investment_quantity, investmentData.investment_price);\n        return {\n            transaction,\n            holding,\n            taxCalculation,\n            realizedGainLoss\n        };\n    }\n    /**\n   * Get investment transactions for a user or specific account\n   */ static async getInvestmentTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*)\\n      \", {\n            count: 'exact'\n        }).eq('user_id', user.id).in('transaction_type', [\n            'investment_buy',\n            'investment_sell'\n        ]).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            query = query.eq('account_id', options.account_id);\n        }\n        if (options === null || options === void 0 ? void 0 : options.symbol) {\n            query = query.eq('investment_symbol', options.symbol);\n        }\n        if (options === null || options === void 0 ? void 0 : options.transaction_type) {\n            query = query.eq('transaction_type', options.transaction_type);\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 20) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch investment transactions: \".concat(error.message));\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    /**\n   * Get investment holdings for a user or specific account\n   */ static async getInvestmentHoldings(accountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_holdings').select(\"\\n        *,\\n        account:accounts(*)\\n      \").order('symbol');\n        if (accountId) {\n            query = query.eq('account_id', accountId);\n        } else {\n            // Filter by user's accounts\n            const { data: userAccounts } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('id').eq('user_id', user.id).eq('account_type', 'investment');\n            if (userAccounts && userAccounts.length > 0) {\n                const accountIds = userAccounts.map((acc)=>acc.id);\n                query = query.in('account_id', accountIds);\n            } else {\n                return [];\n            }\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch investment holdings: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get portfolio summary for all investment accounts\n   */ static async getPortfolioSummary() {\n        const holdings = await this.getInvestmentHoldings();\n        let totalValue = 0;\n        let totalCost = 0;\n        const holdingsBySymbol = {};\n        holdings.forEach((holding)=>{\n            const symbol = holding.symbol;\n            const quantity = holding.quantity;\n            const avgCost = holding.average_cost;\n            const currentPrice = holding.current_price || avgCost;\n            const marketValue = quantity * currentPrice;\n            const costBasis = quantity * avgCost;\n            const gainLoss = marketValue - costBasis;\n            const gainLossPercent = costBasis > 0 ? gainLoss / costBasis * 100 : 0;\n            if (!holdingsBySymbol[symbol]) {\n                holdingsBySymbol[symbol] = {\n                    symbol,\n                    totalQuantity: 0,\n                    averageCost: 0,\n                    currentPrice,\n                    marketValue: 0,\n                    gainLoss: 0,\n                    gainLossPercent: 0\n                };\n            }\n            // Aggregate holdings for the same symbol across accounts\n            const existing = holdingsBySymbol[symbol];\n            const newTotalQuantity = existing.totalQuantity + quantity;\n            const newTotalCost = existing.totalQuantity * existing.averageCost + quantity * avgCost;\n            holdingsBySymbol[symbol] = {\n                ...existing,\n                totalQuantity: newTotalQuantity,\n                averageCost: newTotalQuantity > 0 ? newTotalCost / newTotalQuantity : 0,\n                marketValue: existing.marketValue + marketValue,\n                gainLoss: existing.gainLoss + gainLoss\n            };\n            // Recalculate percentage\n            const totalCostBasis = holdingsBySymbol[symbol].totalQuantity * holdingsBySymbol[symbol].averageCost;\n            holdingsBySymbol[symbol].gainLossPercent = totalCostBasis > 0 ? holdingsBySymbol[symbol].gainLoss / totalCostBasis * 100 : 0;\n            totalValue += marketValue;\n            totalCost += costBasis;\n        });\n        const totalGainLoss = totalValue - totalCost;\n        const totalGainLossPercent = totalCost > 0 ? totalGainLoss / totalCost * 100 : 0;\n        return {\n            totalValue,\n            totalCost,\n            totalGainLoss,\n            totalGainLossPercent,\n            holdingsBySymbol\n        };\n    }\n    /**\n   * Update current prices for holdings (would typically be called by a background job)\n   */ static async updateHoldingPrices(priceUpdates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        for (const update of priceUpdates){\n            const marketValue = update.price // Will be calculated by trigger\n            ;\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_holdings').update({\n                current_price: update.price,\n                market_value: marketValue,\n                last_updated: new Date().toISOString()\n            }).eq('symbol', update.symbol);\n            if (error) {\n                console.error(\"Failed to update price for \".concat(update.symbol, \":\"), error);\n            }\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/investments.ts\n"));

/***/ })

});