"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/app/expenses/page.tsx":
/*!***********************************!*\
  !*** ./src/app/expenses/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExpensesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var _components_TransactionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/TransactionList */ \"(app-pages-browser)/./src/components/TransactionList.tsx\");\n/* harmony import */ var _components_TransactionTemplates__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/TransactionTemplates */ \"(app-pages-browser)/./src/components/TransactionTemplates.tsx\");\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Modal */ \"(app-pages-browser)/./src/components/Modal.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ExpensesPage() {\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTemplates, setShowTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTransaction, setEditingTransaction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [templateData, setTemplateData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ExpensesPage.useEffect\": ()=>{\n            loadCategories();\n        }\n    }[\"ExpensesPage.useEffect\"], []);\n    const loadCategories = async ()=>{\n        try {\n            const categoriesData = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.CategoryService.getCategories({\n                is_active: true\n            });\n            setCategories(categoriesData);\n        } catch (error) {\n            console.error('Failed to load categories:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Failed to load categories');\n        }\n    };\n    const handleSubmit = async (data)=>{\n        setSubmitting(true);\n        try {\n            if (editingTransaction) {\n                // For basic transactions, use the update method\n                if (editingTransaction.transaction_type === 'income' || editingTransaction.transaction_type === 'expense') {\n                    await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.updateTransaction(editingTransaction.id, {\n                        amount: data.amount,\n                        description: data.description,\n                        category_id: data.category_id,\n                        transaction_date: data.transaction_date,\n                        fees: data.fees\n                    });\n                } else {\n                    throw new Error('Cannot update transfer or investment transactions');\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success('Transaction updated successfully!');\n            } else {\n                await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.createTransaction(data);\n                const transactionTypeLabel = {\n                    income: 'Income',\n                    expense: 'Expense',\n                    transfer: 'Transfer',\n                    investment_buy: 'Investment Purchase',\n                    investment_sell: 'Investment Sale',\n                    dividend: 'Dividend'\n                }[data.transaction_type] || 'Transaction';\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(transactionTypeLabel, \" added successfully!\"));\n            }\n            setShowForm(false);\n            setEditingTransaction(null);\n            setTemplateData(null);\n            setRefreshKey((prev)=>prev + 1);\n        } catch (error) {\n            console.error('Failed to save transaction:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to \".concat(editingTransaction ? 'update' : 'add', \" transaction\"));\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleEditTransaction = (transaction)=>{\n        setEditingTransaction(transaction);\n        setShowForm(true);\n    };\n    const handleCancelEdit = ()=>{\n        setEditingTransaction(null);\n        setTemplateData(null);\n        setShowForm(false);\n    };\n    const handleUseTemplate = (template)=>{\n        var _template_amount;\n        setTemplateData({\n            amount: ((_template_amount = template.amount) === null || _template_amount === void 0 ? void 0 : _template_amount.toString()) || '',\n            category_id: template.category_id || '',\n            description: template.description || '',\n            transaction_date: new Date(),\n            transaction_type: template.transaction_type || 'expense',\n            account_id: '',\n            to_account_id: '',\n            fees: '',\n            investment_symbol: '',\n            investment_quantity: '',\n            investment_price: '',\n            funding_account_id: ''\n        });\n        setShowTemplates(false);\n        setShowForm(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    currentPage: \"expenses\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-4xl font-bold text-text-primary\",\n                                                children: \"Expenses\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary text-lg mt-2\",\n                                                children: \"Track your income and expenses efficiently\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            editingTransaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCancelEdit,\n                                                className: \"bg-surface text-text-secondary border border-border px-6 py-3 rounded-lg font-medium hover:bg-surface-elevated transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Cancel Edit\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowTemplates(true),\n                                                className: \"bg-surface text-text-primary border border-border px-6 py-3 rounded-lg font-medium hover:bg-surface-elevated transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Templates\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowForm(true),\n                                                className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 4v16m8-8H4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Add Transaction\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_5__.Modal, {\n                                isOpen: showForm,\n                                onClose: ()=>{\n                                    setShowForm(false);\n                                    setEditingTransaction(null);\n                                    setTemplateData(null);\n                                },\n                                title: editingTransaction ? 'Edit Transaction' : templateData ? 'New Transaction from Template' : 'Add New Transaction',\n                                size: \"xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExpenseForm, {\n                                    onSubmit: handleSubmit,\n                                    categories: categories,\n                                    loading: submitting,\n                                    compact: true,\n                                    initialData: editingTransaction ? {\n                                        amount: editingTransaction.amount,\n                                        category_id: editingTransaction.category_id,\n                                        description: editingTransaction.description || '',\n                                        transaction_date: new Date(editingTransaction.transaction_date),\n                                        transaction_type: editingTransaction.transaction_type\n                                    } : templateData ? templateData : undefined\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_5__.Modal, {\n                                isOpen: showTemplates,\n                                onClose: ()=>setShowTemplates(false),\n                                title: \"Transaction Templates\",\n                                size: \"lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-text-secondary\",\n                                            children: \"Choose from your saved templates to quickly create new transactions.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionTemplates__WEBPACK_IMPORTED_MODULE_4__.TransactionTemplates, {\n                                            categories: categories,\n                                            onUseTemplate: handleUseTemplate\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onEditTransaction: handleEditTransaction\n                            }, refreshKey, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_s(ExpensesPage, \"UERJjLKSgP48aUuJiGmfvLSILOk=\");\n_c = ExpensesPage;\nvar _c;\n$RefreshReg$(_c, \"ExpensesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/expenses/page.tsx\n"));

/***/ })

});