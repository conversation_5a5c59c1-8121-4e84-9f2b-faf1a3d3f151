"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/components/TransactionList.tsx":
/*!********************************************!*\
  !*** ./src/components/TransactionList.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionListWeb: () => (/* binding */ TransactionListWeb),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction TransactionListWeb(param) {\n    let { onEditTransaction, key, ...props } = param;\n    _s();\n    const { formatCurrency } = (0,_repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore)();\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        searchQuery: '',\n        categoryId: '',\n        accountId: '',\n        startDate: '',\n        endDate: '',\n        transactionType: 'all'\n    });\n    const [offset, setOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasMoreItems, setHasMoreItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ITEMS_PER_PAGE = 20;\n    const loadTransactions = async function() {\n        let reset = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const currentOffset = reset ? 0 : offset;\n            if (reset) {\n                setLoading(true);\n                setError('');\n            } else {\n                setLoadingMore(true);\n            }\n            const options = {\n                limit: ITEMS_PER_PAGE,\n                offset: currentOffset,\n                ...filters.categoryId && {\n                    categoryId: filters.categoryId\n                },\n                ...filters.accountId && {\n                    accountId: filters.accountId\n                },\n                ...filters.startDate && {\n                    startDate: filters.startDate\n                },\n                ...filters.endDate && {\n                    endDate: filters.endDate\n                },\n                ...filters.transactionType !== 'all' && {\n                    transactionType: filters.transactionType\n                },\n                ...filters.searchQuery && {\n                    searchQuery: filters.searchQuery\n                },\n                includeTransfers: true,\n                includeInvestments: true\n            };\n            const result = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.getTransactions(options);\n            if (reset) {\n                setTransactions(result.data);\n                setOffset(ITEMS_PER_PAGE);\n            } else {\n                setTransactions((prev)=>[\n                        ...prev,\n                        ...result.data\n                    ]);\n                setOffset((prev)=>prev + ITEMS_PER_PAGE);\n            }\n            setTotalCount(result.count);\n            setHasMoreItems(result.data.length === ITEMS_PER_PAGE && currentOffset + ITEMS_PER_PAGE < result.count);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load transactions');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to load transactions');\n        } finally{\n            setLoading(false);\n            setLoadingMore(false);\n        }\n    };\n    const loadCategories = async ()=>{\n        try {\n            const categoriesData = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.CategoryService.getCategories({\n                is_active: true\n            });\n            setCategories(categoriesData);\n        } catch (err) {\n            console.error('Failed to load categories:', err);\n        }\n    };\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            loadCategories();\n            loadTransactions(true);\n        }\n    }[\"TransactionListWeb.useEffect\"], []);\n    // Reload when key changes (for refresh from parent)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            if (key !== undefined) {\n                loadTransactions(true);\n            }\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        key\n    ]);\n    // Reload when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            loadTransactions(true);\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        filters\n    ]);\n    const handleRefresh = ()=>{\n        loadTransactions(true);\n    };\n    const handleLoadMore = ()=>{\n        if (!loadingMore && hasMoreItems) {\n            loadTransactions(false);\n        }\n    };\n    const handleSearch = (query)=>{\n        setFilters((prev)=>({\n                ...prev,\n                searchQuery: query\n            }));\n    };\n    const handleFilterChange = (newFilters)=>{\n        setFilters((prev)=>({\n                ...prev,\n                ...newFilters\n            }));\n    };\n    const handleEdit = (transaction)=>{\n        onEditTransaction === null || onEditTransaction === void 0 ? void 0 : onEditTransaction(transaction);\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm('Are you sure you want to delete this transaction?')) {\n            return;\n        }\n        try {\n            await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.deleteTransaction(id);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success('Transaction deleted successfully');\n            loadTransactions(true);\n        } catch (err) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to delete transaction');\n        }\n    };\n    const formatDate = (date)=>new Date(date).toLocaleDateString();\n    // Quick date filter functions\n    const [activeQuickFilter, setActiveQuickFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const setQuickDateFilter = (period)=>{\n        const today = new Date();\n        let startDate;\n        switch(period){\n            case 'week':\n                startDate = new Date(today);\n                startDate.setDate(today.getDate() - 7);\n                break;\n            case 'month':\n                startDate = new Date(today);\n                startDate.setMonth(today.getMonth() - 1);\n                break;\n            case 'year':\n                startDate = new Date(today);\n                startDate.setFullYear(today.getFullYear() - 1);\n                break;\n        }\n        setActiveQuickFilter(period);\n        setFilters((prev)=>({\n                ...prev,\n                startDate: startDate.toISOString().split('T')[0],\n                endDate: today.toISOString().split('T')[0]\n            }));\n    };\n    const clearDateFilter = ()=>{\n        setActiveQuickFilter('all');\n        setFilters((prev)=>({\n                ...prev,\n                startDate: '',\n                endDate: ''\n            }));\n    };\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const loadMoreRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Sync search input with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            setSearchInput(filters.searchQuery);\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        filters.searchQuery\n    ]);\n    // Debounced search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"TransactionListWeb.useEffect.timeoutId\": ()=>{\n                    if (searchInput !== filters.searchQuery) {\n                        setFilters({\n                            \"TransactionListWeb.useEffect.timeoutId\": (prev)=>({\n                                    ...prev,\n                                    searchQuery: searchInput\n                                })\n                        }[\"TransactionListWeb.useEffect.timeoutId\"]);\n                    }\n                }\n            }[\"TransactionListWeb.useEffect.timeoutId\"], 500);\n            return ({\n                \"TransactionListWeb.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"TransactionListWeb.useEffect\"];\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        searchInput,\n        filters.searchQuery\n    ]);\n    // Infinite scroll implementation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"TransactionListWeb.useEffect\": (entries)=>{\n                    if (entries[0].isIntersecting && hasMoreItems && !loadingMore) {\n                        handleLoadMore();\n                    }\n                }\n            }[\"TransactionListWeb.useEffect\"], {\n                threshold: 0.1\n            });\n            if (loadMoreRef.current) {\n                observer.observe(loadMoreRef.current);\n            }\n            return ({\n                \"TransactionListWeb.useEffect\": ()=>observer.disconnect()\n            })[\"TransactionListWeb.useEffect\"];\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        hasMoreItems,\n        loadingMore,\n        handleLoadMore\n    ]);\n    const handleSearchSubmit = (e)=>{\n        e.preventDefault();\n        handleSearch(searchInput);\n    };\n    const clearFilters = ()=>{\n        const clearedFilters = {\n            searchQuery: '',\n            categoryId: '',\n            startDate: '',\n            endDate: '',\n            transactionType: 'all'\n        };\n        setActiveQuickFilter(null);\n        setFilters(clearedFilters);\n    };\n    const hasActiveFilters = filters.searchQuery || filters.categoryId || filters.startDate || filters.endDate || filters.transactionType !== 'all';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg border border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-5 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this),\n                                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mt-1\",\n                                        children: [\n                                            totalCount,\n                                            \" transaction\",\n                                            totalCount === 1 ? '' : 's',\n                                            \" found\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSearchSubmit,\n                                        className: \"flex-1 sm:flex-initial\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search by description or category...\",\n                                                    value: searchInput,\n                                                    onChange: (e)=>setSearchInput(e.target.value),\n                                                    className: \"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-5 w-5 text-gray-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFilters(!showFilters),\n                                        className: \"px-4 py-2 rounded-lg font-medium transition-colors \".concat(showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Filters\",\n                                                hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-red-500 text-white rounded-full w-2 h-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRefresh,\n                                        disabled: loading,\n                                        className: \"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors disabled:opacity-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 \".concat(loading ? 'animate-spin' : ''),\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this),\n                    showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Quick Date Filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('week'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'week' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last 7 Days\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('month'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'month' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last Month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('year'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'year' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last Year\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearDateFilter,\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'all' ? 'bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'),\n                                                children: \"All Time\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.categoryId,\n                                                onChange: (e)=>handleFilterChange({\n                                                        categoryId: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Categories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: category.id,\n                                                            children: [\n                                                                category.icon,\n                                                                \" \",\n                                                                category.name\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.transactionType,\n                                                onChange: (e)=>handleFilterChange({\n                                                        transactionType: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"income\",\n                                                        children: \"Income\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"expense\",\n                                                        children: \"Expense\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"From Date\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: filters.startDate,\n                                                onChange: (e)=>handleFilterChange({\n                                                        startDate: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"To Date\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: filters.endDate,\n                                                onChange: (e)=>handleFilterChange({\n                                                        endDate: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this),\n                            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearFilters,\n                                    className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n                                    children: \"Clear all filters\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 border-l-4 border-red-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 460,\n                columnNumber: 9\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Loading transactions...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 471,\n                columnNumber: 9\n            }, this) : transactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-16 px-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-12 h-12 text-gray-400\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"No transactions found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 max-w-sm mx-auto\",\n                        children: hasActiveFilters ? 'Try adjusting your filters or search terms to find more transactions' : 'Add your first expense or income to get started!'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 476,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: transactions.map((transaction)=>{\n                            var _transaction_category, _transaction_category1;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 hover:bg-gray-50 transition-colors group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl flex-shrink-0\",\n                                                    children: ((_transaction_category = transaction.category) === null || _transaction_category === void 0 ? void 0 : _transaction_category.icon) || '💰'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-900 truncate\",\n                                                                    children: ((_transaction_category1 = transaction.category) === null || _transaction_category1 === void 0 ? void 0 : _transaction_category1.name) || 'Uncategorized'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 ml-2 flex-shrink-0\",\n                                                                    children: formatDate(transaction.transaction_date)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        transaction.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 truncate\",\n                                                            children: transaction.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-semibold \".concat(transaction.transaction_type === 'income' ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            transaction.transaction_type === 'income' ? '+' : '-',\n                                                            formatCurrency(transaction.amount)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        onEditTransaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleEdit(transaction),\n                                                            className: \"p-1.5 text-gray-400 hover:text-blue-600 transition-colors\",\n                                                            title: \"Edit transaction\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-4 w-4\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleDelete(transaction.id),\n                                                            className: \"p-1.5 text-gray-400 hover:text-red-600 transition-colors\",\n                                                            title: \"Delete transaction\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-4 w-4\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 17\n                                }, this)\n                            }, transaction.id, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 11\n                    }, this),\n                    hasMoreItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: loadMoreRef,\n                        className: \"p-6 text-center border-t\",\n                        children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-gray-600\",\n                                    children: \"Loading more...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLoadMore,\n                            className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                            children: \"Load More Transactions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n_s(TransactionListWeb, \"4l0Emjm/S3khbNUhtPxFspR6QXc=\", false, function() {\n    return [\n        _repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore\n    ];\n});\n_c = TransactionListWeb;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TransactionListWeb);\nvar _c;\n$RefreshReg$(_c, \"TransactionListWeb\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TransactionList.tsx\n"));

/***/ })

});