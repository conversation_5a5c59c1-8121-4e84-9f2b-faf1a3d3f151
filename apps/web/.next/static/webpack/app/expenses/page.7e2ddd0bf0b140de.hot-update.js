"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/components/TransactionList.tsx":
/*!********************************************!*\
  !*** ./src/components/TransactionList.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionListWeb: () => (/* binding */ TransactionListWeb),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction TransactionListWeb(param) {\n    let { onEditTransaction, key, ...props } = param;\n    _s();\n    const { formatCurrency } = (0,_repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore)();\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        searchQuery: '',\n        categoryId: '',\n        accountId: '',\n        startDate: '',\n        endDate: '',\n        transactionType: 'all'\n    });\n    const [offset, setOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasMoreItems, setHasMoreItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ITEMS_PER_PAGE = 20;\n    const loadTransactions = async function() {\n        let reset = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const currentOffset = reset ? 0 : offset;\n            if (reset) {\n                setLoading(true);\n                setError('');\n            } else {\n                setLoadingMore(true);\n            }\n            const options = {\n                limit: ITEMS_PER_PAGE,\n                offset: currentOffset,\n                ...filters.categoryId && {\n                    categoryId: filters.categoryId\n                },\n                ...filters.accountId && {\n                    accountId: filters.accountId\n                },\n                ...filters.startDate && {\n                    startDate: filters.startDate\n                },\n                ...filters.endDate && {\n                    endDate: filters.endDate\n                },\n                ...filters.transactionType !== 'all' && {\n                    transactionType: filters.transactionType\n                },\n                ...filters.searchQuery && {\n                    searchQuery: filters.searchQuery\n                },\n                includeTransfers: true,\n                includeInvestments: true\n            };\n            const result = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.getTransactions(options);\n            if (reset) {\n                setTransactions(result.data);\n                setOffset(ITEMS_PER_PAGE);\n            } else {\n                setTransactions((prev)=>[\n                        ...prev,\n                        ...result.data\n                    ]);\n                setOffset((prev)=>prev + ITEMS_PER_PAGE);\n            }\n            setTotalCount(result.count);\n            setHasMoreItems(result.data.length === ITEMS_PER_PAGE && currentOffset + ITEMS_PER_PAGE < result.count);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load transactions');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to load transactions');\n        } finally{\n            setLoading(false);\n            setLoadingMore(false);\n        }\n    };\n    const loadCategories = async ()=>{\n        try {\n            const categoriesData = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.CategoryService.getCategories({\n                is_active: true\n            });\n            setCategories(categoriesData);\n        } catch (err) {\n            console.error('Failed to load categories:', err);\n        }\n    };\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            loadCategories();\n            loadTransactions(true);\n        }\n    }[\"TransactionListWeb.useEffect\"], []);\n    // Reload when key changes (for refresh from parent)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            if (key !== undefined) {\n                loadTransactions(true);\n            }\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        key\n    ]);\n    // Reload when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            loadTransactions(true);\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        filters\n    ]);\n    const handleRefresh = ()=>{\n        loadTransactions(true);\n    };\n    const handleLoadMore = ()=>{\n        if (!loadingMore && hasMoreItems) {\n            loadTransactions(false);\n        }\n    };\n    const handleSearch = (query)=>{\n        setFilters((prev)=>({\n                ...prev,\n                searchQuery: query\n            }));\n    };\n    const handleFilterChange = (newFilters)=>{\n        setFilters((prev)=>({\n                ...prev,\n                ...newFilters\n            }));\n    };\n    const handleEdit = (transaction)=>{\n        onEditTransaction === null || onEditTransaction === void 0 ? void 0 : onEditTransaction(transaction);\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm('Are you sure you want to delete this transaction?')) {\n            return;\n        }\n        try {\n            await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.deleteTransaction(id);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success('Transaction deleted successfully');\n            loadTransactions(true);\n        } catch (err) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to delete transaction');\n        }\n    };\n    const formatDate = (date)=>new Date(date).toLocaleDateString();\n    // Helper function to get transaction display info\n    const getTransactionDisplayInfo = (transaction)=>{\n        switch(transaction.transaction_type){\n            case 'income':\n                var _transaction_category, _transaction_category1;\n                return {\n                    icon: ((_transaction_category = transaction.category) === null || _transaction_category === void 0 ? void 0 : _transaction_category.icon) || '💰',\n                    title: ((_transaction_category1 = transaction.category) === null || _transaction_category1 === void 0 ? void 0 : _transaction_category1.name) || 'Income',\n                    color: 'text-green-600',\n                    sign: '+',\n                    description: transaction.description\n                };\n            case 'expense':\n                var _transaction_category2, _transaction_category3;\n                return {\n                    icon: ((_transaction_category2 = transaction.category) === null || _transaction_category2 === void 0 ? void 0 : _transaction_category2.icon) || '💸',\n                    title: ((_transaction_category3 = transaction.category) === null || _transaction_category3 === void 0 ? void 0 : _transaction_category3.name) || 'Expense',\n                    color: 'text-red-600',\n                    sign: '-',\n                    description: transaction.description\n                };\n            case 'transfer':\n                var _transaction_account, _transaction_to_account;\n                return {\n                    icon: '🔄',\n                    title: \"Transfer \".concat(((_transaction_account = transaction.account) === null || _transaction_account === void 0 ? void 0 : _transaction_account.name) ? \"from \".concat(transaction.account.name) : '').concat(((_transaction_to_account = transaction.to_account) === null || _transaction_to_account === void 0 ? void 0 : _transaction_to_account.name) ? \" to \".concat(transaction.to_account.name) : ''),\n                    color: 'text-blue-600',\n                    sign: '',\n                    description: transaction.description || 'Account transfer'\n                };\n            case 'investment_buy':\n                return {\n                    icon: '📈',\n                    title: \"Buy \".concat(transaction.investment_symbol),\n                    color: 'text-purple-600',\n                    sign: '-',\n                    description: transaction.description || \"\".concat(transaction.investment_quantity, \" shares at \").concat(formatCurrency(transaction.investment_price || 0))\n                };\n            case 'investment_sell':\n                return {\n                    icon: '📉',\n                    title: \"Sell \".concat(transaction.investment_symbol),\n                    color: 'text-orange-600',\n                    sign: '+',\n                    description: transaction.description || \"\".concat(transaction.investment_quantity, \" shares at \").concat(formatCurrency(transaction.investment_price || 0))\n                };\n            case 'dividend':\n                return {\n                    icon: '💎',\n                    title: \"Dividend from \".concat(transaction.investment_symbol),\n                    color: 'text-green-600',\n                    sign: '+',\n                    description: transaction.description || 'Dividend payment'\n                };\n            default:\n                return {\n                    icon: '💰',\n                    title: 'Transaction',\n                    color: 'text-gray-600',\n                    sign: '',\n                    description: transaction.description\n                };\n        }\n    };\n    // Quick date filter functions\n    const [activeQuickFilter, setActiveQuickFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const setQuickDateFilter = (period)=>{\n        const today = new Date();\n        let startDate;\n        switch(period){\n            case 'week':\n                startDate = new Date(today);\n                startDate.setDate(today.getDate() - 7);\n                break;\n            case 'month':\n                startDate = new Date(today);\n                startDate.setMonth(today.getMonth() - 1);\n                break;\n            case 'year':\n                startDate = new Date(today);\n                startDate.setFullYear(today.getFullYear() - 1);\n                break;\n        }\n        setActiveQuickFilter(period);\n        setFilters((prev)=>({\n                ...prev,\n                startDate: startDate.toISOString().split('T')[0],\n                endDate: today.toISOString().split('T')[0]\n            }));\n    };\n    const clearDateFilter = ()=>{\n        setActiveQuickFilter('all');\n        setFilters((prev)=>({\n                ...prev,\n                startDate: '',\n                endDate: ''\n            }));\n    };\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const loadMoreRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Sync search input with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            setSearchInput(filters.searchQuery);\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        filters.searchQuery\n    ]);\n    // Debounced search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"TransactionListWeb.useEffect.timeoutId\": ()=>{\n                    if (searchInput !== filters.searchQuery) {\n                        setFilters({\n                            \"TransactionListWeb.useEffect.timeoutId\": (prev)=>({\n                                    ...prev,\n                                    searchQuery: searchInput\n                                })\n                        }[\"TransactionListWeb.useEffect.timeoutId\"]);\n                    }\n                }\n            }[\"TransactionListWeb.useEffect.timeoutId\"], 500);\n            return ({\n                \"TransactionListWeb.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"TransactionListWeb.useEffect\"];\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        searchInput,\n        filters.searchQuery\n    ]);\n    // Infinite scroll implementation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"TransactionListWeb.useEffect\": (entries)=>{\n                    if (entries[0].isIntersecting && hasMoreItems && !loadingMore) {\n                        handleLoadMore();\n                    }\n                }\n            }[\"TransactionListWeb.useEffect\"], {\n                threshold: 0.1\n            });\n            if (loadMoreRef.current) {\n                observer.observe(loadMoreRef.current);\n            }\n            return ({\n                \"TransactionListWeb.useEffect\": ()=>observer.disconnect()\n            })[\"TransactionListWeb.useEffect\"];\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        hasMoreItems,\n        loadingMore,\n        handleLoadMore\n    ]);\n    const handleSearchSubmit = (e)=>{\n        e.preventDefault();\n        handleSearch(searchInput);\n    };\n    const clearFilters = ()=>{\n        const clearedFilters = {\n            searchQuery: '',\n            categoryId: '',\n            accountId: '',\n            startDate: '',\n            endDate: '',\n            transactionType: 'all'\n        };\n        setActiveQuickFilter(null);\n        setFilters(clearedFilters);\n    };\n    const hasActiveFilters = filters.searchQuery || filters.categoryId || filters.accountId || filters.startDate || filters.endDate || filters.transactionType !== 'all';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg border border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-5 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this),\n                                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mt-1\",\n                                        children: [\n                                            totalCount,\n                                            \" transaction\",\n                                            totalCount === 1 ? '' : 's',\n                                            \" found\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSearchSubmit,\n                                        className: \"flex-1 sm:flex-initial\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search by description or category...\",\n                                                    value: searchInput,\n                                                    onChange: (e)=>setSearchInput(e.target.value),\n                                                    className: \"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-5 w-5 text-gray-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFilters(!showFilters),\n                                        className: \"px-4 py-2 rounded-lg font-medium transition-colors \".concat(showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Filters\",\n                                                hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-red-500 text-white rounded-full w-2 h-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRefresh,\n                                        disabled: loading,\n                                        className: \"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors disabled:opacity-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 \".concat(loading ? 'animate-spin' : ''),\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Quick Date Filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('week'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'week' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last 7 Days\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('month'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'month' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last Month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('year'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'year' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last Year\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearDateFilter,\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'all' ? 'bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'),\n                                                children: \"All Time\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.categoryId,\n                                                onChange: (e)=>handleFilterChange({\n                                                        categoryId: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Categories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: category.id,\n                                                            children: [\n                                                                category.icon,\n                                                                \" \",\n                                                                category.name\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.transactionType,\n                                                onChange: (e)=>handleFilterChange({\n                                                        transactionType: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"income\",\n                                                        children: \"Income\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"expense\",\n                                                        children: \"Expense\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"transfer\",\n                                                        children: \"Transfer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"investment_buy\",\n                                                        children: \"Investment Purchase\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"investment_sell\",\n                                                        children: \"Investment Sale\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"dividend\",\n                                                        children: \"Dividend\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"From Date\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: filters.startDate,\n                                                onChange: (e)=>handleFilterChange({\n                                                        startDate: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"To Date\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: filters.endDate,\n                                                onChange: (e)=>handleFilterChange({\n                                                        endDate: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this),\n                            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearFilters,\n                                    className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n                                    children: \"Clear all filters\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 border-l-4 border-red-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                    lineNumber: 528,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 527,\n                columnNumber: 9\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Loading transactions...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 538,\n                columnNumber: 9\n            }, this) : transactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-16 px-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-12 h-12 text-gray-400\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"No transactions found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 max-w-sm mx-auto\",\n                        children: hasActiveFilters ? 'Try adjusting your filters or search terms to find more transactions' : 'Add your first expense or income to get started!'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 550,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 543,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: transactions.map((transaction)=>{\n                            var _transaction_category, _transaction_category1;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 hover:bg-gray-50 transition-colors group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl flex-shrink-0\",\n                                                    children: ((_transaction_category = transaction.category) === null || _transaction_category === void 0 ? void 0 : _transaction_category.icon) || '💰'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-900 truncate\",\n                                                                    children: ((_transaction_category1 = transaction.category) === null || _transaction_category1 === void 0 ? void 0 : _transaction_category1.name) || 'Uncategorized'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 ml-2 flex-shrink-0\",\n                                                                    children: formatDate(transaction.transaction_date)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        transaction.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 truncate\",\n                                                            children: transaction.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-semibold \".concat(transaction.transaction_type === 'income' ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            transaction.transaction_type === 'income' ? '+' : '-',\n                                                            formatCurrency(transaction.amount)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        onEditTransaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleEdit(transaction),\n                                                            className: \"p-1.5 text-gray-400 hover:text-blue-600 transition-colors\",\n                                                            title: \"Edit transaction\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-4 w-4\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleDelete(transaction.id),\n                                                            className: \"p-1.5 text-gray-400 hover:text-red-600 transition-colors\",\n                                                            title: \"Delete transaction\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-4 w-4\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 17\n                                }, this)\n                            }, transaction.id, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 11\n                    }, this),\n                    hasMoreItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: loadMoreRef,\n                        className: \"p-6 text-center border-t\",\n                        children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-gray-600\",\n                                    children: \"Loading more...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLoadMore,\n                            className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                            children: \"Load More Transactions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, this);\n}\n_s(TransactionListWeb, \"4l0Emjm/S3khbNUhtPxFspR6QXc=\", false, function() {\n    return [\n        _repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore\n    ];\n});\n_c = TransactionListWeb;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TransactionListWeb);\nvar _c;\n$RefreshReg$(_c, \"TransactionListWeb\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TransactionList.tsx\n"));

/***/ })

});