"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/app/expenses/page.tsx":
/*!***********************************!*\
  !*** ./src/app/expenses/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExpensesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var _components_TabbedTransactionForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/TabbedTransactionForm */ \"(app-pages-browser)/./src/components/TabbedTransactionForm.tsx\");\n/* harmony import */ var _components_InvestmentForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/InvestmentForm */ \"(app-pages-browser)/./src/components/InvestmentForm.tsx\");\n/* harmony import */ var _components_TransactionList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/TransactionList */ \"(app-pages-browser)/./src/components/TransactionList.tsx\");\n/* harmony import */ var _components_TransactionTemplates__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/TransactionTemplates */ \"(app-pages-browser)/./src/components/TransactionTemplates.tsx\");\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Modal */ \"(app-pages-browser)/./src/components/Modal.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ExpensesPage() {\n    var _editingTransaction_amount, _editingTransaction_fees;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showInvestmentForm, setShowInvestmentForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTemplates, setShowTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTransaction, setEditingTransaction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [templateData, setTemplateData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ExpensesPage.useEffect\": ()=>{\n            loadCategories();\n        }\n    }[\"ExpensesPage.useEffect\"], []);\n    const loadCategories = async ()=>{\n        try {\n            const categoriesData = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.CategoryService.getCategories({\n                is_active: true\n            });\n            setCategories(categoriesData);\n        } catch (error) {\n            console.error('Failed to load categories:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.toast.error('Failed to load categories');\n        }\n    };\n    const handleSubmit = async (data)=>{\n        setSubmitting(true);\n        try {\n            if (editingTransaction) {\n                // For basic transactions, use the update method\n                if (editingTransaction.transaction_type === 'income' || editingTransaction.transaction_type === 'expense') {\n                    await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.updateTransaction(editingTransaction.id, {\n                        amount: data.amount,\n                        description: data.description,\n                        category_id: data.category_id,\n                        transaction_date: data.transaction_date,\n                        fees: data.fees\n                    });\n                } else {\n                    throw new Error('Cannot update transfer or investment transactions');\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.toast.success('Transaction updated successfully!');\n            } else {\n                await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.TransactionService.createTransaction(data);\n                const transactionTypeLabel = {\n                    income: 'Income',\n                    expense: 'Expense',\n                    transfer: 'Transfer'\n                }[data.transaction_type] || 'Transaction';\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"\".concat(transactionTypeLabel, \" added successfully!\"));\n            }\n            setShowForm(false);\n            setEditingTransaction(null);\n            setTemplateData(null);\n            setRefreshKey((prev)=>prev + 1);\n        } catch (error) {\n            console.error('Failed to save transaction:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to \".concat(editingTransaction ? 'update' : 'add', \" transaction\"));\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleInvestmentSubmit = async (data)=>{\n        setSubmitting(true);\n        try {\n            // Investment transactions are handled by the InvestmentForm component\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.toast.success('Investment transaction created successfully!');\n            setShowInvestmentForm(false);\n            setRefreshKey((prev)=>prev + 1);\n        } catch (error) {\n            console.error('Failed to save investment transaction:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.toast.error('Failed to add investment transaction');\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleEditTransaction = (transaction)=>{\n        setEditingTransaction(transaction);\n        setShowForm(true);\n    };\n    const handleCancelEdit = ()=>{\n        setEditingTransaction(null);\n        setTemplateData(null);\n        setShowForm(false);\n    };\n    const handleUseTemplate = (template)=>{\n        var _template_amount;\n        setTemplateData({\n            amount: ((_template_amount = template.amount) === null || _template_amount === void 0 ? void 0 : _template_amount.toString()) || '',\n            category_id: template.category_id || '',\n            description: template.description || '',\n            transaction_date: new Date(),\n            transaction_type: template.transaction_type || 'expense',\n            account_id: '',\n            to_account_id: '',\n            fees: '',\n            investment_symbol: '',\n            investment_quantity: '',\n            investment_price: '',\n            funding_account_id: ''\n        });\n        setShowTemplates(false);\n        setShowForm(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    currentPage: \"expenses\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-4xl font-bold text-text-primary\",\n                                                children: \"Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary text-lg mt-2\",\n                                                children: \"Track your income, expenses, transfers, and investments\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            editingTransaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCancelEdit,\n                                                className: \"bg-surface text-text-secondary border border-border px-6 py-3 rounded-lg font-medium hover:bg-surface-elevated transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Cancel Edit\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowTemplates(true),\n                                                className: \"bg-surface text-text-primary border border-border px-6 py-3 rounded-lg font-medium hover:bg-surface-elevated transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Templates\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowInvestmentForm(true),\n                                                className: \"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Investments\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowForm(true),\n                                                className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 4v16m8-8H4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Add Transaction\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n                                isOpen: showForm,\n                                onClose: ()=>{\n                                    setShowForm(false);\n                                    setEditingTransaction(null);\n                                    setTemplateData(null);\n                                },\n                                title: editingTransaction ? 'Edit Transaction' : templateData ? 'New Transaction from Template' : 'Add New Transaction',\n                                size: \"xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TabbedTransactionForm__WEBPACK_IMPORTED_MODULE_3__.TabbedTransactionForm, {\n                                    onSubmit: handleSubmit,\n                                    loading: submitting,\n                                    compact: true,\n                                    initialData: editingTransaction ? {\n                                        amount: ((_editingTransaction_amount = editingTransaction.amount) === null || _editingTransaction_amount === void 0 ? void 0 : _editingTransaction_amount.toString()) || '',\n                                        category_id: editingTransaction.category_id || '',\n                                        account_id: editingTransaction.account_id || '',\n                                        to_account_id: editingTransaction.to_account_id || '',\n                                        description: editingTransaction.description || '',\n                                        transaction_date: new Date(editingTransaction.transaction_date),\n                                        transaction_type: editingTransaction.transaction_type,\n                                        fees: ((_editingTransaction_fees = editingTransaction.fees) === null || _editingTransaction_fees === void 0 ? void 0 : _editingTransaction_fees.toString()) || ''\n                                    } : templateData ? templateData : undefined\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n                                isOpen: showInvestmentForm,\n                                onClose: ()=>setShowInvestmentForm(false),\n                                title: \"Investment Management\",\n                                size: \"xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvestmentForm__WEBPACK_IMPORTED_MODULE_4__.InvestmentForm, {\n                                    onSubmit: handleInvestmentSubmit,\n                                    loading: submitting,\n                                    compact: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n                                isOpen: showTemplates,\n                                onClose: ()=>setShowTemplates(false),\n                                title: \"Transaction Templates\",\n                                size: \"lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-text-secondary\",\n                                            children: \"Choose from your saved templates to quickly create new transactions.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionTemplates__WEBPACK_IMPORTED_MODULE_6__.TransactionTemplates, {\n                                            categories: categories,\n                                            onUseTemplate: handleUseTemplate\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionList__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                onEditTransaction: handleEditTransaction\n                            }, refreshKey, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n_s(ExpensesPage, \"EvXCVCe5kclcffT/0YuzOXm+S4o=\");\n_c = ExpensesPage;\nvar _c;\n$RefreshReg$(_c, \"ExpensesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/expenses/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/InvestmentForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/InvestmentForm.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvestmentForm: () => (/* binding */ InvestmentForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/../../node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/../../node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/../../node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _shared_index__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @shared/index */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Investment form schema\nconst investmentFormSchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    transaction_type: zod__WEBPACK_IMPORTED_MODULE_3__.z[\"enum\"]([\n        'investment_buy',\n        'investment_sell',\n        'dividend'\n    ]),\n    amount: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(1, 'Amount is required').transform((val)=>parseFloat(val)),\n    investment_account_id: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().uuid('Please select an investment account'),\n    funding_account_id: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().uuid('Please select a funding account').optional(),\n    investment_symbol: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(1, 'Stock symbol is required').optional(),\n    investment_quantity: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().transform((val)=>val ? parseFloat(val) : undefined).optional(),\n    investment_price: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().transform((val)=>val ? parseFloat(val) : undefined).optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    transaction_date: zod__WEBPACK_IMPORTED_MODULE_3__.z.date(),\n    fees: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().transform((val)=>val ? parseFloat(val) : 0)\n}).refine((data)=>{\n    if (data.transaction_type === 'investment_buy' || data.transaction_type === 'investment_sell') {\n        return data.investment_symbol && data.investment_quantity && data.investment_price;\n    }\n    return true;\n}, {\n    message: \"Stock symbol, quantity, and price are required for buy/sell transactions\",\n    path: [\n        \"investment_symbol\"\n    ]\n});\nconst InvestmentForm = (param)=>{\n    let { onSubmit, loading = false, className = \"\", initialData, compact = false } = param;\n    var _initialData_amount, _initialData_investment_quantity, _initialData_investment_price, _initialData_fees;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((initialData === null || initialData === void 0 ? void 0 : initialData.transaction_type) === 'investment_sell' ? 'sell' : (initialData === null || initialData === void 0 ? void 0 : initialData.transaction_type) === 'dividend' ? 'dividend' : 'buy');\n    const [accounts, setAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(investmentFormSchema),\n        defaultValues: {\n            transaction_type: (initialData === null || initialData === void 0 ? void 0 : initialData.transaction_type) || 'investment_buy',\n            amount: (initialData === null || initialData === void 0 ? void 0 : (_initialData_amount = initialData.amount) === null || _initialData_amount === void 0 ? void 0 : _initialData_amount.toString()) || '',\n            investment_account_id: (initialData === null || initialData === void 0 ? void 0 : initialData.account_id) || '',\n            funding_account_id: (initialData === null || initialData === void 0 ? void 0 : initialData.funding_account_id) || '',\n            investment_symbol: (initialData === null || initialData === void 0 ? void 0 : initialData.investment_symbol) || '',\n            investment_quantity: (initialData === null || initialData === void 0 ? void 0 : (_initialData_investment_quantity = initialData.investment_quantity) === null || _initialData_investment_quantity === void 0 ? void 0 : _initialData_investment_quantity.toString()) || '',\n            investment_price: (initialData === null || initialData === void 0 ? void 0 : (_initialData_investment_price = initialData.investment_price) === null || _initialData_investment_price === void 0 ? void 0 : _initialData_investment_price.toString()) || '',\n            description: (initialData === null || initialData === void 0 ? void 0 : initialData.description) || '',\n            transaction_date: (initialData === null || initialData === void 0 ? void 0 : initialData.transaction_date) || new Date(),\n            fees: (initialData === null || initialData === void 0 ? void 0 : (_initialData_fees = initialData.fees) === null || _initialData_fees === void 0 ? void 0 : _initialData_fees.toString()) || ''\n        }\n    });\n    const { control, handleSubmit, reset, setValue, watch, formState: { errors, isSubmitting } } = form;\n    const transactionType = watch('transaction_type');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvestmentForm.useEffect\": ()=>{\n            const loadAccounts = {\n                \"InvestmentForm.useEffect.loadAccounts\": async ()=>{\n                    try {\n                        const accountsData = await _shared_index__WEBPACK_IMPORTED_MODULE_4__.AccountService.getAccounts();\n                        setAccounts(accountsData);\n                    } catch (error) {\n                        console.error('Error loading accounts:', error);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('Failed to load accounts');\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"InvestmentForm.useEffect.loadAccounts\"];\n            loadAccounts();\n        }\n    }[\"InvestmentForm.useEffect\"], []);\n    // Update transaction type when tab changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvestmentForm.useEffect\": ()=>{\n            const transactionTypeMap = {\n                'buy': 'investment_buy',\n                'sell': 'investment_sell',\n                'dividend': 'dividend'\n            };\n            setValue('transaction_type', transactionTypeMap[activeTab]);\n        }\n    }[\"InvestmentForm.useEffect\"], [\n        activeTab,\n        setValue\n    ]);\n    // Filter accounts based on type\n    const getInvestmentAccounts = ()=>{\n        return accounts.filter((acc)=>acc.account_type === 'investment');\n    };\n    const getFundingAccounts = ()=>{\n        return accounts.filter((acc)=>acc.account_type !== 'investment');\n    };\n    const handleFormSubmit = async (data)=>{\n        try {\n            if (data.transaction_type === 'dividend') {\n                // Handle dividend as a transfer from investment account to funding account\n                await _shared_index__WEBPACK_IMPORTED_MODULE_4__.TransferService.createTransfer({\n                    amount: data.amount,\n                    description: data.description || \"Dividend payment: \".concat(data.investment_symbol || 'Investment'),\n                    from_account_id: data.investment_account_id,\n                    to_account_id: data.funding_account_id,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees || 0\n                });\n            } else {\n                // Handle investment buy/sell\n                await _shared_index__WEBPACK_IMPORTED_MODULE_4__.InvestmentService.createInvestmentTransaction({\n                    amount: data.amount,\n                    description: data.description,\n                    account_id: data.investment_account_id,\n                    investment_symbol: data.investment_symbol,\n                    investment_quantity: data.investment_quantity,\n                    investment_price: data.investment_price,\n                    transaction_type: data.transaction_type,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees || 0\n                }, data.transaction_type === 'investment_buy' ? data.funding_account_id : undefined);\n            }\n            await onSubmit(data);\n            if (!initialData) {\n                reset();\n            }\n        } catch (error) {\n            console.error('Error submitting investment transaction:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('Failed to submit investment transaction');\n        }\n    };\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-32\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-4 border-border border-t-primary-blue\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, undefined);\n    }\n    const tabs = [\n        {\n            id: 'buy',\n            label: 'Buy',\n            icon: '📈'\n        },\n        {\n            id: 'sell',\n            label: 'Sell',\n            icon: '📉'\n        },\n        {\n            id: 'dividend',\n            label: 'Dividend',\n            icon: '💰'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-1 bg-surface-secondary rounded-lg p-1 mb-6\",\n                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>setActiveTab(tab.id),\n                        className: \"flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-md text-sm font-medium transition-all \".concat(activeTab === tab.id ? 'bg-primary-blue text-white shadow-sm' : 'text-text-secondary hover:text-text-primary hover:bg-surface'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: tab.icon\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: tab.label\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, tab.id, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit(handleFormSubmit),\n                className: \"\".concat(compact ? 'space-y-4' : 'space-y-6'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-text-primary\",\n                                children: \"Investment Account *\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_6__.Controller, {\n                                name: \"investment_account_id\",\n                                control: control,\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        ...field,\n                                        className: \"w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary \".concat(errors.investment_account_id ? 'border-error-red' : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select investment account\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, void 0),\n                                            getInvestmentAccounts().map((account)=>{\n                                                var _account_current_balance;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: account.id,\n                                                    children: [\n                                                        account.name,\n                                                        \" - $\",\n                                                        (_account_current_balance = account.current_balance) === null || _account_current_balance === void 0 ? void 0 : _account_current_balance.toFixed(2)\n                                                    ]\n                                                }, account.id, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined),\n                            errors.investment_account_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-error-red\",\n                                children: errors.investment_account_id.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined),\n                    (activeTab === 'buy' || activeTab === 'dividend') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-text-primary\",\n                                children: activeTab === 'buy' ? 'Funding Account *' : 'Receiving Account *'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_6__.Controller, {\n                                name: \"funding_account_id\",\n                                control: control,\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        ...field,\n                                        className: \"w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary \".concat(errors.funding_account_id ? 'border-error-red' : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: [\n                                                    \"Select \",\n                                                    activeTab === 'buy' ? 'funding' : 'receiving',\n                                                    \" account\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            getFundingAccounts().map((account)=>{\n                                                var _account_current_balance;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: account.id,\n                                                    children: [\n                                                        account.name,\n                                                        \" (\",\n                                                        account.account_type,\n                                                        \") - $\",\n                                                        (_account_current_balance = account.current_balance) === null || _account_current_balance === void 0 ? void 0 : _account_current_balance.toFixed(2)\n                                                    ]\n                                                }, account.id, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, void 0);\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, undefined),\n                            errors.funding_account_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-error-red\",\n                                children: errors.funding_account_id.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab !== 'dividend' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-text-primary\",\n                                children: \"Stock Symbol *\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_6__.Controller, {\n                                name: \"investment_symbol\",\n                                control: control,\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        ...field,\n                                        placeholder: \"e.g., AAPL, GOOGL\",\n                                        className: \"w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary \".concat(errors.investment_symbol ? 'border-error-red' : '')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, undefined),\n                            errors.investment_symbol && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-error-red\",\n                                children: errors.investment_symbol.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab !== 'dividend' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-text-primary\",\n                                        children: \"Quantity *\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_6__.Controller, {\n                                        name: \"investment_quantity\",\n                                        control: control,\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                inputMode: \"decimal\",\n                                                ...field,\n                                                placeholder: \"0\",\n                                                className: \"w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary \".concat(errors.investment_quantity ? 'border-error-red' : '')\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, void 0);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    errors.investment_quantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-error-red\",\n                                        children: errors.investment_quantity.message\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-text-primary\",\n                                        children: \"Price per Share *\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_6__.Controller, {\n                                        name: \"investment_price\",\n                                        control: control,\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                inputMode: \"decimal\",\n                                                ...field,\n                                                placeholder: \"0.00\",\n                                                className: \"w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary \".concat(errors.investment_price ? 'border-error-red' : '')\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, void 0);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    errors.investment_price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-error-red\",\n                                        children: errors.investment_price.message\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-text-primary\",\n                                children: activeTab === 'dividend' ? 'Dividend Amount *' : 'Total Amount *'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_6__.Controller, {\n                                name: \"amount\",\n                                control: control,\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        inputMode: \"decimal\",\n                                        ...field,\n                                        placeholder: \"0.00\",\n                                        className: \"w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary \".concat(errors.amount ? 'border-error-red' : '')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, undefined),\n                            errors.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-error-red\",\n                                children: errors.amount.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-text-primary\",\n                                children: \"Fees\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_6__.Controller, {\n                                name: \"fees\",\n                                control: control,\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        inputMode: \"decimal\",\n                                        ...field,\n                                        placeholder: \"0.00\",\n                                        className: \"w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary \".concat(errors.fees ? 'border-error-red' : '')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, undefined),\n                            errors.fees && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-error-red\",\n                                children: errors.fees.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-text-primary\",\n                                children: \"Description\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_6__.Controller, {\n                                name: \"description\",\n                                control: control,\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ...field,\n                                        rows: 3,\n                                        placeholder: \"Add a note about this investment transaction...\",\n                                        className: \"w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary resize-none\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-text-primary\",\n                                children: \"Date *\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_6__.Controller, {\n                                name: \"transaction_date\",\n                                control: control,\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        ...field,\n                                        value: field.value instanceof Date ? field.value.toISOString().split('T')[0] : field.value,\n                                        onChange: (e)=>field.onChange(new Date(e.target.value)),\n                                        className: \"w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary \".concat(errors.transaction_date ? 'border-error-red' : '')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, undefined),\n                            errors.transaction_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-error-red\",\n                                children: errors.transaction_date.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isSubmitting || loading,\n                            className: \"w-full bg-primary-blue hover:bg-primary-blue/90 disabled:bg-primary-blue/50 text-white font-medium py-3 px-4 rounded-lg transition-colors\",\n                            children: isSubmitting || loading ? 'Processing...' : initialData ? 'Update Investment' : activeTab === 'buy' ? 'Buy Investment' : activeTab === 'sell' ? 'Sell Investment' : 'Record Dividend'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/InvestmentForm.tsx\",\n        lineNumber: 175,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InvestmentForm, \"EloCZwM0saht6+JT1KGCcECXk1M=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm\n    ];\n});\n_c = InvestmentForm;\nvar _c;\n$RefreshReg$(_c, \"InvestmentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0ludmVzdG1lbnRGb3JtLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUFrRDtBQUNHO0FBQ0E7QUFDOUI7QUFNRDtBQUNhO0FBRW5DLHlCQUF5QjtBQUN6QixNQUFNVyx1QkFBdUJMLHlDQUFRLENBQUM7SUFDcENPLGtCQUFrQlAsMENBQU0sQ0FBQztRQUFDO1FBQWtCO1FBQW1CO0tBQVc7SUFDMUVTLFFBQVFULHlDQUFRLEdBQUdXLEdBQUcsQ0FBQyxHQUFHLHNCQUFzQkMsU0FBUyxDQUFDQyxDQUFBQSxNQUFPQyxXQUFXRDtJQUM1RUUsdUJBQXVCZix5Q0FBUSxHQUFHZ0IsSUFBSSxDQUFDO0lBQ3ZDQyxvQkFBb0JqQix5Q0FBUSxHQUFHZ0IsSUFBSSxDQUFDLG1DQUFtQ0UsUUFBUTtJQUMvRUMsbUJBQW1CbkIseUNBQVEsR0FBR1csR0FBRyxDQUFDLEdBQUcsNEJBQTRCTyxRQUFRO0lBQ3pFRSxxQkFBcUJwQix5Q0FBUSxHQUFHWSxTQUFTLENBQUNDLENBQUFBLE1BQU9BLE1BQU1DLFdBQVdELE9BQU9RLFdBQVdILFFBQVE7SUFDNUZJLGtCQUFrQnRCLHlDQUFRLEdBQUdZLFNBQVMsQ0FBQ0MsQ0FBQUEsTUFBT0EsTUFBTUMsV0FBV0QsT0FBT1EsV0FBV0gsUUFBUTtJQUN6RkssYUFBYXZCLHlDQUFRLEdBQUdrQixRQUFRO0lBQ2hDTSxrQkFBa0J4Qix1Q0FBTTtJQUN4QjBCLE1BQU0xQix5Q0FBUSxHQUFHWSxTQUFTLENBQUNDLENBQUFBLE1BQU9BLE1BQU1DLFdBQVdELE9BQU87QUFDNUQsR0FBR2MsTUFBTSxDQUFDLENBQUNDO0lBQ1QsSUFBSUEsS0FBS3JCLGdCQUFnQixLQUFLLG9CQUFvQnFCLEtBQUtyQixnQkFBZ0IsS0FBSyxtQkFBbUI7UUFDN0YsT0FBT3FCLEtBQUtULGlCQUFpQixJQUFJUyxLQUFLUixtQkFBbUIsSUFBSVEsS0FBS04sZ0JBQWdCO0lBQ3BGO0lBQ0EsT0FBTztBQUNULEdBQUc7SUFDRE8sU0FBUztJQUNUQyxNQUFNO1FBQUM7S0FBb0I7QUFDN0I7QUFjTyxNQUFNQyxpQkFBZ0Q7UUFBQyxFQUM1REMsUUFBUSxFQUNSQyxVQUFVLEtBQUssRUFDZkMsWUFBWSxFQUFFLEVBQ2RDLFdBQVcsRUFDWEMsVUFBVSxLQUFLLEVBQ2hCO1FBWWFELHFCQUlhQSxrQ0FDSEEsK0JBR1pBOztJQW5CVixNQUFNLENBQUNFLFdBQVdDLGFBQWEsR0FBRzNDLCtDQUFRQSxDQUN4Q3dDLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYTVCLGdCQUFnQixNQUFLLG9CQUFvQixTQUN0RDRCLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYTVCLGdCQUFnQixNQUFLLGFBQWEsYUFBYTtJQUU5RCxNQUFNLENBQUNnQyxVQUFVQyxZQUFZLEdBQUc3QywrQ0FBUUEsQ0FBYSxFQUFFO0lBQ3ZELE1BQU0sQ0FBQzhDLGFBQWFDLGVBQWUsR0FBRy9DLCtDQUFRQSxDQUFDO0lBRS9DLE1BQU1nRCxPQUFPOUMsd0RBQU9BLENBQXFCO1FBQ3ZDK0MsVUFBVTdDLG9FQUFXQSxDQUFDTTtRQUN0QndDLGVBQWU7WUFDYnRDLGtCQUFrQjRCLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYTVCLGdCQUFnQixLQUFJO1lBQ25ERSxRQUFRMEIsQ0FBQUEsd0JBQUFBLG1DQUFBQSxzQkFBQUEsWUFBYTFCLE1BQU0sY0FBbkIwQiwwQ0FBQUEsb0JBQXFCVyxRQUFRLE9BQU07WUFDM0MvQix1QkFBdUJvQixDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFZLFVBQVUsS0FBSTtZQUNsRDlCLG9CQUFvQmtCLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYWxCLGtCQUFrQixLQUFJO1lBQ3ZERSxtQkFBbUJnQixDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFoQixpQkFBaUIsS0FBSTtZQUNyREMscUJBQXFCZSxDQUFBQSx3QkFBQUEsbUNBQUFBLG1DQUFBQSxZQUFhZixtQkFBbUIsY0FBaENlLHVEQUFBQSxpQ0FBa0NXLFFBQVEsT0FBTTtZQUNyRXhCLGtCQUFrQmEsQ0FBQUEsd0JBQUFBLG1DQUFBQSxnQ0FBQUEsWUFBYWIsZ0JBQWdCLGNBQTdCYSxvREFBQUEsOEJBQStCVyxRQUFRLE9BQU07WUFDL0R2QixhQUFhWSxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFaLFdBQVcsS0FBSTtZQUN6Q0Msa0JBQWtCVyxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFYLGdCQUFnQixLQUFJLElBQUl3QjtZQUN2RHRCLE1BQU1TLENBQUFBLHdCQUFBQSxtQ0FBQUEsb0JBQUFBLFlBQWFULElBQUksY0FBakJTLHdDQUFBQSxrQkFBbUJXLFFBQVEsT0FBTTtRQUN6QztJQUNGO0lBRUEsTUFBTSxFQUNKRyxPQUFPLEVBQ1BDLFlBQVksRUFDWkMsS0FBSyxFQUNMQyxRQUFRLEVBQ1JDLEtBQUssRUFDTEMsV0FBVyxFQUFFQyxNQUFNLEVBQUVDLFlBQVksRUFBRSxFQUNwQyxHQUFHYjtJQUVKLE1BQU1jLGtCQUFrQkosTUFBTTtJQUU5QnpELGdEQUFTQTtvQ0FBQztZQUNSLE1BQU04RDt5REFBZTtvQkFDbkIsSUFBSTt3QkFDRixNQUFNQyxlQUFlLE1BQU0xRCxxRUFBMEI7d0JBQ3JEdUMsWUFBWW1CO29CQUNkLEVBQUUsT0FBT0UsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7d0JBQ3pDekQsdURBQUtBLENBQUN5RCxLQUFLLENBQUM7b0JBQ2QsU0FBVTt3QkFDUm5CLGVBQWU7b0JBQ2pCO2dCQUNGOztZQUVBZ0I7UUFDRjttQ0FBRyxFQUFFO0lBRUwsMkNBQTJDO0lBQzNDOUQsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTW1FLHFCQUFxQjtnQkFDekIsT0FBTztnQkFDUCxRQUFRO2dCQUNSLFlBQVk7WUFDZDtZQUNBWCxTQUFTLG9CQUFvQlcsa0JBQWtCLENBQUMxQixVQUFVO1FBQzVEO21DQUFHO1FBQUNBO1FBQVdlO0tBQVM7SUFFeEIsZ0NBQWdDO0lBQ2hDLE1BQU1ZLHdCQUF3QjtRQUM1QixPQUFPekIsU0FBUzBCLE1BQU0sQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUMsWUFBWSxLQUFLO0lBQ3JEO0lBRUEsTUFBTUMscUJBQXFCO1FBQ3pCLE9BQU83QixTQUFTMEIsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxZQUFZLEtBQUs7SUFDckQ7SUFFQSxNQUFNRSxtQkFBbUIsT0FBT3pDO1FBQzlCLElBQUk7WUFDRixJQUFJQSxLQUFLckIsZ0JBQWdCLEtBQUssWUFBWTtnQkFDeEMsMkVBQTJFO2dCQUMzRSxNQUFNSix5RUFBOEIsQ0FBQztvQkFDbkNNLFFBQVFtQixLQUFLbkIsTUFBTTtvQkFDbkJjLGFBQWFLLEtBQUtMLFdBQVcsSUFBSSxxQkFBNEQsT0FBdkNLLEtBQUtULGlCQUFpQixJQUFJO29CQUNoRm9ELGlCQUFpQjNDLEtBQUtiLHFCQUFxQjtvQkFDM0N5RCxlQUFlNUMsS0FBS1gsa0JBQWtCO29CQUN0Q08sa0JBQWtCSSxLQUFLSixnQkFBZ0IsQ0FBQ2lELFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO29CQUNuRWhELE1BQU1FLEtBQUtGLElBQUksSUFBSTtnQkFDckI7WUFDRixPQUFPO2dCQUNMLDZCQUE2QjtnQkFDN0IsTUFBTXhCLHdGQUE2QyxDQUFDO29CQUNsRE8sUUFBUW1CLEtBQUtuQixNQUFNO29CQUNuQmMsYUFBYUssS0FBS0wsV0FBVztvQkFDN0J3QixZQUFZbkIsS0FBS2IscUJBQXFCO29CQUN0Q0ksbUJBQW1CUyxLQUFLVCxpQkFBaUI7b0JBQ3pDQyxxQkFBcUJRLEtBQUtSLG1CQUFtQjtvQkFDN0NFLGtCQUFrQk0sS0FBS04sZ0JBQWdCO29CQUN2Q2Ysa0JBQWtCcUIsS0FBS3JCLGdCQUFnQjtvQkFDdkNpQixrQkFBa0JJLEtBQUtKLGdCQUFnQixDQUFDaUQsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7b0JBQ25FaEQsTUFBTUUsS0FBS0YsSUFBSSxJQUFJO2dCQUNyQixHQUFHRSxLQUFLckIsZ0JBQWdCLEtBQUssbUJBQW1CcUIsS0FBS1gsa0JBQWtCLEdBQUdJO1lBQzVFO1lBRUEsTUFBTVcsU0FBU0o7WUFDZixJQUFJLENBQUNPLGFBQWE7Z0JBQ2hCZ0I7WUFDRjtRQUNGLEVBQUUsT0FBT1UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNENBQTRDQTtZQUMxRHpELHVEQUFLQSxDQUFDeUQsS0FBSyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLElBQUlwQixhQUFhO1FBQ2YscUJBQ0UsOERBQUNtQztZQUFJMUMsV0FBVTtzQkFDYiw0RUFBQzBDO2dCQUFJMUMsV0FBVTs7Ozs7Ozs7Ozs7SUFHckI7SUFFQSxNQUFNMkMsT0FBTztRQUNYO1lBQUVDLElBQUk7WUFBNEJDLE9BQU87WUFBT0MsTUFBTTtRQUFLO1FBQzNEO1lBQUVGLElBQUk7WUFBNkJDLE9BQU87WUFBUUMsTUFBTTtRQUFLO1FBQzdEO1lBQUVGLElBQUk7WUFBaUNDLE9BQU87WUFBWUMsTUFBTTtRQUFLO0tBQ3RFO0lBRUQscUJBQ0UsOERBQUNKO1FBQUkxQyxXQUFXLEdBQWEsT0FBVkE7OzBCQUVqQiw4REFBQzBDO2dCQUFJMUMsV0FBVTswQkFDWjJDLEtBQUtJLEdBQUcsQ0FBQyxDQUFDQyxvQkFDVCw4REFBQ0M7d0JBRUNDLE1BQUs7d0JBQ0xDLFNBQVMsSUFBTS9DLGFBQWE0QyxJQUFJSixFQUFFO3dCQUNsQzVDLFdBQVcsNkdBSVYsT0FIQ0csY0FBYzZDLElBQUlKLEVBQUUsR0FDaEIseUNBQ0E7OzBDQUdOLDhEQUFDUTswQ0FBTUosSUFBSUYsSUFBSTs7Ozs7OzBDQUNmLDhEQUFDTTswQ0FBTUosSUFBSUgsS0FBSzs7Ozs7Ozt1QkFWWEcsSUFBSUosRUFBRTs7Ozs7Ozs7OzswQkFnQmpCLDhEQUFDbkM7Z0JBQUtYLFVBQVVrQixhQUFhbUI7Z0JBQW1CbkMsV0FBVyxHQUF1QyxPQUFwQ0UsVUFBVSxjQUFjOztrQ0FFcEYsOERBQUN3Qzt3QkFBSTFDLFdBQVU7OzBDQUNiLDhEQUFDNkM7Z0NBQU03QyxXQUFVOzBDQUE4Qzs7Ozs7OzBDQUcvRCw4REFBQ3BDLHVEQUFVQTtnQ0FDVHlGLE1BQUs7Z0NBQ0x0QyxTQUFTQTtnQ0FDVHVDLFFBQVE7d0NBQUMsRUFBRUMsS0FBSyxFQUFFO3lEQUNoQiw4REFBQ0M7d0NBQ0UsR0FBR0QsS0FBSzt3Q0FDVHZELFdBQVcsaU1BQXdQLE9BQXZEcUIsT0FBT3hDLHFCQUFxQixHQUFHLHFCQUFxQjs7MERBRWhRLDhEQUFDNEU7Z0RBQU9DLE9BQU07MERBQUc7Ozs7Ozs0Q0FDaEI1Qix3QkFBd0JpQixHQUFHLENBQUMsQ0FBQ1k7b0RBRVBBO3FFQURyQiw4REFBQ0Y7b0RBQXdCQyxPQUFPQyxRQUFRZixFQUFFOzt3REFDdkNlLFFBQVFOLElBQUk7d0RBQUM7eURBQUtNLDJCQUFBQSxRQUFRQyxlQUFlLGNBQXZCRCwrQ0FBQUEseUJBQXlCRSxPQUFPLENBQUM7O21EQUR6Q0YsUUFBUWYsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQU85QnZCLE9BQU94QyxxQkFBcUIsa0JBQzNCLDhEQUFDaUY7Z0NBQUU5RCxXQUFVOzBDQUEwQnFCLE9BQU94QyxxQkFBcUIsQ0FBQ2MsT0FBTzs7Ozs7Ozs7Ozs7O29CQUs3RVEsQ0FBQUEsY0FBYyxTQUFTQSxjQUFjLFVBQVMsbUJBQzlDLDhEQUFDdUM7d0JBQUkxQyxXQUFVOzswQ0FDYiw4REFBQzZDO2dDQUFNN0MsV0FBVTswQ0FDZEcsY0FBYyxRQUFRLHNCQUFzQjs7Ozs7OzBDQUUvQyw4REFBQ3ZDLHVEQUFVQTtnQ0FDVHlGLE1BQUs7Z0NBQ0x0QyxTQUFTQTtnQ0FDVHVDLFFBQVE7d0NBQUMsRUFBRUMsS0FBSyxFQUFFO3lEQUNoQiw4REFBQ0M7d0NBQ0UsR0FBR0QsS0FBSzt3Q0FDVHZELFdBQVcsaU1BQXFQLE9BQXBEcUIsT0FBT3RDLGtCQUFrQixHQUFHLHFCQUFxQjs7MERBRTdQLDhEQUFDMEU7Z0RBQU9DLE9BQU07O29EQUFHO29EQUFRdkQsY0FBYyxRQUFRLFlBQVk7b0RBQVk7Ozs7Ozs7NENBQ3RFK0IscUJBQXFCYSxHQUFHLENBQUMsQ0FBQ1k7b0RBRXFCQTtxRUFEOUMsOERBQUNGO29EQUF3QkMsT0FBT0MsUUFBUWYsRUFBRTs7d0RBQ3ZDZSxRQUFRTixJQUFJO3dEQUFDO3dEQUFHTSxRQUFRMUIsWUFBWTt3REFBQzt5REFBTTBCLDJCQUFBQSxRQUFRQyxlQUFlLGNBQXZCRCwrQ0FBQUEseUJBQXlCRSxPQUFPLENBQUM7O21EQURsRUYsUUFBUWYsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQU85QnZCLE9BQU90QyxrQkFBa0Isa0JBQ3hCLDhEQUFDK0U7Z0NBQUU5RCxXQUFVOzBDQUEwQnFCLE9BQU90QyxrQkFBa0IsQ0FBQ1ksT0FBTzs7Ozs7Ozs7Ozs7O29CQU03RVEsY0FBYyw0QkFDYiw4REFBQ3VDO3dCQUFJMUMsV0FBVTs7MENBQ2IsOERBQUM2QztnQ0FBTTdDLFdBQVU7MENBQThDOzs7Ozs7MENBRy9ELDhEQUFDcEMsdURBQVVBO2dDQUNUeUYsTUFBSztnQ0FDTHRDLFNBQVNBO2dDQUNUdUMsUUFBUTt3Q0FBQyxFQUFFQyxLQUFLLEVBQUU7eURBQ2hCLDhEQUFDUTt3Q0FDQ2IsTUFBSzt3Q0FDSixHQUFHSyxLQUFLO3dDQUNUUyxhQUFZO3dDQUNaaEUsV0FBVyxpTUFBb1AsT0FBbkRxQixPQUFPcEMsaUJBQWlCLEdBQUcscUJBQXFCOzs7Ozs7Ozs7Ozs7NEJBSWpRb0MsT0FBT3BDLGlCQUFpQixrQkFDdkIsOERBQUM2RTtnQ0FBRTlELFdBQVU7MENBQTBCcUIsT0FBT3BDLGlCQUFpQixDQUFDVSxPQUFPOzs7Ozs7Ozs7Ozs7b0JBTTVFUSxjQUFjLDRCQUNiLDhEQUFDdUM7d0JBQUkxQyxXQUFVOzswQ0FDYiw4REFBQzBDO2dDQUFJMUMsV0FBVTs7a0RBQ2IsOERBQUM2Qzt3Q0FBTTdDLFdBQVU7a0RBQThDOzs7Ozs7a0RBRy9ELDhEQUFDcEMsdURBQVVBO3dDQUNUeUYsTUFBSzt3Q0FDTHRDLFNBQVNBO3dDQUNUdUMsUUFBUTtnREFBQyxFQUFFQyxLQUFLLEVBQUU7aUVBQ2hCLDhEQUFDUTtnREFDQ2IsTUFBSztnREFDTGUsV0FBVTtnREFDVCxHQUFHVixLQUFLO2dEQUNUUyxhQUFZO2dEQUNaaEUsV0FBVyxpTUFBc1AsT0FBckRxQixPQUFPbkMsbUJBQW1CLEdBQUcscUJBQXFCOzs7Ozs7Ozs7Ozs7b0NBSW5RbUMsT0FBT25DLG1CQUFtQixrQkFDekIsOERBQUM0RTt3Q0FBRTlELFdBQVU7a0RBQTBCcUIsT0FBT25DLG1CQUFtQixDQUFDUyxPQUFPOzs7Ozs7Ozs7Ozs7MENBSTdFLDhEQUFDK0M7Z0NBQUkxQyxXQUFVOztrREFDYiw4REFBQzZDO3dDQUFNN0MsV0FBVTtrREFBOEM7Ozs7OztrREFHL0QsOERBQUNwQyx1REFBVUE7d0NBQ1R5RixNQUFLO3dDQUNMdEMsU0FBU0E7d0NBQ1R1QyxRQUFRO2dEQUFDLEVBQUVDLEtBQUssRUFBRTtpRUFDaEIsOERBQUNRO2dEQUNDYixNQUFLO2dEQUNMZSxXQUFVO2dEQUNULEdBQUdWLEtBQUs7Z0RBQ1RTLGFBQVk7Z0RBQ1poRSxXQUFXLGlNQUFtUCxPQUFsRHFCLE9BQU9qQyxnQkFBZ0IsR0FBRyxxQkFBcUI7Ozs7Ozs7Ozs7OztvQ0FJaFFpQyxPQUFPakMsZ0JBQWdCLGtCQUN0Qiw4REFBQzBFO3dDQUFFOUQsV0FBVTtrREFBMEJxQixPQUFPakMsZ0JBQWdCLENBQUNPLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPOUUsOERBQUMrQzt3QkFBSTFDLFdBQVU7OzBDQUNiLDhEQUFDNkM7Z0NBQU03QyxXQUFVOzBDQUNkRyxjQUFjLGFBQWEsc0JBQXNCOzs7Ozs7MENBRXBELDhEQUFDdkMsdURBQVVBO2dDQUNUeUYsTUFBSztnQ0FDTHRDLFNBQVNBO2dDQUNUdUMsUUFBUTt3Q0FBQyxFQUFFQyxLQUFLLEVBQUU7eURBQ2hCLDhEQUFDUTt3Q0FDQ2IsTUFBSzt3Q0FDTGUsV0FBVTt3Q0FDVCxHQUFHVixLQUFLO3dDQUNUUyxhQUFZO3dDQUNaaEUsV0FBVyxpTUFBeU8sT0FBeENxQixPQUFPOUMsTUFBTSxHQUFHLHFCQUFxQjs7Ozs7Ozs7Ozs7OzRCQUl0UDhDLE9BQU85QyxNQUFNLGtCQUNaLDhEQUFDdUY7Z0NBQUU5RCxXQUFVOzBDQUEwQnFCLE9BQU85QyxNQUFNLENBQUNvQixPQUFPOzs7Ozs7Ozs7Ozs7a0NBS2hFLDhEQUFDK0M7d0JBQUkxQyxXQUFVOzswQ0FDYiw4REFBQzZDO2dDQUFNN0MsV0FBVTswQ0FBOEM7Ozs7OzswQ0FHL0QsOERBQUNwQyx1REFBVUE7Z0NBQ1R5RixNQUFLO2dDQUNMdEMsU0FBU0E7Z0NBQ1R1QyxRQUFRO3dDQUFDLEVBQUVDLEtBQUssRUFBRTt5REFDaEIsOERBQUNRO3dDQUNDYixNQUFLO3dDQUNMZSxXQUFVO3dDQUNULEdBQUdWLEtBQUs7d0NBQ1RTLGFBQVk7d0NBQ1poRSxXQUFXLGlNQUF1TyxPQUF0Q3FCLE9BQU83QixJQUFJLEdBQUcscUJBQXFCOzs7Ozs7Ozs7Ozs7NEJBSXBQNkIsT0FBTzdCLElBQUksa0JBQ1YsOERBQUNzRTtnQ0FBRTlELFdBQVU7MENBQTBCcUIsT0FBTzdCLElBQUksQ0FBQ0csT0FBTzs7Ozs7Ozs7Ozs7O2tDQUs5RCw4REFBQytDO3dCQUFJMUMsV0FBVTs7MENBQ2IsOERBQUM2QztnQ0FBTTdDLFdBQVU7MENBQThDOzs7Ozs7MENBRy9ELDhEQUFDcEMsdURBQVVBO2dDQUNUeUYsTUFBSztnQ0FDTHRDLFNBQVNBO2dDQUNUdUMsUUFBUTt3Q0FBQyxFQUFFQyxLQUFLLEVBQUU7eURBQ2hCLDhEQUFDVzt3Q0FDRSxHQUFHWCxLQUFLO3dDQUNUWSxNQUFNO3dDQUNOSCxhQUFZO3dDQUNaaEUsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9sQiw4REFBQzBDO3dCQUFJMUMsV0FBVTs7MENBQ2IsOERBQUM2QztnQ0FBTTdDLFdBQVU7MENBQThDOzs7Ozs7MENBRy9ELDhEQUFDcEMsdURBQVVBO2dDQUNUeUYsTUFBSztnQ0FDTHRDLFNBQVNBO2dDQUNUdUMsUUFBUTt3Q0FBQyxFQUFFQyxLQUFLLEVBQUU7eURBQ2hCLDhEQUFDUTt3Q0FDQ2IsTUFBSzt3Q0FDSixHQUFHSyxLQUFLO3dDQUNURyxPQUFPSCxNQUFNRyxLQUFLLFlBQVk1QyxPQUFPeUMsTUFBTUcsS0FBSyxDQUFDbkIsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsR0FBR2UsTUFBTUcsS0FBSzt3Q0FDMUZVLFVBQVUsQ0FBQ0MsSUFBTWQsTUFBTWEsUUFBUSxDQUFDLElBQUl0RCxLQUFLdUQsRUFBRUMsTUFBTSxDQUFDWixLQUFLO3dDQUN2RDFELFdBQVcsaU1BQW1QLE9BQWxEcUIsT0FBTy9CLGdCQUFnQixHQUFHLHFCQUFxQjs7Ozs7Ozs7Ozs7OzRCQUloUStCLE9BQU8vQixnQkFBZ0Isa0JBQ3RCLDhEQUFDd0U7Z0NBQUU5RCxXQUFVOzBDQUEwQnFCLE9BQU8vQixnQkFBZ0IsQ0FBQ0ssT0FBTzs7Ozs7Ozs7Ozs7O2tDQUsxRSw4REFBQytDO3dCQUFJMUMsV0FBVTtrQ0FDYiw0RUFBQ2lEOzRCQUNDQyxNQUFLOzRCQUNMcUIsVUFBVWpELGdCQUFnQnZCOzRCQUMxQkMsV0FBVTtzQ0FFVHNCLGdCQUFnQnZCLFVBQVUsa0JBQzFCRSxjQUFjLHNCQUNkRSxjQUFjLFFBQVEsbUJBQ3RCQSxjQUFjLFNBQVMsb0JBQW9COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU14RCxFQUFDO0dBL1hZTjs7UUFjRWxDLG9EQUFPQTs7O0tBZFRrQyIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9hcHBzL3dlYi9zcmMvY29tcG9uZW50cy9JbnZlc3RtZW50Rm9ybS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZUZvcm0sIENvbnRyb2xsZXIgfSBmcm9tICdyZWFjdC1ob29rLWZvcm0nXG5pbXBvcnQgeyB6b2RSZXNvbHZlciB9IGZyb20gJ0Bob29rZm9ybS9yZXNvbHZlcnMvem9kJ1xuaW1wb3J0IHsgeiB9IGZyb20gJ3pvZCdcbmltcG9ydCB7IFxuICB0eXBlIElBY2NvdW50LFxuICBBY2NvdW50U2VydmljZSxcbiAgSW52ZXN0bWVudFNlcnZpY2UsXG4gIFRyYW5zZmVyU2VydmljZVxufSBmcm9tICdAc2hhcmVkL2luZGV4J1xuaW1wb3J0IHRvYXN0IGZyb20gJ3JlYWN0LWhvdC10b2FzdCdcblxuLy8gSW52ZXN0bWVudCBmb3JtIHNjaGVtYVxuY29uc3QgaW52ZXN0bWVudEZvcm1TY2hlbWEgPSB6Lm9iamVjdCh7XG4gIHRyYW5zYWN0aW9uX3R5cGU6IHouZW51bShbJ2ludmVzdG1lbnRfYnV5JywgJ2ludmVzdG1lbnRfc2VsbCcsICdkaXZpZGVuZCddKSxcbiAgYW1vdW50OiB6LnN0cmluZygpLm1pbigxLCAnQW1vdW50IGlzIHJlcXVpcmVkJykudHJhbnNmb3JtKHZhbCA9PiBwYXJzZUZsb2F0KHZhbCkpLFxuICBpbnZlc3RtZW50X2FjY291bnRfaWQ6IHouc3RyaW5nKCkudXVpZCgnUGxlYXNlIHNlbGVjdCBhbiBpbnZlc3RtZW50IGFjY291bnQnKSxcbiAgZnVuZGluZ19hY2NvdW50X2lkOiB6LnN0cmluZygpLnV1aWQoJ1BsZWFzZSBzZWxlY3QgYSBmdW5kaW5nIGFjY291bnQnKS5vcHRpb25hbCgpLFxuICBpbnZlc3RtZW50X3N5bWJvbDogei5zdHJpbmcoKS5taW4oMSwgJ1N0b2NrIHN5bWJvbCBpcyByZXF1aXJlZCcpLm9wdGlvbmFsKCksXG4gIGludmVzdG1lbnRfcXVhbnRpdHk6IHouc3RyaW5nKCkudHJhbnNmb3JtKHZhbCA9PiB2YWwgPyBwYXJzZUZsb2F0KHZhbCkgOiB1bmRlZmluZWQpLm9wdGlvbmFsKCksXG4gIGludmVzdG1lbnRfcHJpY2U6IHouc3RyaW5nKCkudHJhbnNmb3JtKHZhbCA9PiB2YWwgPyBwYXJzZUZsb2F0KHZhbCkgOiB1bmRlZmluZWQpLm9wdGlvbmFsKCksXG4gIGRlc2NyaXB0aW9uOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIHRyYW5zYWN0aW9uX2RhdGU6IHouZGF0ZSgpLFxuICBmZWVzOiB6LnN0cmluZygpLnRyYW5zZm9ybSh2YWwgPT4gdmFsID8gcGFyc2VGbG9hdCh2YWwpIDogMCksXG59KS5yZWZpbmUoKGRhdGEpID0+IHtcbiAgaWYgKGRhdGEudHJhbnNhY3Rpb25fdHlwZSA9PT0gJ2ludmVzdG1lbnRfYnV5JyB8fCBkYXRhLnRyYW5zYWN0aW9uX3R5cGUgPT09ICdpbnZlc3RtZW50X3NlbGwnKSB7XG4gICAgcmV0dXJuIGRhdGEuaW52ZXN0bWVudF9zeW1ib2wgJiYgZGF0YS5pbnZlc3RtZW50X3F1YW50aXR5ICYmIGRhdGEuaW52ZXN0bWVudF9wcmljZVxuICB9XG4gIHJldHVybiB0cnVlXG59LCB7XG4gIG1lc3NhZ2U6IFwiU3RvY2sgc3ltYm9sLCBxdWFudGl0eSwgYW5kIHByaWNlIGFyZSByZXF1aXJlZCBmb3IgYnV5L3NlbGwgdHJhbnNhY3Rpb25zXCIsXG4gIHBhdGg6IFtcImludmVzdG1lbnRfc3ltYm9sXCJdXG59KVxuXG50eXBlIEludmVzdG1lbnRGb3JtRGF0YSA9IHouaW5mZXI8dHlwZW9mIGludmVzdG1lbnRGb3JtU2NoZW1hPlxuXG5pbnRlcmZhY2UgSW52ZXN0bWVudEZvcm1Qcm9wcyB7XG4gIG9uU3VibWl0OiAoZGF0YTogYW55KSA9PiBQcm9taXNlPHZvaWQ+XG4gIGxvYWRpbmc/OiBib29sZWFuXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xuICBpbml0aWFsRGF0YT86IGFueVxuICBjb21wYWN0PzogYm9vbGVhblxufVxuXG50eXBlIEludmVzdG1lbnRUYWJUeXBlID0gJ2J1eScgfCAnc2VsbCcgfCAnZGl2aWRlbmQnXG5cbmV4cG9ydCBjb25zdCBJbnZlc3RtZW50Rm9ybTogUmVhY3QuRkM8SW52ZXN0bWVudEZvcm1Qcm9wcz4gPSAoe1xuICBvblN1Ym1pdCxcbiAgbG9hZGluZyA9IGZhbHNlLFxuICBjbGFzc05hbWUgPSBcIlwiLFxuICBpbml0aWFsRGF0YSxcbiAgY29tcGFjdCA9IGZhbHNlXG59KSA9PiB7XG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZTxJbnZlc3RtZW50VGFiVHlwZT4oXG4gICAgaW5pdGlhbERhdGE/LnRyYW5zYWN0aW9uX3R5cGUgPT09ICdpbnZlc3RtZW50X3NlbGwnID8gJ3NlbGwnIDpcbiAgICBpbml0aWFsRGF0YT8udHJhbnNhY3Rpb25fdHlwZSA9PT0gJ2RpdmlkZW5kJyA/ICdkaXZpZGVuZCcgOiAnYnV5J1xuICApXG4gIGNvbnN0IFthY2NvdW50cywgc2V0QWNjb3VudHNdID0gdXNlU3RhdGU8SUFjY291bnRbXT4oW10pXG4gIGNvbnN0IFtsb2FkaW5nRGF0YSwgc2V0TG9hZGluZ0RhdGFdID0gdXNlU3RhdGUodHJ1ZSlcblxuICBjb25zdCBmb3JtID0gdXNlRm9ybTxJbnZlc3RtZW50Rm9ybURhdGE+KHtcbiAgICByZXNvbHZlcjogem9kUmVzb2x2ZXIoaW52ZXN0bWVudEZvcm1TY2hlbWEpLFxuICAgIGRlZmF1bHRWYWx1ZXM6IHtcbiAgICAgIHRyYW5zYWN0aW9uX3R5cGU6IGluaXRpYWxEYXRhPy50cmFuc2FjdGlvbl90eXBlIHx8ICdpbnZlc3RtZW50X2J1eScsXG4gICAgICBhbW91bnQ6IGluaXRpYWxEYXRhPy5hbW91bnQ/LnRvU3RyaW5nKCkgfHwgJycsXG4gICAgICBpbnZlc3RtZW50X2FjY291bnRfaWQ6IGluaXRpYWxEYXRhPy5hY2NvdW50X2lkIHx8ICcnLFxuICAgICAgZnVuZGluZ19hY2NvdW50X2lkOiBpbml0aWFsRGF0YT8uZnVuZGluZ19hY2NvdW50X2lkIHx8ICcnLFxuICAgICAgaW52ZXN0bWVudF9zeW1ib2w6IGluaXRpYWxEYXRhPy5pbnZlc3RtZW50X3N5bWJvbCB8fCAnJyxcbiAgICAgIGludmVzdG1lbnRfcXVhbnRpdHk6IGluaXRpYWxEYXRhPy5pbnZlc3RtZW50X3F1YW50aXR5Py50b1N0cmluZygpIHx8ICcnLFxuICAgICAgaW52ZXN0bWVudF9wcmljZTogaW5pdGlhbERhdGE/LmludmVzdG1lbnRfcHJpY2U/LnRvU3RyaW5nKCkgfHwgJycsXG4gICAgICBkZXNjcmlwdGlvbjogaW5pdGlhbERhdGE/LmRlc2NyaXB0aW9uIHx8ICcnLFxuICAgICAgdHJhbnNhY3Rpb25fZGF0ZTogaW5pdGlhbERhdGE/LnRyYW5zYWN0aW9uX2RhdGUgfHwgbmV3IERhdGUoKSxcbiAgICAgIGZlZXM6IGluaXRpYWxEYXRhPy5mZWVzPy50b1N0cmluZygpIHx8ICcnLFxuICAgIH1cbiAgfSlcblxuICBjb25zdCB7XG4gICAgY29udHJvbCxcbiAgICBoYW5kbGVTdWJtaXQsXG4gICAgcmVzZXQsXG4gICAgc2V0VmFsdWUsXG4gICAgd2F0Y2gsXG4gICAgZm9ybVN0YXRlOiB7IGVycm9ycywgaXNTdWJtaXR0aW5nIH1cbiAgfSA9IGZvcm1cblxuICBjb25zdCB0cmFuc2FjdGlvblR5cGUgPSB3YXRjaCgndHJhbnNhY3Rpb25fdHlwZScpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBsb2FkQWNjb3VudHMgPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBhY2NvdW50c0RhdGEgPSBhd2FpdCBBY2NvdW50U2VydmljZS5nZXRBY2NvdW50cygpXG4gICAgICAgIHNldEFjY291bnRzKGFjY291bnRzRGF0YSlcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgYWNjb3VudHM6JywgZXJyb3IpXG4gICAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gbG9hZCBhY2NvdW50cycpXG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nRGF0YShmYWxzZSlcbiAgICAgIH1cbiAgICB9XG5cbiAgICBsb2FkQWNjb3VudHMoKVxuICB9LCBbXSlcblxuICAvLyBVcGRhdGUgdHJhbnNhY3Rpb24gdHlwZSB3aGVuIHRhYiBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdHJhbnNhY3Rpb25UeXBlTWFwID0ge1xuICAgICAgJ2J1eSc6ICdpbnZlc3RtZW50X2J1eScsXG4gICAgICAnc2VsbCc6ICdpbnZlc3RtZW50X3NlbGwnLFxuICAgICAgJ2RpdmlkZW5kJzogJ2RpdmlkZW5kJ1xuICAgIH0gYXMgY29uc3RcbiAgICBzZXRWYWx1ZSgndHJhbnNhY3Rpb25fdHlwZScsIHRyYW5zYWN0aW9uVHlwZU1hcFthY3RpdmVUYWJdKVxuICB9LCBbYWN0aXZlVGFiLCBzZXRWYWx1ZV0pXG5cbiAgLy8gRmlsdGVyIGFjY291bnRzIGJhc2VkIG9uIHR5cGVcbiAgY29uc3QgZ2V0SW52ZXN0bWVudEFjY291bnRzID0gKCkgPT4ge1xuICAgIHJldHVybiBhY2NvdW50cy5maWx0ZXIoYWNjID0+IGFjYy5hY2NvdW50X3R5cGUgPT09ICdpbnZlc3RtZW50JylcbiAgfVxuXG4gIGNvbnN0IGdldEZ1bmRpbmdBY2NvdW50cyA9ICgpID0+IHtcbiAgICByZXR1cm4gYWNjb3VudHMuZmlsdGVyKGFjYyA9PiBhY2MuYWNjb3VudF90eXBlICE9PSAnaW52ZXN0bWVudCcpXG4gIH1cblxuICBjb25zdCBoYW5kbGVGb3JtU3VibWl0ID0gYXN5bmMgKGRhdGE6IEludmVzdG1lbnRGb3JtRGF0YSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAoZGF0YS50cmFuc2FjdGlvbl90eXBlID09PSAnZGl2aWRlbmQnKSB7XG4gICAgICAgIC8vIEhhbmRsZSBkaXZpZGVuZCBhcyBhIHRyYW5zZmVyIGZyb20gaW52ZXN0bWVudCBhY2NvdW50IHRvIGZ1bmRpbmcgYWNjb3VudFxuICAgICAgICBhd2FpdCBUcmFuc2ZlclNlcnZpY2UuY3JlYXRlVHJhbnNmZXIoe1xuICAgICAgICAgIGFtb3VudDogZGF0YS5hbW91bnQsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGRhdGEuZGVzY3JpcHRpb24gfHwgYERpdmlkZW5kIHBheW1lbnQ6ICR7ZGF0YS5pbnZlc3RtZW50X3N5bWJvbCB8fCAnSW52ZXN0bWVudCd9YCxcbiAgICAgICAgICBmcm9tX2FjY291bnRfaWQ6IGRhdGEuaW52ZXN0bWVudF9hY2NvdW50X2lkLFxuICAgICAgICAgIHRvX2FjY291bnRfaWQ6IGRhdGEuZnVuZGluZ19hY2NvdW50X2lkISxcbiAgICAgICAgICB0cmFuc2FjdGlvbl9kYXRlOiBkYXRhLnRyYW5zYWN0aW9uX2RhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLFxuICAgICAgICAgIGZlZXM6IGRhdGEuZmVlcyB8fCAwLFxuICAgICAgICB9KVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gSGFuZGxlIGludmVzdG1lbnQgYnV5L3NlbGxcbiAgICAgICAgYXdhaXQgSW52ZXN0bWVudFNlcnZpY2UuY3JlYXRlSW52ZXN0bWVudFRyYW5zYWN0aW9uKHtcbiAgICAgICAgICBhbW91bnQ6IGRhdGEuYW1vdW50LFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBkYXRhLmRlc2NyaXB0aW9uLFxuICAgICAgICAgIGFjY291bnRfaWQ6IGRhdGEuaW52ZXN0bWVudF9hY2NvdW50X2lkLFxuICAgICAgICAgIGludmVzdG1lbnRfc3ltYm9sOiBkYXRhLmludmVzdG1lbnRfc3ltYm9sISxcbiAgICAgICAgICBpbnZlc3RtZW50X3F1YW50aXR5OiBkYXRhLmludmVzdG1lbnRfcXVhbnRpdHkhLFxuICAgICAgICAgIGludmVzdG1lbnRfcHJpY2U6IGRhdGEuaW52ZXN0bWVudF9wcmljZSEsXG4gICAgICAgICAgdHJhbnNhY3Rpb25fdHlwZTogZGF0YS50cmFuc2FjdGlvbl90eXBlIGFzICdpbnZlc3RtZW50X2J1eScgfCAnaW52ZXN0bWVudF9zZWxsJyxcbiAgICAgICAgICB0cmFuc2FjdGlvbl9kYXRlOiBkYXRhLnRyYW5zYWN0aW9uX2RhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLFxuICAgICAgICAgIGZlZXM6IGRhdGEuZmVlcyB8fCAwLFxuICAgICAgICB9LCBkYXRhLnRyYW5zYWN0aW9uX3R5cGUgPT09ICdpbnZlc3RtZW50X2J1eScgPyBkYXRhLmZ1bmRpbmdfYWNjb3VudF9pZCA6IHVuZGVmaW5lZClcbiAgICAgIH1cblxuICAgICAgYXdhaXQgb25TdWJtaXQoZGF0YSlcbiAgICAgIGlmICghaW5pdGlhbERhdGEpIHtcbiAgICAgICAgcmVzZXQoKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzdWJtaXR0aW5nIGludmVzdG1lbnQgdHJhbnNhY3Rpb246JywgZXJyb3IpXG4gICAgICB0b2FzdC5lcnJvcignRmFpbGVkIHRvIHN1Ym1pdCBpbnZlc3RtZW50IHRyYW5zYWN0aW9uJylcbiAgICB9XG4gIH1cblxuICBpZiAobG9hZGluZ0RhdGEpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTMyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTggdy04IGJvcmRlci00IGJvcmRlci1ib3JkZXIgYm9yZGVyLXQtcHJpbWFyeS1ibHVlXCI+PC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICBjb25zdCB0YWJzID0gW1xuICAgIHsgaWQ6ICdidXknIGFzIEludmVzdG1lbnRUYWJUeXBlLCBsYWJlbDogJ0J1eScsIGljb246ICfwn5OIJyB9LFxuICAgIHsgaWQ6ICdzZWxsJyBhcyBJbnZlc3RtZW50VGFiVHlwZSwgbGFiZWw6ICdTZWxsJywgaWNvbjogJ/Cfk4knIH0sXG4gICAgeyBpZDogJ2RpdmlkZW5kJyBhcyBJbnZlc3RtZW50VGFiVHlwZSwgbGFiZWw6ICdEaXZpZGVuZCcsIGljb246ICfwn5KwJyB9XG4gIF1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtjbGFzc05hbWV9YH0+XG4gICAgICB7LyogVGFiIE5hdmlnYXRpb24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xIGJnLXN1cmZhY2Utc2Vjb25kYXJ5IHJvdW5kZWQtbGcgcC0xIG1iLTZcIj5cbiAgICAgICAge3RhYnMubWFwKCh0YWIpID0+IChcbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBrZXk9e3RhYi5pZH1cbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKHRhYi5pZCl9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4LTEgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yIHB4LTQgcHktMyByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgJHtcbiAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSB0YWIuaWRcbiAgICAgICAgICAgICAgICA/ICdiZy1wcmltYXJ5LWJsdWUgdGV4dC13aGl0ZSBzaGFkb3ctc20nXG4gICAgICAgICAgICAgICAgOiAndGV4dC10ZXh0LXNlY29uZGFyeSBob3Zlcjp0ZXh0LXRleHQtcHJpbWFyeSBob3ZlcjpiZy1zdXJmYWNlJ1xuICAgICAgICAgICAgfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPHNwYW4+e3RhYi5pY29ufTwvc3Bhbj5cbiAgICAgICAgICAgIDxzcGFuPnt0YWIubGFiZWx9PC9zcGFuPlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRm9ybSBDb250ZW50ICovfVxuICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdChoYW5kbGVGb3JtU3VibWl0KX0gY2xhc3NOYW1lPXtgJHtjb21wYWN0ID8gJ3NwYWNlLXktNCcgOiAnc3BhY2UteS02J31gfT5cbiAgICAgICAgey8qIEludmVzdG1lbnQgQWNjb3VudCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXRleHQtcHJpbWFyeVwiPlxuICAgICAgICAgICAgSW52ZXN0bWVudCBBY2NvdW50ICpcbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxDb250cm9sbGVyXG4gICAgICAgICAgICBuYW1lPVwiaW52ZXN0bWVudF9hY2NvdW50X2lkXCJcbiAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgIHsuLi5maWVsZH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgYmctc3VyZmFjZSBib3JkZXItMiBib3JkZXItYm9yZGVyIHJvdW5kZWQtbGcgcHgtNCBweS0zIHRleHQtYmFzZSB0cmFuc2l0aW9uLWFsbCBmb2N1czpib3JkZXItcHJpbWFyeS1ibHVlIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5LWJsdWUvMjAgdGV4dC10ZXh0LXByaW1hcnkgJHtlcnJvcnMuaW52ZXN0bWVudF9hY2NvdW50X2lkID8gJ2JvcmRlci1lcnJvci1yZWQnIDogJyd9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5TZWxlY3QgaW52ZXN0bWVudCBhY2NvdW50PC9vcHRpb24+XG4gICAgICAgICAgICAgICAge2dldEludmVzdG1lbnRBY2NvdW50cygpLm1hcCgoYWNjb3VudCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2FjY291bnQuaWR9IHZhbHVlPXthY2NvdW50LmlkfT5cbiAgICAgICAgICAgICAgICAgICAge2FjY291bnQubmFtZX0gLSAke2FjY291bnQuY3VycmVudF9iYWxhbmNlPy50b0ZpeGVkKDIpfVxuICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAvPlxuICAgICAgICAgIHtlcnJvcnMuaW52ZXN0bWVudF9hY2NvdW50X2lkICYmIChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1lcnJvci1yZWRcIj57ZXJyb3JzLmludmVzdG1lbnRfYWNjb3VudF9pZC5tZXNzYWdlfTwvcD5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRnVuZGluZyBBY2NvdW50IChmb3IgYnV5IHRyYW5zYWN0aW9ucyBhbmQgZGl2aWRlbmRzKSAqL31cbiAgICAgICAgeyhhY3RpdmVUYWIgPT09ICdidXknIHx8IGFjdGl2ZVRhYiA9PT0gJ2RpdmlkZW5kJykgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXRleHQtcHJpbWFyeVwiPlxuICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnYnV5JyA/ICdGdW5kaW5nIEFjY291bnQgKicgOiAnUmVjZWl2aW5nIEFjY291bnQgKid9XG4gICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPENvbnRyb2xsZXJcbiAgICAgICAgICAgICAgbmFtZT1cImZ1bmRpbmdfYWNjb3VudF9pZFwiXG4gICAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICAgIHJlbmRlcj17KHsgZmllbGQgfSkgPT4gKFxuICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgIHsuLi5maWVsZH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBiZy1zdXJmYWNlIGJvcmRlci0yIGJvcmRlci1ib3JkZXIgcm91bmRlZC1sZyBweC00IHB5LTMgdGV4dC1iYXNlIHRyYW5zaXRpb24tYWxsIGZvY3VzOmJvcmRlci1wcmltYXJ5LWJsdWUgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktYmx1ZS8yMCB0ZXh0LXRleHQtcHJpbWFyeSAke2Vycm9ycy5mdW5kaW5nX2FjY291bnRfaWQgPyAnYm9yZGVyLWVycm9yLXJlZCcgOiAnJ31gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5TZWxlY3Qge2FjdGl2ZVRhYiA9PT0gJ2J1eScgPyAnZnVuZGluZycgOiAncmVjZWl2aW5nJ30gYWNjb3VudDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAge2dldEZ1bmRpbmdBY2NvdW50cygpLm1hcCgoYWNjb3VudCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17YWNjb3VudC5pZH0gdmFsdWU9e2FjY291bnQuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgIHthY2NvdW50Lm5hbWV9ICh7YWNjb3VudC5hY2NvdW50X3R5cGV9KSAtICR7YWNjb3VudC5jdXJyZW50X2JhbGFuY2U/LnRvRml4ZWQoMil9XG4gICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAge2Vycm9ycy5mdW5kaW5nX2FjY291bnRfaWQgJiYgKFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZXJyb3ItcmVkXCI+e2Vycm9ycy5mdW5kaW5nX2FjY291bnRfaWQubWVzc2FnZX08L3A+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBTdG9jayBTeW1ib2wgKG5vdCByZXF1aXJlZCBmb3IgZGl2aWRlbmQpICovfVxuICAgICAgICB7YWN0aXZlVGFiICE9PSAnZGl2aWRlbmQnICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC10ZXh0LXByaW1hcnlcIj5cbiAgICAgICAgICAgICAgU3RvY2sgU3ltYm9sICpcbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8Q29udHJvbGxlclxuICAgICAgICAgICAgICBuYW1lPVwiaW52ZXN0bWVudF9zeW1ib2xcIlxuICAgICAgICAgICAgICBjb250cm9sPXtjb250cm9sfVxuICAgICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIHsuLi5maWVsZH1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgQUFQTCwgR09PR0xcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIGJnLXN1cmZhY2UgYm9yZGVyLTIgYm9yZGVyLWJvcmRlciByb3VuZGVkLWxnIHB4LTQgcHktMyB0ZXh0LWJhc2UgdHJhbnNpdGlvbi1hbGwgZm9jdXM6Ym9yZGVyLXByaW1hcnktYmx1ZSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS1ibHVlLzIwIHRleHQtdGV4dC1wcmltYXJ5ICR7ZXJyb3JzLmludmVzdG1lbnRfc3ltYm9sID8gJ2JvcmRlci1lcnJvci1yZWQnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIHtlcnJvcnMuaW52ZXN0bWVudF9zeW1ib2wgJiYgKFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZXJyb3ItcmVkXCI+e2Vycm9ycy5pbnZlc3RtZW50X3N5bWJvbC5tZXNzYWdlfTwvcD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIFF1YW50aXR5IGFuZCBQcmljZSAobm90IHJlcXVpcmVkIGZvciBkaXZpZGVuZCkgKi99XG4gICAgICAgIHthY3RpdmVUYWIgIT09ICdkaXZpZGVuZCcgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC10ZXh0LXByaW1hcnlcIj5cbiAgICAgICAgICAgICAgICBRdWFudGl0eSAqXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxDb250cm9sbGVyXG4gICAgICAgICAgICAgICAgbmFtZT1cImludmVzdG1lbnRfcXVhbnRpdHlcIlxuICAgICAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICAgICAgcmVuZGVyPXsoeyBmaWVsZCB9KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICBpbnB1dE1vZGU9XCJkZWNpbWFsXCJcbiAgICAgICAgICAgICAgICAgICAgey4uLmZpZWxkfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjBcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgYmctc3VyZmFjZSBib3JkZXItMiBib3JkZXItYm9yZGVyIHJvdW5kZWQtbGcgcHgtNCBweS0zIHRleHQtYmFzZSB0cmFuc2l0aW9uLWFsbCBmb2N1czpib3JkZXItcHJpbWFyeS1ibHVlIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5LWJsdWUvMjAgdGV4dC10ZXh0LXByaW1hcnkgJHtlcnJvcnMuaW52ZXN0bWVudF9xdWFudGl0eSA/ICdib3JkZXItZXJyb3ItcmVkJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIHtlcnJvcnMuaW52ZXN0bWVudF9xdWFudGl0eSAmJiAoXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWVycm9yLXJlZFwiPntlcnJvcnMuaW52ZXN0bWVudF9xdWFudGl0eS5tZXNzYWdlfTwvcD5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXRleHQtcHJpbWFyeVwiPlxuICAgICAgICAgICAgICAgIFByaWNlIHBlciBTaGFyZSAqXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxDb250cm9sbGVyXG4gICAgICAgICAgICAgICAgbmFtZT1cImludmVzdG1lbnRfcHJpY2VcIlxuICAgICAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICAgICAgcmVuZGVyPXsoeyBmaWVsZCB9KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICBpbnB1dE1vZGU9XCJkZWNpbWFsXCJcbiAgICAgICAgICAgICAgICAgICAgey4uLmZpZWxkfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjAuMDBcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgYmctc3VyZmFjZSBib3JkZXItMiBib3JkZXItYm9yZGVyIHJvdW5kZWQtbGcgcHgtNCBweS0zIHRleHQtYmFzZSB0cmFuc2l0aW9uLWFsbCBmb2N1czpib3JkZXItcHJpbWFyeS1ibHVlIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5LWJsdWUvMjAgdGV4dC10ZXh0LXByaW1hcnkgJHtlcnJvcnMuaW52ZXN0bWVudF9wcmljZSA/ICdib3JkZXItZXJyb3ItcmVkJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIHtlcnJvcnMuaW52ZXN0bWVudF9wcmljZSAmJiAoXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWVycm9yLXJlZFwiPntlcnJvcnMuaW52ZXN0bWVudF9wcmljZS5tZXNzYWdlfTwvcD5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBBbW91bnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC10ZXh0LXByaW1hcnlcIj5cbiAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdkaXZpZGVuZCcgPyAnRGl2aWRlbmQgQW1vdW50IConIDogJ1RvdGFsIEFtb3VudCAqJ31cbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxDb250cm9sbGVyXG4gICAgICAgICAgICBuYW1lPVwiYW1vdW50XCJcbiAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIGlucHV0TW9kZT1cImRlY2ltYWxcIlxuICAgICAgICAgICAgICAgIHsuLi5maWVsZH1cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjAuMDBcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBiZy1zdXJmYWNlIGJvcmRlci0yIGJvcmRlci1ib3JkZXIgcm91bmRlZC1sZyBweC00IHB5LTMgdGV4dC1iYXNlIHRyYW5zaXRpb24tYWxsIGZvY3VzOmJvcmRlci1wcmltYXJ5LWJsdWUgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktYmx1ZS8yMCB0ZXh0LXRleHQtcHJpbWFyeSAke2Vycm9ycy5hbW91bnQgPyAnYm9yZGVyLWVycm9yLXJlZCcgOiAnJ31gfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAvPlxuICAgICAgICAgIHtlcnJvcnMuYW1vdW50ICYmIChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1lcnJvci1yZWRcIj57ZXJyb3JzLmFtb3VudC5tZXNzYWdlfTwvcD5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRmVlcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXRleHQtcHJpbWFyeVwiPlxuICAgICAgICAgICAgRmVlc1xuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPENvbnRyb2xsZXJcbiAgICAgICAgICAgIG5hbWU9XCJmZWVzXCJcbiAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIGlucHV0TW9kZT1cImRlY2ltYWxcIlxuICAgICAgICAgICAgICAgIHsuLi5maWVsZH1cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjAuMDBcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBiZy1zdXJmYWNlIGJvcmRlci0yIGJvcmRlci1ib3JkZXIgcm91bmRlZC1sZyBweC00IHB5LTMgdGV4dC1iYXNlIHRyYW5zaXRpb24tYWxsIGZvY3VzOmJvcmRlci1wcmltYXJ5LWJsdWUgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktYmx1ZS8yMCB0ZXh0LXRleHQtcHJpbWFyeSAke2Vycm9ycy5mZWVzID8gJ2JvcmRlci1lcnJvci1yZWQnIDogJyd9YH1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgLz5cbiAgICAgICAgICB7ZXJyb3JzLmZlZXMgJiYgKFxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWVycm9yLXJlZFwiPntlcnJvcnMuZmVlcy5tZXNzYWdlfTwvcD5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRGVzY3JpcHRpb24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC10ZXh0LXByaW1hcnlcIj5cbiAgICAgICAgICAgIERlc2NyaXB0aW9uXG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8Q29udHJvbGxlclxuICAgICAgICAgICAgbmFtZT1cImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgey4uLmZpZWxkfVxuICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJBZGQgYSBub3RlIGFib3V0IHRoaXMgaW52ZXN0bWVudCB0cmFuc2FjdGlvbi4uLlwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXN1cmZhY2UgYm9yZGVyLTIgYm9yZGVyLWJvcmRlciByb3VuZGVkLWxnIHB4LTQgcHktMyB0ZXh0LWJhc2UgdHJhbnNpdGlvbi1hbGwgZm9jdXM6Ym9yZGVyLXByaW1hcnktYmx1ZSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS1ibHVlLzIwIHRleHQtdGV4dC1wcmltYXJ5IHJlc2l6ZS1ub25lXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFRyYW5zYWN0aW9uIERhdGUgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC10ZXh0LXByaW1hcnlcIj5cbiAgICAgICAgICAgIERhdGUgKlxuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPENvbnRyb2xsZXJcbiAgICAgICAgICAgIG5hbWU9XCJ0cmFuc2FjdGlvbl9kYXRlXCJcbiAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxuICAgICAgICAgICAgICAgIHsuLi5maWVsZH1cbiAgICAgICAgICAgICAgICB2YWx1ZT17ZmllbGQudmFsdWUgaW5zdGFuY2VvZiBEYXRlID8gZmllbGQudmFsdWUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdIDogZmllbGQudmFsdWV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBmaWVsZC5vbkNoYW5nZShuZXcgRGF0ZShlLnRhcmdldC52YWx1ZSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBiZy1zdXJmYWNlIGJvcmRlci0yIGJvcmRlci1ib3JkZXIgcm91bmRlZC1sZyBweC00IHB5LTMgdGV4dC1iYXNlIHRyYW5zaXRpb24tYWxsIGZvY3VzOmJvcmRlci1wcmltYXJ5LWJsdWUgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktYmx1ZS8yMCB0ZXh0LXRleHQtcHJpbWFyeSAke2Vycm9ycy50cmFuc2FjdGlvbl9kYXRlID8gJ2JvcmRlci1lcnJvci1yZWQnIDogJyd9YH1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgLz5cbiAgICAgICAgICB7ZXJyb3JzLnRyYW5zYWN0aW9uX2RhdGUgJiYgKFxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWVycm9yLXJlZFwiPntlcnJvcnMudHJhbnNhY3Rpb25fZGF0ZS5tZXNzYWdlfTwvcD5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU3VibWl0IEJ1dHRvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC00XCI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nIHx8IGxvYWRpbmd9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctcHJpbWFyeS1ibHVlIGhvdmVyOmJnLXByaW1hcnktYmx1ZS85MCBkaXNhYmxlZDpiZy1wcmltYXJ5LWJsdWUvNTAgdGV4dC13aGl0ZSBmb250LW1lZGl1bSBweS0zIHB4LTQgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzU3VibWl0dGluZyB8fCBsb2FkaW5nID8gJ1Byb2Nlc3NpbmcuLi4nIDogXG4gICAgICAgICAgICAgaW5pdGlhbERhdGEgPyAnVXBkYXRlIEludmVzdG1lbnQnIDogXG4gICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnYnV5JyA/ICdCdXkgSW52ZXN0bWVudCcgOlxuICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ3NlbGwnID8gJ1NlbGwgSW52ZXN0bWVudCcgOiAnUmVjb3JkIERpdmlkZW5kJ31cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Zvcm0+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlRm9ybSIsIkNvbnRyb2xsZXIiLCJ6b2RSZXNvbHZlciIsInoiLCJBY2NvdW50U2VydmljZSIsIkludmVzdG1lbnRTZXJ2aWNlIiwiVHJhbnNmZXJTZXJ2aWNlIiwidG9hc3QiLCJpbnZlc3RtZW50Rm9ybVNjaGVtYSIsIm9iamVjdCIsInRyYW5zYWN0aW9uX3R5cGUiLCJlbnVtIiwiYW1vdW50Iiwic3RyaW5nIiwibWluIiwidHJhbnNmb3JtIiwidmFsIiwicGFyc2VGbG9hdCIsImludmVzdG1lbnRfYWNjb3VudF9pZCIsInV1aWQiLCJmdW5kaW5nX2FjY291bnRfaWQiLCJvcHRpb25hbCIsImludmVzdG1lbnRfc3ltYm9sIiwiaW52ZXN0bWVudF9xdWFudGl0eSIsInVuZGVmaW5lZCIsImludmVzdG1lbnRfcHJpY2UiLCJkZXNjcmlwdGlvbiIsInRyYW5zYWN0aW9uX2RhdGUiLCJkYXRlIiwiZmVlcyIsInJlZmluZSIsImRhdGEiLCJtZXNzYWdlIiwicGF0aCIsIkludmVzdG1lbnRGb3JtIiwib25TdWJtaXQiLCJsb2FkaW5nIiwiY2xhc3NOYW1lIiwiaW5pdGlhbERhdGEiLCJjb21wYWN0IiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwiYWNjb3VudHMiLCJzZXRBY2NvdW50cyIsImxvYWRpbmdEYXRhIiwic2V0TG9hZGluZ0RhdGEiLCJmb3JtIiwicmVzb2x2ZXIiLCJkZWZhdWx0VmFsdWVzIiwidG9TdHJpbmciLCJhY2NvdW50X2lkIiwiRGF0ZSIsImNvbnRyb2wiLCJoYW5kbGVTdWJtaXQiLCJyZXNldCIsInNldFZhbHVlIiwid2F0Y2giLCJmb3JtU3RhdGUiLCJlcnJvcnMiLCJpc1N1Ym1pdHRpbmciLCJ0cmFuc2FjdGlvblR5cGUiLCJsb2FkQWNjb3VudHMiLCJhY2NvdW50c0RhdGEiLCJnZXRBY2NvdW50cyIsImVycm9yIiwiY29uc29sZSIsInRyYW5zYWN0aW9uVHlwZU1hcCIsImdldEludmVzdG1lbnRBY2NvdW50cyIsImZpbHRlciIsImFjYyIsImFjY291bnRfdHlwZSIsImdldEZ1bmRpbmdBY2NvdW50cyIsImhhbmRsZUZvcm1TdWJtaXQiLCJjcmVhdGVUcmFuc2ZlciIsImZyb21fYWNjb3VudF9pZCIsInRvX2FjY291bnRfaWQiLCJ0b0lTT1N0cmluZyIsInNwbGl0IiwiY3JlYXRlSW52ZXN0bWVudFRyYW5zYWN0aW9uIiwiZGl2IiwidGFicyIsImlkIiwibGFiZWwiLCJpY29uIiwibWFwIiwidGFiIiwiYnV0dG9uIiwidHlwZSIsIm9uQ2xpY2siLCJzcGFuIiwibmFtZSIsInJlbmRlciIsImZpZWxkIiwic2VsZWN0Iiwib3B0aW9uIiwidmFsdWUiLCJhY2NvdW50IiwiY3VycmVudF9iYWxhbmNlIiwidG9GaXhlZCIsInAiLCJpbnB1dCIsInBsYWNlaG9sZGVyIiwiaW5wdXRNb2RlIiwidGV4dGFyZWEiLCJyb3dzIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InvestmentForm.tsx\n"));

/***/ })

});