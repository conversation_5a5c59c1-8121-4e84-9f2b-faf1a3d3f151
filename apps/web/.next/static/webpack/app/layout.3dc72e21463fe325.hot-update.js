"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/../../packages/shared/src/lib/transactions.ts":
/*!*****************************************************!*\
  !*** ../../packages/shared/src/lib/transactions.ts ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionService: () => (/* binding */ TransactionService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _investments__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./investments */ \"(app-pages-browser)/../../packages/shared/src/lib/investments.ts\");\n\n\nclass TransactionService {\n    /**\n   * Create a transaction of any type\n   */ static async createTransaction(data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { TransferService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\"));\n        // Handle different transaction types\n        switch(data.transaction_type){\n            case 'transfer':\n                if (!data.account_id || !data.to_account_id) {\n                    throw new Error('Source and destination accounts are required for transfers');\n                }\n                const transfer = await TransferService.createTransfer({\n                    amount: data.amount,\n                    description: data.description,\n                    from_account_id: data.account_id,\n                    to_account_id: data.to_account_id,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees\n                });\n                // Return the outgoing transaction as the primary transaction\n                const { data: transferTransactions } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n            *,\\n            category:categories(*),\\n            account:accounts(*),\\n            to_account:accounts!transactions_to_account_id_fkey(*)\\n          \").eq('transfer_id', transfer.transfer_id).eq('account_id', data.account_id).single();\n                return transferTransactions;\n            case 'investment_buy':\n            case 'investment_sell':\n                if (!data.account_id || !data.investment_symbol || !data.investment_quantity || !data.investment_price) {\n                    throw new Error('Investment account, symbol, quantity, and price are required for investment transactions');\n                }\n                const investment = await _investments__WEBPACK_IMPORTED_MODULE_1__.InvestmentService.createInvestmentTransaction({\n                    amount: data.amount,\n                    description: data.description,\n                    account_id: data.account_id,\n                    investment_symbol: data.investment_symbol,\n                    investment_quantity: data.investment_quantity,\n                    investment_price: data.investment_price,\n                    transaction_type: data.transaction_type,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees\n                }, data.funding_account_id);\n                return investment;\n            case 'dividend':\n                // Handle dividend as a transfer from investment account to bank account\n                if (!data.account_id || !data.to_account_id) {\n                    throw new Error('Investment account and receiving account are required for dividend transactions');\n                }\n                const dividendTransfer = await TransferService.createTransfer({\n                    amount: data.amount,\n                    description: data.description || \"Dividend payment: \".concat(data.investment_symbol || 'Investment'),\n                    from_account_id: data.account_id,\n                    to_account_id: data.to_account_id,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees || 0\n                });\n                return dividendTransfer;\n            case 'income':\n            case 'expense':\n                if (!data.category_id || !data.account_id) {\n                    throw new Error('Category and account are required for income/expense transactions');\n                }\n                const transactionData = {\n                    amount: data.amount,\n                    description: data.description || null,\n                    category_id: data.category_id,\n                    account_id: data.account_id,\n                    transaction_type: data.transaction_type,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    transaction_status: 'completed',\n                    fees: data.fees || 0,\n                    user_id: user.id\n                };\n                const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(\"\\n            *,\\n            category:categories(*),\\n            account:accounts(*)\\n          \").single();\n                if (error) {\n                    throw new Error(\"Failed to create transaction: \".concat(error.message));\n                }\n                return transaction;\n            default:\n                throw new Error(\"Unsupported transaction type: \".concat(data.transaction_type));\n        }\n    }\n    /**\n   * Get all transactions for the current user with enhanced filtering\n   */ static async getTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \", {\n            count: 'exact'\n        }).eq('user_id', user.id).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        // Apply filters\n        if (options === null || options === void 0 ? void 0 : options.categoryId) {\n            query = query.eq('category_id', options.categoryId);\n        }\n        if (options === null || options === void 0 ? void 0 : options.accountId) {\n            query = query.or(\"account_id.eq.\".concat(options.accountId, \",to_account_id.eq.\").concat(options.accountId));\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.transactionType) {\n            if (Array.isArray(options.transactionType)) {\n                query = query.in('transaction_type', options.transactionType);\n            } else {\n                query = query.eq('transaction_type', options.transactionType);\n            }\n        }\n        if (options === null || options === void 0 ? void 0 : options.transactionStatus) {\n            query = query.eq('transaction_status', options.transactionStatus);\n        }\n        // Filter out transfers and investments if not explicitly requested\n        if (!(options === null || options === void 0 ? void 0 : options.includeTransfers) && !(options === null || options === void 0 ? void 0 : options.includeInvestments)) {\n            query = query.in('transaction_type', [\n                'income',\n                'expense',\n                'dividend'\n            ]);\n        } else if (!(options === null || options === void 0 ? void 0 : options.includeTransfers)) {\n            query = query.neq('transaction_type', 'transfer');\n        } else if (!(options === null || options === void 0 ? void 0 : options.includeInvestments)) {\n            query = query.not('transaction_type', 'in', '(investment_buy,investment_sell)');\n        }\n        if (options === null || options === void 0 ? void 0 : options.searchQuery) {\n            // Search in description, category name, and investment symbol\n            query = query.or(\"description.ilike.%\".concat(options.searchQuery, \"%,\") + \"investment_symbol.ilike.%\".concat(options.searchQuery, \"%\"));\n        }\n        // Apply pagination\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch transactions: \".concat(error.message));\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    /**\n   * Get a specific transaction by ID\n   */ static async getTransaction(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            throw new Error(\"Failed to fetch transaction: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update a transaction (limited to basic transactions, not transfers or investments)\n   */ static async updateTransaction(id, updates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // First check if this is a basic transaction (not transfer or investment)\n        const { data: existingTransaction } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, transfer_id').eq('id', id).eq('user_id', user.id).single();\n        if (!existingTransaction) {\n            throw new Error('Transaction not found');\n        }\n        if (existingTransaction.transaction_type === 'transfer' || existingTransaction.transaction_type === 'investment_buy' || existingTransaction.transaction_type === 'investment_sell') {\n            throw new Error('Cannot update transfer or investment transactions through this method');\n        }\n        const updateData = {\n            updated_at: new Date().toISOString()\n        };\n        if (updates.amount !== undefined) updateData.amount = updates.amount;\n        if (updates.description !== undefined) updateData.description = updates.description;\n        if (updates.category_id !== undefined) updateData.category_id = updates.category_id;\n        if (updates.fees !== undefined) updateData.fees = updates.fees;\n        if (updates.transaction_date !== undefined) {\n            updateData.transaction_date = updates.transaction_date.toISOString().split('T')[0];\n        }\n        const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').update(updateData).eq('id', id).eq('user_id', user.id).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to update transaction: \".concat(error.message));\n        }\n        return transaction;\n    }\n    /**\n   * Delete a transaction\n   */ static async deleteTransaction(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if this is a transfer transaction\n        const { data: transaction } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, transfer_id').eq('id', id).eq('user_id', user.id).single();\n        if (!transaction) {\n            throw new Error('Transaction not found');\n        }\n        if (transaction.transaction_type === 'transfer' && transaction.transfer_id) {\n            // Delete all transactions with the same transfer_id\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('transfer_id', transaction.transfer_id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete transfer: \".concat(error.message));\n            }\n        } else {\n            // Delete single transaction\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete transaction: \".concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get transaction summary for a date range\n   */ static async getTransactionSummary(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, amount').eq('user_id', user.id).eq('transaction_status', 'completed');\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.accountId) {\n            query = query.or(\"account_id.eq.\".concat(options.accountId, \",to_account_id.eq.\").concat(options.accountId));\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch transaction summary: \".concat(error.message));\n        }\n        const summary = {\n            totalIncome: 0,\n            totalExpenses: 0,\n            totalTransfers: 0,\n            totalInvestments: 0,\n            netFlow: 0,\n            transactionCount: (data === null || data === void 0 ? void 0 : data.length) || 0\n        };\n        data === null || data === void 0 ? void 0 : data.forEach((transaction)=>{\n            switch(transaction.transaction_type){\n                case 'income':\n                case 'dividend':\n                    summary.totalIncome += transaction.amount;\n                    break;\n                case 'expense':\n                    summary.totalExpenses += transaction.amount;\n                    break;\n                case 'transfer':\n                    summary.totalTransfers += transaction.amount;\n                    break;\n                case 'investment_buy':\n                case 'investment_sell':\n                    summary.totalInvestments += transaction.amount;\n                    break;\n            }\n        });\n        summary.netFlow = summary.totalIncome - summary.totalExpenses;\n        return summary;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL2xpYi90cmFuc2FjdGlvbnMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXFDO0FBSVk7QUFFMUMsTUFBTUU7SUFDWDs7R0FFQyxHQUNELGFBQWFDLGtCQUFrQkMsSUFtQjlCLEVBQXlCO1FBQ3hCLE1BQU0sRUFBRUEsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUFDTSxJQUFJLENBQUNDLE9BQU87UUFFdEQsSUFBSSxDQUFDRixNQUFNO1lBQ1QsTUFBTSxJQUFJRyxNQUFNO1FBQ2xCO1FBQ0EsTUFBTSxFQUFFQyxlQUFlLEVBQUUsR0FBRyxNQUFNLHlLQUFxQjtRQUV2RCxxQ0FBcUM7UUFDckMsT0FBUUwsS0FBS00sZ0JBQWdCO1lBQzNCLEtBQUs7Z0JBQ0gsSUFBSSxDQUFDTixLQUFLTyxVQUFVLElBQUksQ0FBQ1AsS0FBS1EsYUFBYSxFQUFFO29CQUMzQyxNQUFNLElBQUlKLE1BQU07Z0JBQ2xCO2dCQUVBLE1BQU1LLFdBQVcsTUFBTUosZ0JBQWdCSyxjQUFjLENBQUM7b0JBQ3BEQyxRQUFRWCxLQUFLVyxNQUFNO29CQUNuQkMsYUFBYVosS0FBS1ksV0FBVztvQkFDN0JDLGlCQUFpQmIsS0FBS08sVUFBVTtvQkFDaENDLGVBQWVSLEtBQUtRLGFBQWE7b0JBQ2pDTSxrQkFBa0JkLEtBQUtjLGdCQUFnQixDQUFDQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtvQkFDbkVDLE1BQU1qQixLQUFLaUIsSUFBSTtnQkFDakI7Z0JBRUEsNkRBQTZEO2dCQUM3RCxNQUFNLEVBQUVqQixNQUFNa0Isb0JBQW9CLEVBQUUsR0FBRyxNQUFNdEIsK0NBQVFBLENBQ2xEdUIsSUFBSSxDQUFDLGdCQUNMQyxNQUFNLENBQUUsMktBTVJDLEVBQUUsQ0FBQyxlQUFlWixTQUFTYSxXQUFXLEVBQ3RDRCxFQUFFLENBQUMsY0FBY3JCLEtBQUtPLFVBQVUsRUFDaENnQixNQUFNO2dCQUVULE9BQU9MO1lBRVQsS0FBSztZQUNMLEtBQUs7Z0JBQ0gsSUFBSSxDQUFDbEIsS0FBS08sVUFBVSxJQUFJLENBQUNQLEtBQUt3QixpQkFBaUIsSUFBSSxDQUFDeEIsS0FBS3lCLG1CQUFtQixJQUFJLENBQUN6QixLQUFLMEIsZ0JBQWdCLEVBQUU7b0JBQ3RHLE1BQU0sSUFBSXRCLE1BQU07Z0JBQ2xCO2dCQUVBLE1BQU11QixhQUFhLE1BQU05QiwyREFBaUJBLENBQUMrQiwyQkFBMkIsQ0FBQztvQkFDckVqQixRQUFRWCxLQUFLVyxNQUFNO29CQUNuQkMsYUFBYVosS0FBS1ksV0FBVztvQkFDN0JMLFlBQVlQLEtBQUtPLFVBQVU7b0JBQzNCaUIsbUJBQW1CeEIsS0FBS3dCLGlCQUFpQjtvQkFDekNDLHFCQUFxQnpCLEtBQUt5QixtQkFBbUI7b0JBQzdDQyxrQkFBa0IxQixLQUFLMEIsZ0JBQWdCO29CQUN2Q3BCLGtCQUFrQk4sS0FBS00sZ0JBQWdCO29CQUN2Q1Esa0JBQWtCZCxLQUFLYyxnQkFBZ0IsQ0FBQ0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7b0JBQ25FQyxNQUFNakIsS0FBS2lCLElBQUk7Z0JBQ2pCLEdBQUdqQixLQUFLNkIsa0JBQWtCO2dCQUUxQixPQUFPRjtZQUVULEtBQUs7Z0JBQ0gsd0VBQXdFO2dCQUN4RSxJQUFJLENBQUMzQixLQUFLTyxVQUFVLElBQUksQ0FBQ1AsS0FBS1EsYUFBYSxFQUFFO29CQUMzQyxNQUFNLElBQUlKLE1BQU07Z0JBQ2xCO2dCQUVBLE1BQU0wQixtQkFBbUIsTUFBTXpCLGdCQUFnQkssY0FBYyxDQUFDO29CQUM1REMsUUFBUVgsS0FBS1csTUFBTTtvQkFDbkJDLGFBQWFaLEtBQUtZLFdBQVcsSUFBSSxxQkFBNEQsT0FBdkNaLEtBQUt3QixpQkFBaUIsSUFBSTtvQkFDaEZYLGlCQUFpQmIsS0FBS08sVUFBVTtvQkFDaENDLGVBQWVSLEtBQUtRLGFBQWE7b0JBQ2pDTSxrQkFBa0JkLEtBQUtjLGdCQUFnQixDQUFDQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtvQkFDbkVDLE1BQU1qQixLQUFLaUIsSUFBSSxJQUFJO2dCQUNyQjtnQkFFQSxPQUFPYTtZQUVULEtBQUs7WUFDTCxLQUFLO2dCQUNILElBQUksQ0FBQzlCLEtBQUsrQixXQUFXLElBQUksQ0FBQy9CLEtBQUtPLFVBQVUsRUFBRTtvQkFDekMsTUFBTSxJQUFJSCxNQUFNO2dCQUNsQjtnQkFFQSxNQUFNNEIsa0JBQWdEO29CQUNwRHJCLFFBQVFYLEtBQUtXLE1BQU07b0JBQ25CQyxhQUFhWixLQUFLWSxXQUFXLElBQUk7b0JBQ2pDbUIsYUFBYS9CLEtBQUsrQixXQUFXO29CQUM3QnhCLFlBQVlQLEtBQUtPLFVBQVU7b0JBQzNCRCxrQkFBa0JOLEtBQUtNLGdCQUFnQjtvQkFDdkNRLGtCQUFrQmQsS0FBS2MsZ0JBQWdCLENBQUNDLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO29CQUNuRWlCLG9CQUFvQjtvQkFDcEJoQixNQUFNakIsS0FBS2lCLElBQUksSUFBSTtvQkFDbkJpQixTQUFTakMsS0FBS2tDLEVBQUU7Z0JBQ2xCO2dCQUVBLE1BQU0sRUFBRW5DLE1BQU1vQyxXQUFXLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU16QywrQ0FBUUEsQ0FDaER1QixJQUFJLENBQUMsZ0JBQ0xtQixNQUFNLENBQUNOLGlCQUNQWixNQUFNLENBQUUsc0dBS1JHLE1BQU07Z0JBRVQsSUFBSWMsT0FBTztvQkFDVCxNQUFNLElBQUlqQyxNQUFNLGlDQUErQyxPQUFkaUMsTUFBTUUsT0FBTztnQkFDaEU7Z0JBRUEsT0FBT0g7WUFFVDtnQkFDRSxNQUFNLElBQUloQyxNQUFNLGlDQUF1RCxPQUF0QkosS0FBS00sZ0JBQWdCO1FBQzFFO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFrQyxnQkFBZ0JDLE9BWTVCLEVBQW9EO1FBQ25ELE1BQU0sRUFBRXpDLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUUsR0FBRyxNQUFNTCwrQ0FBUUEsQ0FBQ00sSUFBSSxDQUFDQyxPQUFPO1FBRXRELElBQUksQ0FBQ0YsTUFBTTtZQUNULE1BQU0sSUFBSUcsTUFBTTtRQUNsQjtRQUVBLElBQUlzQyxRQUFROUMsK0NBQVFBLENBQ2pCdUIsSUFBSSxDQUFDLGdCQUNMQyxNQUFNLENBQUUsdUpBS047WUFBRXVCLE9BQU87UUFBUSxHQUNuQnRCLEVBQUUsQ0FBQyxXQUFXcEIsS0FBS2tDLEVBQUUsRUFDckJTLEtBQUssQ0FBQyxvQkFBb0I7WUFBRUMsV0FBVztRQUFNLEdBQzdDRCxLQUFLLENBQUMsY0FBYztZQUFFQyxXQUFXO1FBQU07UUFFMUMsZ0JBQWdCO1FBQ2hCLElBQUlKLG9CQUFBQSw4QkFBQUEsUUFBU0ssVUFBVSxFQUFFO1lBQ3ZCSixRQUFRQSxNQUFNckIsRUFBRSxDQUFDLGVBQWVvQixRQUFRSyxVQUFVO1FBQ3BEO1FBRUEsSUFBSUwsb0JBQUFBLDhCQUFBQSxRQUFTTSxTQUFTLEVBQUU7WUFDdEJMLFFBQVFBLE1BQU1NLEVBQUUsQ0FBQyxpQkFBdURQLE9BQXRDQSxRQUFRTSxTQUFTLEVBQUMsc0JBQXNDLE9BQWxCTixRQUFRTSxTQUFTO1FBQzNGO1FBRUEsSUFBSU4sb0JBQUFBLDhCQUFBQSxRQUFTUSxTQUFTLEVBQUU7WUFDdEJQLFFBQVFBLE1BQU1RLEdBQUcsQ0FBQyxvQkFBb0JULFFBQVFRLFNBQVM7UUFDekQ7UUFFQSxJQUFJUixvQkFBQUEsOEJBQUFBLFFBQVNVLE9BQU8sRUFBRTtZQUNwQlQsUUFBUUEsTUFBTVUsR0FBRyxDQUFDLG9CQUFvQlgsUUFBUVUsT0FBTztRQUN2RDtRQUVBLElBQUlWLG9CQUFBQSw4QkFBQUEsUUFBU1ksZUFBZSxFQUFFO1lBQzVCLElBQUlDLE1BQU1DLE9BQU8sQ0FBQ2QsUUFBUVksZUFBZSxHQUFHO2dCQUMxQ1gsUUFBUUEsTUFBTWMsRUFBRSxDQUFDLG9CQUFvQmYsUUFBUVksZUFBZTtZQUM5RCxPQUFPO2dCQUNMWCxRQUFRQSxNQUFNckIsRUFBRSxDQUFDLG9CQUFvQm9CLFFBQVFZLGVBQWU7WUFDOUQ7UUFDRjtRQUVBLElBQUlaLG9CQUFBQSw4QkFBQUEsUUFBU2dCLGlCQUFpQixFQUFFO1lBQzlCZixRQUFRQSxNQUFNckIsRUFBRSxDQUFDLHNCQUFzQm9CLFFBQVFnQixpQkFBaUI7UUFDbEU7UUFFQSxtRUFBbUU7UUFDbkUsSUFBSSxFQUFDaEIsb0JBQUFBLDhCQUFBQSxRQUFTaUIsZ0JBQWdCLEtBQUksRUFBQ2pCLG9CQUFBQSw4QkFBQUEsUUFBU2tCLGtCQUFrQixHQUFFO1lBQzlEakIsUUFBUUEsTUFBTWMsRUFBRSxDQUFDLG9CQUFvQjtnQkFBQztnQkFBVTtnQkFBVzthQUFXO1FBQ3hFLE9BQU8sSUFBSSxFQUFDZixvQkFBQUEsOEJBQUFBLFFBQVNpQixnQkFBZ0IsR0FBRTtZQUNyQ2hCLFFBQVFBLE1BQU1rQixHQUFHLENBQUMsb0JBQW9CO1FBQ3hDLE9BQU8sSUFBSSxFQUFDbkIsb0JBQUFBLDhCQUFBQSxRQUFTa0Isa0JBQWtCLEdBQUU7WUFDdkNqQixRQUFRQSxNQUFNbUIsR0FBRyxDQUFDLG9CQUFvQixNQUFNO1FBQzlDO1FBRUEsSUFBSXBCLG9CQUFBQSw4QkFBQUEsUUFBU3FCLFdBQVcsRUFBRTtZQUN4Qiw4REFBOEQ7WUFDOURwQixRQUFRQSxNQUFNTSxFQUFFLENBQ2Qsc0JBQTBDLE9BQXBCUCxRQUFRcUIsV0FBVyxFQUFDLFFBQzFDLDRCQUFnRCxPQUFwQnJCLFFBQVFxQixXQUFXLEVBQUM7UUFFcEQ7UUFFQSxtQkFBbUI7UUFDbkIsSUFBSXJCLG9CQUFBQSw4QkFBQUEsUUFBU3NCLEtBQUssRUFBRTtZQUNsQnJCLFFBQVFBLE1BQU1xQixLQUFLLENBQUN0QixRQUFRc0IsS0FBSztRQUNuQztRQUVBLElBQUl0QixvQkFBQUEsOEJBQUFBLFFBQVN1QixNQUFNLEVBQUU7WUFDbkJ0QixRQUFRQSxNQUFNdUIsS0FBSyxDQUFDeEIsUUFBUXVCLE1BQU0sRUFBRSxRQUFTQSxNQUFNLEdBQUl2QixDQUFBQSxRQUFRc0IsS0FBSyxJQUFJLEVBQUMsSUFBTTtRQUNqRjtRQUVBLE1BQU0sRUFBRS9ELElBQUksRUFBRXFDLEtBQUssRUFBRU0sS0FBSyxFQUFFLEdBQUcsTUFBTUQ7UUFFckMsSUFBSUwsT0FBTztZQUNULE1BQU0sSUFBSWpDLE1BQU0saUNBQStDLE9BQWRpQyxNQUFNRSxPQUFPO1FBQ2hFO1FBRUEsT0FBTztZQUNMdkMsTUFBTUE7WUFDTjJDLE9BQU9BLFNBQVM7UUFDbEI7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYXVCLGVBQWUvQixFQUFVLEVBQXlCO1FBQzdELE1BQU0sRUFBRW5DLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUUsR0FBRyxNQUFNTCwrQ0FBUUEsQ0FBQ00sSUFBSSxDQUFDQyxPQUFPO1FBRXRELElBQUksQ0FBQ0YsTUFBTTtZQUNULE1BQU0sSUFBSUcsTUFBTTtRQUNsQjtRQUVBLE1BQU0sRUFBRUosSUFBSSxFQUFFcUMsS0FBSyxFQUFFLEdBQUcsTUFBTXpDLCtDQUFRQSxDQUNuQ3VCLElBQUksQ0FBQyxnQkFDTEMsTUFBTSxDQUFFLHVKQU1SQyxFQUFFLENBQUMsTUFBTWMsSUFDVGQsRUFBRSxDQUFDLFdBQVdwQixLQUFLa0MsRUFBRSxFQUNyQlosTUFBTTtRQUVULElBQUljLE9BQU87WUFDVCxNQUFNLElBQUlqQyxNQUFNLGdDQUE4QyxPQUFkaUMsTUFBTUUsT0FBTztRQUMvRDtRQUVBLE9BQU92QztJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhbUUsa0JBQWtCaEMsRUFBVSxFQUFFaUMsT0FNMUMsRUFBeUI7UUFDeEIsTUFBTSxFQUFFcEUsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUFDTSxJQUFJLENBQUNDLE9BQU87UUFFdEQsSUFBSSxDQUFDRixNQUFNO1lBQ1QsTUFBTSxJQUFJRyxNQUFNO1FBQ2xCO1FBRUEsMEVBQTBFO1FBQzFFLE1BQU0sRUFBRUosTUFBTXFFLG1CQUFtQixFQUFFLEdBQUcsTUFBTXpFLCtDQUFRQSxDQUNqRHVCLElBQUksQ0FBQyxnQkFDTEMsTUFBTSxDQUFDLGlDQUNQQyxFQUFFLENBQUMsTUFBTWMsSUFDVGQsRUFBRSxDQUFDLFdBQVdwQixLQUFLa0MsRUFBRSxFQUNyQlosTUFBTTtRQUVULElBQUksQ0FBQzhDLHFCQUFxQjtZQUN4QixNQUFNLElBQUlqRSxNQUFNO1FBQ2xCO1FBRUEsSUFBSWlFLG9CQUFvQi9ELGdCQUFnQixLQUFLLGNBQ3pDK0Qsb0JBQW9CL0QsZ0JBQWdCLEtBQUssb0JBQ3pDK0Qsb0JBQW9CL0QsZ0JBQWdCLEtBQUssbUJBQW1CO1lBQzlELE1BQU0sSUFBSUYsTUFBTTtRQUNsQjtRQUVBLE1BQU1rRSxhQUEyQztZQUMvQ0MsWUFBWSxJQUFJQyxPQUFPekQsV0FBVztRQUNwQztRQUVBLElBQUlxRCxRQUFRekQsTUFBTSxLQUFLOEQsV0FBV0gsV0FBVzNELE1BQU0sR0FBR3lELFFBQVF6RCxNQUFNO1FBQ3BFLElBQUl5RCxRQUFReEQsV0FBVyxLQUFLNkQsV0FBV0gsV0FBVzFELFdBQVcsR0FBR3dELFFBQVF4RCxXQUFXO1FBQ25GLElBQUl3RCxRQUFRckMsV0FBVyxLQUFLMEMsV0FBV0gsV0FBV3ZDLFdBQVcsR0FBR3FDLFFBQVFyQyxXQUFXO1FBQ25GLElBQUlxQyxRQUFRbkQsSUFBSSxLQUFLd0QsV0FBV0gsV0FBV3JELElBQUksR0FBR21ELFFBQVFuRCxJQUFJO1FBQzlELElBQUltRCxRQUFRdEQsZ0JBQWdCLEtBQUsyRCxXQUFXO1lBQzFDSCxXQUFXeEQsZ0JBQWdCLEdBQUdzRCxRQUFRdEQsZ0JBQWdCLENBQUNDLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1FBQ3BGO1FBRUEsTUFBTSxFQUFFaEIsTUFBTW9DLFdBQVcsRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTXpDLCtDQUFRQSxDQUNoRHVCLElBQUksQ0FBQyxnQkFDTHVELE1BQU0sQ0FBQ0osWUFDUGpELEVBQUUsQ0FBQyxNQUFNYyxJQUNUZCxFQUFFLENBQUMsV0FBV3BCLEtBQUtrQyxFQUFFLEVBQ3JCZixNQUFNLENBQUUsc0ZBS1JHLE1BQU07UUFFVCxJQUFJYyxPQUFPO1lBQ1QsTUFBTSxJQUFJakMsTUFBTSxpQ0FBK0MsT0FBZGlDLE1BQU1FLE9BQU87UUFDaEU7UUFFQSxPQUFPSDtJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhdUMsa0JBQWtCeEMsRUFBVSxFQUFpQjtRQUN4RCxNQUFNLEVBQUVuQyxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTUwsK0NBQVFBLENBQUNNLElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVCxNQUFNLElBQUlHLE1BQU07UUFDbEI7UUFFQSwwQ0FBMEM7UUFDMUMsTUFBTSxFQUFFSixNQUFNb0MsV0FBVyxFQUFFLEdBQUcsTUFBTXhDLCtDQUFRQSxDQUN6Q3VCLElBQUksQ0FBQyxnQkFDTEMsTUFBTSxDQUFDLGlDQUNQQyxFQUFFLENBQUMsTUFBTWMsSUFDVGQsRUFBRSxDQUFDLFdBQVdwQixLQUFLa0MsRUFBRSxFQUNyQlosTUFBTTtRQUVULElBQUksQ0FBQ2EsYUFBYTtZQUNoQixNQUFNLElBQUloQyxNQUFNO1FBQ2xCO1FBRUEsSUFBSWdDLFlBQVk5QixnQkFBZ0IsS0FBSyxjQUFjOEIsWUFBWWQsV0FBVyxFQUFFO1lBQzFFLG9EQUFvRDtZQUNwRCxNQUFNLEVBQUVlLEtBQUssRUFBRSxHQUFHLE1BQU16QywrQ0FBUUEsQ0FDN0J1QixJQUFJLENBQUMsZ0JBQ0x5RCxNQUFNLEdBQ052RCxFQUFFLENBQUMsZUFBZWUsWUFBWWQsV0FBVyxFQUN6Q0QsRUFBRSxDQUFDLFdBQVdwQixLQUFLa0MsRUFBRTtZQUV4QixJQUFJRSxPQUFPO2dCQUNULE1BQU0sSUFBSWpDLE1BQU0sOEJBQTRDLE9BQWRpQyxNQUFNRSxPQUFPO1lBQzdEO1FBQ0YsT0FBTztZQUNMLDRCQUE0QjtZQUM1QixNQUFNLEVBQUVGLEtBQUssRUFBRSxHQUFHLE1BQU16QywrQ0FBUUEsQ0FDN0J1QixJQUFJLENBQUMsZ0JBQ0x5RCxNQUFNLEdBQ052RCxFQUFFLENBQUMsTUFBTWMsSUFDVGQsRUFBRSxDQUFDLFdBQVdwQixLQUFLa0MsRUFBRTtZQUV4QixJQUFJRSxPQUFPO2dCQUNULE1BQU0sSUFBSWpDLE1BQU0saUNBQStDLE9BQWRpQyxNQUFNRSxPQUFPO1lBQ2hFO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYXNDLHNCQUFzQnBDLE9BSWxDLEVBT0U7UUFDRCxNQUFNLEVBQUV6QyxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTUwsK0NBQVFBLENBQUNNLElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVCxNQUFNLElBQUlHLE1BQU07UUFDbEI7UUFFQSxJQUFJc0MsUUFBUTlDLCtDQUFRQSxDQUNqQnVCLElBQUksQ0FBQyxnQkFDTEMsTUFBTSxDQUFDLDRCQUNQQyxFQUFFLENBQUMsV0FBV3BCLEtBQUtrQyxFQUFFLEVBQ3JCZCxFQUFFLENBQUMsc0JBQXNCO1FBRTVCLElBQUlvQixvQkFBQUEsOEJBQUFBLFFBQVNRLFNBQVMsRUFBRTtZQUN0QlAsUUFBUUEsTUFBTVEsR0FBRyxDQUFDLG9CQUFvQlQsUUFBUVEsU0FBUztRQUN6RDtRQUVBLElBQUlSLG9CQUFBQSw4QkFBQUEsUUFBU1UsT0FBTyxFQUFFO1lBQ3BCVCxRQUFRQSxNQUFNVSxHQUFHLENBQUMsb0JBQW9CWCxRQUFRVSxPQUFPO1FBQ3ZEO1FBRUEsSUFBSVYsb0JBQUFBLDhCQUFBQSxRQUFTTSxTQUFTLEVBQUU7WUFDdEJMLFFBQVFBLE1BQU1NLEVBQUUsQ0FBQyxpQkFBdURQLE9BQXRDQSxRQUFRTSxTQUFTLEVBQUMsc0JBQXNDLE9BQWxCTixRQUFRTSxTQUFTO1FBQzNGO1FBRUEsTUFBTSxFQUFFL0MsSUFBSSxFQUFFcUMsS0FBSyxFQUFFLEdBQUcsTUFBTUs7UUFFOUIsSUFBSUwsT0FBTztZQUNULE1BQU0sSUFBSWpDLE1BQU0sd0NBQXNELE9BQWRpQyxNQUFNRSxPQUFPO1FBQ3ZFO1FBRUEsTUFBTXVDLFVBQVU7WUFDZEMsYUFBYTtZQUNiQyxlQUFlO1lBQ2ZDLGdCQUFnQjtZQUNoQkMsa0JBQWtCO1lBQ2xCQyxTQUFTO1lBQ1RDLGtCQUFrQnBGLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXFGLE1BQU0sS0FBSTtRQUNwQztRQUVBckYsaUJBQUFBLDJCQUFBQSxLQUFNc0YsT0FBTyxDQUFDbEQsQ0FBQUE7WUFDWixPQUFRQSxZQUFZOUIsZ0JBQWdCO2dCQUNsQyxLQUFLO2dCQUNMLEtBQUs7b0JBQ0h3RSxRQUFRQyxXQUFXLElBQUkzQyxZQUFZekIsTUFBTTtvQkFDekM7Z0JBQ0YsS0FBSztvQkFDSG1FLFFBQVFFLGFBQWEsSUFBSTVDLFlBQVl6QixNQUFNO29CQUMzQztnQkFDRixLQUFLO29CQUNIbUUsUUFBUUcsY0FBYyxJQUFJN0MsWUFBWXpCLE1BQU07b0JBQzVDO2dCQUNGLEtBQUs7Z0JBQ0wsS0FBSztvQkFDSG1FLFFBQVFJLGdCQUFnQixJQUFJOUMsWUFBWXpCLE1BQU07b0JBQzlDO1lBQ0o7UUFDRjtRQUVBbUUsUUFBUUssT0FBTyxHQUFHTCxRQUFRQyxXQUFXLEdBQUdELFFBQVFFLGFBQWE7UUFFN0QsT0FBT0Y7SUFDVDtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL3BhY2thZ2VzL3NoYXJlZC9zcmMvbGliL3RyYW5zYWN0aW9ucy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdXBhYmFzZSB9IGZyb20gJy4vc3VwYWJhc2UnXG5pbXBvcnQgdHlwZSB7IElUcmFuc2FjdGlvbiwgSUNhdGVnb3J5LCBJQWNjb3VudCwgVHJhbnNhY3Rpb25UeXBlIH0gZnJvbSAnLi4vdHlwZXMnXG5pbXBvcnQgdHlwZSB7IFRhYmxlc0luc2VydCwgVGFibGVzVXBkYXRlIH0gZnJvbSAnLi4vZGF0YWJhc2UudHlwZXMnXG5pbXBvcnQgeyBUcmFuc2ZlclNlcnZpY2UgfSBmcm9tICcuL3RyYW5zZmVycydcbmltcG9ydCB7IEludmVzdG1lbnRTZXJ2aWNlIH0gZnJvbSAnLi9pbnZlc3RtZW50cydcblxuZXhwb3J0IGNsYXNzIFRyYW5zYWN0aW9uU2VydmljZSB7XG4gIC8qKlxuICAgKiBDcmVhdGUgYSB0cmFuc2FjdGlvbiBvZiBhbnkgdHlwZVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGNyZWF0ZVRyYW5zYWN0aW9uKGRhdGE6IHtcbiAgICBhbW91bnQ6IG51bWJlclxuICAgIGRlc2NyaXB0aW9uPzogc3RyaW5nXG4gICAgdHJhbnNhY3Rpb25fZGF0ZTogRGF0ZVxuICAgIHRyYW5zYWN0aW9uX3R5cGU6IFRyYW5zYWN0aW9uVHlwZVxuICAgIGZlZXM/OiBudW1iZXJcbiAgICBcbiAgICAvLyBCYXNpYyB0cmFuc2FjdGlvbiBmaWVsZHNcbiAgICBjYXRlZ29yeV9pZD86IHN0cmluZ1xuICAgIGFjY291bnRfaWQ/OiBzdHJpbmdcbiAgICBcbiAgICAvLyBUcmFuc2Zlci1zcGVjaWZpYyBmaWVsZHNcbiAgICB0b19hY2NvdW50X2lkPzogc3RyaW5nXG4gICAgXG4gICAgLy8gSW52ZXN0bWVudC1zcGVjaWZpYyBmaWVsZHNcbiAgICBpbnZlc3RtZW50X3N5bWJvbD86IHN0cmluZ1xuICAgIGludmVzdG1lbnRfcXVhbnRpdHk/OiBudW1iZXJcbiAgICBpbnZlc3RtZW50X3ByaWNlPzogbnVtYmVyXG4gICAgZnVuZGluZ19hY2NvdW50X2lkPzogc3RyaW5nIC8vIEZvciBpbnZlc3RtZW50IHB1cmNoYXNlc1xuICB9KTogUHJvbWlzZTxJVHJhbnNhY3Rpb24+IHtcbiAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9IH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKVxuICAgIFxuICAgIGlmICghdXNlcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJylcbiAgICB9XG4gICAgY29uc3QgeyBUcmFuc2ZlclNlcnZpY2UgfSA9IGF3YWl0IGltcG9ydCgnLi90cmFuc2ZlcnMnKVxuXG4gICAgLy8gSGFuZGxlIGRpZmZlcmVudCB0cmFuc2FjdGlvbiB0eXBlc1xuICAgIHN3aXRjaCAoZGF0YS50cmFuc2FjdGlvbl90eXBlKSB7XG4gICAgICBjYXNlICd0cmFuc2Zlcic6XG4gICAgICAgIGlmICghZGF0YS5hY2NvdW50X2lkIHx8ICFkYXRhLnRvX2FjY291bnRfaWQpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1NvdXJjZSBhbmQgZGVzdGluYXRpb24gYWNjb3VudHMgYXJlIHJlcXVpcmVkIGZvciB0cmFuc2ZlcnMnKVxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBjb25zdCB0cmFuc2ZlciA9IGF3YWl0IFRyYW5zZmVyU2VydmljZS5jcmVhdGVUcmFuc2Zlcih7XG4gICAgICAgICAgYW1vdW50OiBkYXRhLmFtb3VudCxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogZGF0YS5kZXNjcmlwdGlvbixcbiAgICAgICAgICBmcm9tX2FjY291bnRfaWQ6IGRhdGEuYWNjb3VudF9pZCxcbiAgICAgICAgICB0b19hY2NvdW50X2lkOiBkYXRhLnRvX2FjY291bnRfaWQsXG4gICAgICAgICAgdHJhbnNhY3Rpb25fZGF0ZTogZGF0YS50cmFuc2FjdGlvbl9kYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgICAgICBmZWVzOiBkYXRhLmZlZXMsXG4gICAgICAgIH0pXG4gICAgICAgIFxuICAgICAgICAvLyBSZXR1cm4gdGhlIG91dGdvaW5nIHRyYW5zYWN0aW9uIGFzIHRoZSBwcmltYXJ5IHRyYW5zYWN0aW9uXG4gICAgICAgIGNvbnN0IHsgZGF0YTogdHJhbnNmZXJUcmFuc2FjdGlvbnMgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oJ3RyYW5zYWN0aW9ucycpXG4gICAgICAgICAgLnNlbGVjdChgXG4gICAgICAgICAgICAqLFxuICAgICAgICAgICAgY2F0ZWdvcnk6Y2F0ZWdvcmllcygqKSxcbiAgICAgICAgICAgIGFjY291bnQ6YWNjb3VudHMoKiksXG4gICAgICAgICAgICB0b19hY2NvdW50OmFjY291bnRzIXRyYW5zYWN0aW9uc190b19hY2NvdW50X2lkX2ZrZXkoKilcbiAgICAgICAgICBgKVxuICAgICAgICAgIC5lcSgndHJhbnNmZXJfaWQnLCB0cmFuc2Zlci50cmFuc2Zlcl9pZClcbiAgICAgICAgICAuZXEoJ2FjY291bnRfaWQnLCBkYXRhLmFjY291bnRfaWQpXG4gICAgICAgICAgLnNpbmdsZSgpXG4gICAgICAgIFxuICAgICAgICByZXR1cm4gdHJhbnNmZXJUcmFuc2FjdGlvbnMgYXMgSVRyYW5zYWN0aW9uXG5cbiAgICAgIGNhc2UgJ2ludmVzdG1lbnRfYnV5JzpcbiAgICAgIGNhc2UgJ2ludmVzdG1lbnRfc2VsbCc6XG4gICAgICAgIGlmICghZGF0YS5hY2NvdW50X2lkIHx8ICFkYXRhLmludmVzdG1lbnRfc3ltYm9sIHx8ICFkYXRhLmludmVzdG1lbnRfcXVhbnRpdHkgfHwgIWRhdGEuaW52ZXN0bWVudF9wcmljZSkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignSW52ZXN0bWVudCBhY2NvdW50LCBzeW1ib2wsIHF1YW50aXR5LCBhbmQgcHJpY2UgYXJlIHJlcXVpcmVkIGZvciBpbnZlc3RtZW50IHRyYW5zYWN0aW9ucycpXG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGNvbnN0IGludmVzdG1lbnQgPSBhd2FpdCBJbnZlc3RtZW50U2VydmljZS5jcmVhdGVJbnZlc3RtZW50VHJhbnNhY3Rpb24oe1xuICAgICAgICAgIGFtb3VudDogZGF0YS5hbW91bnQsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGRhdGEuZGVzY3JpcHRpb24sXG4gICAgICAgICAgYWNjb3VudF9pZDogZGF0YS5hY2NvdW50X2lkLFxuICAgICAgICAgIGludmVzdG1lbnRfc3ltYm9sOiBkYXRhLmludmVzdG1lbnRfc3ltYm9sLFxuICAgICAgICAgIGludmVzdG1lbnRfcXVhbnRpdHk6IGRhdGEuaW52ZXN0bWVudF9xdWFudGl0eSxcbiAgICAgICAgICBpbnZlc3RtZW50X3ByaWNlOiBkYXRhLmludmVzdG1lbnRfcHJpY2UsXG4gICAgICAgICAgdHJhbnNhY3Rpb25fdHlwZTogZGF0YS50cmFuc2FjdGlvbl90eXBlLFxuICAgICAgICAgIHRyYW5zYWN0aW9uX2RhdGU6IGRhdGEudHJhbnNhY3Rpb25fZGF0ZS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgICAgICAgZmVlczogZGF0YS5mZWVzLFxuICAgICAgICB9LCBkYXRhLmZ1bmRpbmdfYWNjb3VudF9pZClcbiAgICAgICAgXG4gICAgICAgIHJldHVybiBpbnZlc3RtZW50IGFzIElUcmFuc2FjdGlvblxuXG4gICAgICBjYXNlICdkaXZpZGVuZCc6XG4gICAgICAgIC8vIEhhbmRsZSBkaXZpZGVuZCBhcyBhIHRyYW5zZmVyIGZyb20gaW52ZXN0bWVudCBhY2NvdW50IHRvIGJhbmsgYWNjb3VudFxuICAgICAgICBpZiAoIWRhdGEuYWNjb3VudF9pZCB8fCAhZGF0YS50b19hY2NvdW50X2lkKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZlc3RtZW50IGFjY291bnQgYW5kIHJlY2VpdmluZyBhY2NvdW50IGFyZSByZXF1aXJlZCBmb3IgZGl2aWRlbmQgdHJhbnNhY3Rpb25zJylcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGRpdmlkZW5kVHJhbnNmZXIgPSBhd2FpdCBUcmFuc2ZlclNlcnZpY2UuY3JlYXRlVHJhbnNmZXIoe1xuICAgICAgICAgIGFtb3VudDogZGF0YS5hbW91bnQsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGRhdGEuZGVzY3JpcHRpb24gfHwgYERpdmlkZW5kIHBheW1lbnQ6ICR7ZGF0YS5pbnZlc3RtZW50X3N5bWJvbCB8fCAnSW52ZXN0bWVudCd9YCxcbiAgICAgICAgICBmcm9tX2FjY291bnRfaWQ6IGRhdGEuYWNjb3VudF9pZCxcbiAgICAgICAgICB0b19hY2NvdW50X2lkOiBkYXRhLnRvX2FjY291bnRfaWQsXG4gICAgICAgICAgdHJhbnNhY3Rpb25fZGF0ZTogZGF0YS50cmFuc2FjdGlvbl9kYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgICAgICBmZWVzOiBkYXRhLmZlZXMgfHwgMCxcbiAgICAgICAgfSlcblxuICAgICAgICByZXR1cm4gZGl2aWRlbmRUcmFuc2ZlciBhcyBJVHJhbnNhY3Rpb25cblxuICAgICAgY2FzZSAnaW5jb21lJzpcbiAgICAgIGNhc2UgJ2V4cGVuc2UnOlxuICAgICAgICBpZiAoIWRhdGEuY2F0ZWdvcnlfaWQgfHwgIWRhdGEuYWNjb3VudF9pZCkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignQ2F0ZWdvcnkgYW5kIGFjY291bnQgYXJlIHJlcXVpcmVkIGZvciBpbmNvbWUvZXhwZW5zZSB0cmFuc2FjdGlvbnMnKVxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgdHJhbnNhY3Rpb25EYXRhOiBUYWJsZXNJbnNlcnQ8J3RyYW5zYWN0aW9ucyc+ID0ge1xuICAgICAgICAgIGFtb3VudDogZGF0YS5hbW91bnQsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGRhdGEuZGVzY3JpcHRpb24gfHwgbnVsbCxcbiAgICAgICAgICBjYXRlZ29yeV9pZDogZGF0YS5jYXRlZ29yeV9pZCxcbiAgICAgICAgICBhY2NvdW50X2lkOiBkYXRhLmFjY291bnRfaWQsXG4gICAgICAgICAgdHJhbnNhY3Rpb25fdHlwZTogZGF0YS50cmFuc2FjdGlvbl90eXBlLFxuICAgICAgICAgIHRyYW5zYWN0aW9uX2RhdGU6IGRhdGEudHJhbnNhY3Rpb25fZGF0ZS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgICAgICAgdHJhbnNhY3Rpb25fc3RhdHVzOiAnY29tcGxldGVkJyxcbiAgICAgICAgICBmZWVzOiBkYXRhLmZlZXMgfHwgMCxcbiAgICAgICAgICB1c2VyX2lkOiB1c2VyLmlkLFxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgeyBkYXRhOiB0cmFuc2FjdGlvbiwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oJ3RyYW5zYWN0aW9ucycpXG4gICAgICAgICAgLmluc2VydCh0cmFuc2FjdGlvbkRhdGEpXG4gICAgICAgICAgLnNlbGVjdChgXG4gICAgICAgICAgICAqLFxuICAgICAgICAgICAgY2F0ZWdvcnk6Y2F0ZWdvcmllcygqKSxcbiAgICAgICAgICAgIGFjY291bnQ6YWNjb3VudHMoKilcbiAgICAgICAgICBgKVxuICAgICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICAgIGlmIChlcnJvcikge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSB0cmFuc2FjdGlvbjogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gdHJhbnNhY3Rpb24gYXMgSVRyYW5zYWN0aW9uXG5cbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgVW5zdXBwb3J0ZWQgdHJhbnNhY3Rpb24gdHlwZTogJHtkYXRhLnRyYW5zYWN0aW9uX3R5cGV9YClcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IGFsbCB0cmFuc2FjdGlvbnMgZm9yIHRoZSBjdXJyZW50IHVzZXIgd2l0aCBlbmhhbmNlZCBmaWx0ZXJpbmdcbiAgICovXG4gIHN0YXRpYyBhc3luYyBnZXRUcmFuc2FjdGlvbnMob3B0aW9ucz86IHtcbiAgICBsaW1pdD86IG51bWJlclxuICAgIG9mZnNldD86IG51bWJlclxuICAgIGNhdGVnb3J5SWQ/OiBzdHJpbmdcbiAgICBhY2NvdW50SWQ/OiBzdHJpbmdcbiAgICBzdGFydERhdGU/OiBzdHJpbmdcbiAgICBlbmREYXRlPzogc3RyaW5nXG4gICAgc2VhcmNoUXVlcnk/OiBzdHJpbmdcbiAgICB0cmFuc2FjdGlvblR5cGU/OiBUcmFuc2FjdGlvblR5cGUgfCBUcmFuc2FjdGlvblR5cGVbXVxuICAgIHRyYW5zYWN0aW9uU3RhdHVzPzogc3RyaW5nXG4gICAgaW5jbHVkZVRyYW5zZmVycz86IGJvb2xlYW5cbiAgICBpbmNsdWRlSW52ZXN0bWVudHM/OiBib29sZWFuXG4gIH0pOiBQcm9taXNlPHsgZGF0YTogSVRyYW5zYWN0aW9uW107IGNvdW50OiBudW1iZXIgfT4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIGxldCBxdWVyeSA9IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndHJhbnNhY3Rpb25zJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopLFxuICAgICAgICBhY2NvdW50OmFjY291bnRzKCopLFxuICAgICAgICB0b19hY2NvdW50OmFjY291bnRzIXRyYW5zYWN0aW9uc190b19hY2NvdW50X2lkX2ZrZXkoKilcbiAgICAgIGAsIHsgY291bnQ6ICdleGFjdCcgfSlcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAub3JkZXIoJ3RyYW5zYWN0aW9uX2RhdGUnLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcbiAgICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuXG4gICAgLy8gQXBwbHkgZmlsdGVyc1xuICAgIGlmIChvcHRpb25zPy5jYXRlZ29yeUlkKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdjYXRlZ29yeV9pZCcsIG9wdGlvbnMuY2F0ZWdvcnlJZClcbiAgICB9XG5cbiAgICBpZiAob3B0aW9ucz8uYWNjb3VudElkKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5Lm9yKGBhY2NvdW50X2lkLmVxLiR7b3B0aW9ucy5hY2NvdW50SWR9LHRvX2FjY291bnRfaWQuZXEuJHtvcHRpb25zLmFjY291bnRJZH1gKVxuICAgIH1cblxuICAgIGlmIChvcHRpb25zPy5zdGFydERhdGUpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuZ3RlKCd0cmFuc2FjdGlvbl9kYXRlJywgb3B0aW9ucy5zdGFydERhdGUpXG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnM/LmVuZERhdGUpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkubHRlKCd0cmFuc2FjdGlvbl9kYXRlJywgb3B0aW9ucy5lbmREYXRlKVxuICAgIH1cblxuICAgIGlmIChvcHRpb25zPy50cmFuc2FjdGlvblR5cGUpIHtcbiAgICAgIGlmIChBcnJheS5pc0FycmF5KG9wdGlvbnMudHJhbnNhY3Rpb25UeXBlKSkge1xuICAgICAgICBxdWVyeSA9IHF1ZXJ5LmluKCd0cmFuc2FjdGlvbl90eXBlJywgb3B0aW9ucy50cmFuc2FjdGlvblR5cGUpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCd0cmFuc2FjdGlvbl90eXBlJywgb3B0aW9ucy50cmFuc2FjdGlvblR5cGUpXG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnM/LnRyYW5zYWN0aW9uU3RhdHVzKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCd0cmFuc2FjdGlvbl9zdGF0dXMnLCBvcHRpb25zLnRyYW5zYWN0aW9uU3RhdHVzKVxuICAgIH1cblxuICAgIC8vIEZpbHRlciBvdXQgdHJhbnNmZXJzIGFuZCBpbnZlc3RtZW50cyBpZiBub3QgZXhwbGljaXRseSByZXF1ZXN0ZWRcbiAgICBpZiAoIW9wdGlvbnM/LmluY2x1ZGVUcmFuc2ZlcnMgJiYgIW9wdGlvbnM/LmluY2x1ZGVJbnZlc3RtZW50cykge1xuICAgICAgcXVlcnkgPSBxdWVyeS5pbigndHJhbnNhY3Rpb25fdHlwZScsIFsnaW5jb21lJywgJ2V4cGVuc2UnLCAnZGl2aWRlbmQnXSlcbiAgICB9IGVsc2UgaWYgKCFvcHRpb25zPy5pbmNsdWRlVHJhbnNmZXJzKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5Lm5lcSgndHJhbnNhY3Rpb25fdHlwZScsICd0cmFuc2ZlcicpXG4gICAgfSBlbHNlIGlmICghb3B0aW9ucz8uaW5jbHVkZUludmVzdG1lbnRzKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5Lm5vdCgndHJhbnNhY3Rpb25fdHlwZScsICdpbicsICcoaW52ZXN0bWVudF9idXksaW52ZXN0bWVudF9zZWxsKScpXG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnM/LnNlYXJjaFF1ZXJ5KSB7XG4gICAgICAvLyBTZWFyY2ggaW4gZGVzY3JpcHRpb24sIGNhdGVnb3J5IG5hbWUsIGFuZCBpbnZlc3RtZW50IHN5bWJvbFxuICAgICAgcXVlcnkgPSBxdWVyeS5vcihcbiAgICAgICAgYGRlc2NyaXB0aW9uLmlsaWtlLiUke29wdGlvbnMuc2VhcmNoUXVlcnl9JSxgICtcbiAgICAgICAgYGludmVzdG1lbnRfc3ltYm9sLmlsaWtlLiUke29wdGlvbnMuc2VhcmNoUXVlcnl9JWBcbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBBcHBseSBwYWdpbmF0aW9uXG4gICAgaWYgKG9wdGlvbnM/LmxpbWl0KSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmxpbWl0KG9wdGlvbnMubGltaXQpXG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnM/Lm9mZnNldCkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5yYW5nZShvcHRpb25zLm9mZnNldCwgKG9wdGlvbnMub2Zmc2V0ICsgKG9wdGlvbnMubGltaXQgfHwgMTApKSAtIDEpXG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciwgY291bnQgfSA9IGF3YWl0IHF1ZXJ5XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHRyYW5zYWN0aW9uczogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIGRhdGE6IGRhdGEgYXMgSVRyYW5zYWN0aW9uW10sXG4gICAgICBjb3VudDogY291bnQgfHwgMFxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgYSBzcGVjaWZpYyB0cmFuc2FjdGlvbiBieSBJRFxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldFRyYW5zYWN0aW9uKGlkOiBzdHJpbmcpOiBQcm9taXNlPElUcmFuc2FjdGlvbj4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndHJhbnNhY3Rpb25zJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopLFxuICAgICAgICBhY2NvdW50OmFjY291bnRzKCopLFxuICAgICAgICB0b19hY2NvdW50OmFjY291bnRzIXRyYW5zYWN0aW9uc190b19hY2NvdW50X2lkX2ZrZXkoKilcbiAgICAgIGApXG4gICAgICAuZXEoJ2lkJywgaWQpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHRyYW5zYWN0aW9uOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YSBhcyBJVHJhbnNhY3Rpb25cbiAgfVxuXG4gIC8qKlxuICAgKiBVcGRhdGUgYSB0cmFuc2FjdGlvbiAobGltaXRlZCB0byBiYXNpYyB0cmFuc2FjdGlvbnMsIG5vdCB0cmFuc2ZlcnMgb3IgaW52ZXN0bWVudHMpXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgdXBkYXRlVHJhbnNhY3Rpb24oaWQ6IHN0cmluZywgdXBkYXRlczoge1xuICAgIGFtb3VudD86IG51bWJlclxuICAgIGRlc2NyaXB0aW9uPzogc3RyaW5nXG4gICAgY2F0ZWdvcnlfaWQ/OiBzdHJpbmdcbiAgICB0cmFuc2FjdGlvbl9kYXRlPzogRGF0ZVxuICAgIGZlZXM/OiBudW1iZXJcbiAgfSk6IFByb21pc2U8SVRyYW5zYWN0aW9uPiB7XG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKClcbiAgICBcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgYXV0aGVudGljYXRlZCcpXG4gICAgfVxuXG4gICAgLy8gRmlyc3QgY2hlY2sgaWYgdGhpcyBpcyBhIGJhc2ljIHRyYW5zYWN0aW9uIChub3QgdHJhbnNmZXIgb3IgaW52ZXN0bWVudClcbiAgICBjb25zdCB7IGRhdGE6IGV4aXN0aW5nVHJhbnNhY3Rpb24gfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndHJhbnNhY3Rpb25zJylcbiAgICAgIC5zZWxlY3QoJ3RyYW5zYWN0aW9uX3R5cGUsIHRyYW5zZmVyX2lkJylcbiAgICAgIC5lcSgnaWQnLCBpZClcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmICghZXhpc3RpbmdUcmFuc2FjdGlvbikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdUcmFuc2FjdGlvbiBub3QgZm91bmQnKVxuICAgIH1cblxuICAgIGlmIChleGlzdGluZ1RyYW5zYWN0aW9uLnRyYW5zYWN0aW9uX3R5cGUgPT09ICd0cmFuc2ZlcicgfHwgXG4gICAgICAgIGV4aXN0aW5nVHJhbnNhY3Rpb24udHJhbnNhY3Rpb25fdHlwZSA9PT0gJ2ludmVzdG1lbnRfYnV5JyB8fCBcbiAgICAgICAgZXhpc3RpbmdUcmFuc2FjdGlvbi50cmFuc2FjdGlvbl90eXBlID09PSAnaW52ZXN0bWVudF9zZWxsJykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdDYW5ub3QgdXBkYXRlIHRyYW5zZmVyIG9yIGludmVzdG1lbnQgdHJhbnNhY3Rpb25zIHRocm91Z2ggdGhpcyBtZXRob2QnKVxuICAgIH1cblxuICAgIGNvbnN0IHVwZGF0ZURhdGE6IFRhYmxlc1VwZGF0ZTwndHJhbnNhY3Rpb25zJz4gPSB7XG4gICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgfVxuXG4gICAgaWYgKHVwZGF0ZXMuYW1vdW50ICE9PSB1bmRlZmluZWQpIHVwZGF0ZURhdGEuYW1vdW50ID0gdXBkYXRlcy5hbW91bnRcbiAgICBpZiAodXBkYXRlcy5kZXNjcmlwdGlvbiAhPT0gdW5kZWZpbmVkKSB1cGRhdGVEYXRhLmRlc2NyaXB0aW9uID0gdXBkYXRlcy5kZXNjcmlwdGlvblxuICAgIGlmICh1cGRhdGVzLmNhdGVnb3J5X2lkICE9PSB1bmRlZmluZWQpIHVwZGF0ZURhdGEuY2F0ZWdvcnlfaWQgPSB1cGRhdGVzLmNhdGVnb3J5X2lkXG4gICAgaWYgKHVwZGF0ZXMuZmVlcyAhPT0gdW5kZWZpbmVkKSB1cGRhdGVEYXRhLmZlZXMgPSB1cGRhdGVzLmZlZXNcbiAgICBpZiAodXBkYXRlcy50cmFuc2FjdGlvbl9kYXRlICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIHVwZGF0ZURhdGEudHJhbnNhY3Rpb25fZGF0ZSA9IHVwZGF0ZXMudHJhbnNhY3Rpb25fZGF0ZS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF1cbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGE6IHRyYW5zYWN0aW9uLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd0cmFuc2FjdGlvbnMnKVxuICAgICAgLnVwZGF0ZSh1cGRhdGVEYXRhKVxuICAgICAgLmVxKCdpZCcsIGlkKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopLFxuICAgICAgICBhY2NvdW50OmFjY291bnRzKCopXG4gICAgICBgKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHVwZGF0ZSB0cmFuc2FjdGlvbjogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIHRyYW5zYWN0aW9uIGFzIElUcmFuc2FjdGlvblxuICB9XG5cbiAgLyoqXG4gICAqIERlbGV0ZSBhIHRyYW5zYWN0aW9uXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZGVsZXRlVHJhbnNhY3Rpb24oaWQ6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHRoaXMgaXMgYSB0cmFuc2ZlciB0cmFuc2FjdGlvblxuICAgIGNvbnN0IHsgZGF0YTogdHJhbnNhY3Rpb24gfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndHJhbnNhY3Rpb25zJylcbiAgICAgIC5zZWxlY3QoJ3RyYW5zYWN0aW9uX3R5cGUsIHRyYW5zZmVyX2lkJylcbiAgICAgIC5lcSgnaWQnLCBpZClcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmICghdHJhbnNhY3Rpb24pIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVHJhbnNhY3Rpb24gbm90IGZvdW5kJylcbiAgICB9XG5cbiAgICBpZiAodHJhbnNhY3Rpb24udHJhbnNhY3Rpb25fdHlwZSA9PT0gJ3RyYW5zZmVyJyAmJiB0cmFuc2FjdGlvbi50cmFuc2Zlcl9pZCkge1xuICAgICAgLy8gRGVsZXRlIGFsbCB0cmFuc2FjdGlvbnMgd2l0aCB0aGUgc2FtZSB0cmFuc2Zlcl9pZFxuICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ3RyYW5zYWN0aW9ucycpXG4gICAgICAgIC5kZWxldGUoKVxuICAgICAgICAuZXEoJ3RyYW5zZmVyX2lkJywgdHJhbnNhY3Rpb24udHJhbnNmZXJfaWQpXG4gICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBkZWxldGUgdHJhbnNmZXI6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBEZWxldGUgc2luZ2xlIHRyYW5zYWN0aW9uXG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgndHJhbnNhY3Rpb25zJylcbiAgICAgICAgLmRlbGV0ZSgpXG4gICAgICAgIC5lcSgnaWQnLCBpZClcbiAgICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGRlbGV0ZSB0cmFuc2FjdGlvbjogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCB0cmFuc2FjdGlvbiBzdW1tYXJ5IGZvciBhIGRhdGUgcmFuZ2VcbiAgICovXG4gIHN0YXRpYyBhc3luYyBnZXRUcmFuc2FjdGlvblN1bW1hcnkob3B0aW9ucz86IHtcbiAgICBzdGFydERhdGU/OiBzdHJpbmdcbiAgICBlbmREYXRlPzogc3RyaW5nXG4gICAgYWNjb3VudElkPzogc3RyaW5nXG4gIH0pOiBQcm9taXNlPHtcbiAgICB0b3RhbEluY29tZTogbnVtYmVyXG4gICAgdG90YWxFeHBlbnNlczogbnVtYmVyXG4gICAgdG90YWxUcmFuc2ZlcnM6IG51bWJlclxuICAgIHRvdGFsSW52ZXN0bWVudHM6IG51bWJlclxuICAgIG5ldEZsb3c6IG51bWJlclxuICAgIHRyYW5zYWN0aW9uQ291bnQ6IG51bWJlclxuICB9PiB7XG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKClcbiAgICBcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgYXV0aGVudGljYXRlZCcpXG4gICAgfVxuXG4gICAgbGV0IHF1ZXJ5ID0gc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd0cmFuc2FjdGlvbnMnKVxuICAgICAgLnNlbGVjdCgndHJhbnNhY3Rpb25fdHlwZSwgYW1vdW50JylcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuZXEoJ3RyYW5zYWN0aW9uX3N0YXR1cycsICdjb21wbGV0ZWQnKVxuXG4gICAgaWYgKG9wdGlvbnM/LnN0YXJ0RGF0ZSkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5ndGUoJ3RyYW5zYWN0aW9uX2RhdGUnLCBvcHRpb25zLnN0YXJ0RGF0ZSlcbiAgICB9XG5cbiAgICBpZiAob3B0aW9ucz8uZW5kRGF0ZSkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5sdGUoJ3RyYW5zYWN0aW9uX2RhdGUnLCBvcHRpb25zLmVuZERhdGUpXG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnM/LmFjY291bnRJZCkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5vcihgYWNjb3VudF9pZC5lcS4ke29wdGlvbnMuYWNjb3VudElkfSx0b19hY2NvdW50X2lkLmVxLiR7b3B0aW9ucy5hY2NvdW50SWR9YClcbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBxdWVyeVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCB0cmFuc2FjdGlvbiBzdW1tYXJ5OiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICBjb25zdCBzdW1tYXJ5ID0ge1xuICAgICAgdG90YWxJbmNvbWU6IDAsXG4gICAgICB0b3RhbEV4cGVuc2VzOiAwLFxuICAgICAgdG90YWxUcmFuc2ZlcnM6IDAsXG4gICAgICB0b3RhbEludmVzdG1lbnRzOiAwLFxuICAgICAgbmV0RmxvdzogMCxcbiAgICAgIHRyYW5zYWN0aW9uQ291bnQ6IGRhdGE/Lmxlbmd0aCB8fCAwLFxuICAgIH1cblxuICAgIGRhdGE/LmZvckVhY2godHJhbnNhY3Rpb24gPT4ge1xuICAgICAgc3dpdGNoICh0cmFuc2FjdGlvbi50cmFuc2FjdGlvbl90eXBlKSB7XG4gICAgICAgIGNhc2UgJ2luY29tZSc6XG4gICAgICAgIGNhc2UgJ2RpdmlkZW5kJzpcbiAgICAgICAgICBzdW1tYXJ5LnRvdGFsSW5jb21lICs9IHRyYW5zYWN0aW9uLmFtb3VudFxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIGNhc2UgJ2V4cGVuc2UnOlxuICAgICAgICAgIHN1bW1hcnkudG90YWxFeHBlbnNlcyArPSB0cmFuc2FjdGlvbi5hbW91bnRcbiAgICAgICAgICBicmVha1xuICAgICAgICBjYXNlICd0cmFuc2Zlcic6XG4gICAgICAgICAgc3VtbWFyeS50b3RhbFRyYW5zZmVycyArPSB0cmFuc2FjdGlvbi5hbW91bnRcbiAgICAgICAgICBicmVha1xuICAgICAgICBjYXNlICdpbnZlc3RtZW50X2J1eSc6XG4gICAgICAgIGNhc2UgJ2ludmVzdG1lbnRfc2VsbCc6XG4gICAgICAgICAgc3VtbWFyeS50b3RhbEludmVzdG1lbnRzICs9IHRyYW5zYWN0aW9uLmFtb3VudFxuICAgICAgICAgIGJyZWFrXG4gICAgICB9XG4gICAgfSlcblxuICAgIHN1bW1hcnkubmV0RmxvdyA9IHN1bW1hcnkudG90YWxJbmNvbWUgLSBzdW1tYXJ5LnRvdGFsRXhwZW5zZXNcblxuICAgIHJldHVybiBzdW1tYXJ5XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJzdXBhYmFzZSIsIkludmVzdG1lbnRTZXJ2aWNlIiwiVHJhbnNhY3Rpb25TZXJ2aWNlIiwiY3JlYXRlVHJhbnNhY3Rpb24iLCJkYXRhIiwidXNlciIsImF1dGgiLCJnZXRVc2VyIiwiRXJyb3IiLCJUcmFuc2ZlclNlcnZpY2UiLCJ0cmFuc2FjdGlvbl90eXBlIiwiYWNjb3VudF9pZCIsInRvX2FjY291bnRfaWQiLCJ0cmFuc2ZlciIsImNyZWF0ZVRyYW5zZmVyIiwiYW1vdW50IiwiZGVzY3JpcHRpb24iLCJmcm9tX2FjY291bnRfaWQiLCJ0cmFuc2FjdGlvbl9kYXRlIiwidG9JU09TdHJpbmciLCJzcGxpdCIsImZlZXMiLCJ0cmFuc2ZlclRyYW5zYWN0aW9ucyIsImZyb20iLCJzZWxlY3QiLCJlcSIsInRyYW5zZmVyX2lkIiwic2luZ2xlIiwiaW52ZXN0bWVudF9zeW1ib2wiLCJpbnZlc3RtZW50X3F1YW50aXR5IiwiaW52ZXN0bWVudF9wcmljZSIsImludmVzdG1lbnQiLCJjcmVhdGVJbnZlc3RtZW50VHJhbnNhY3Rpb24iLCJmdW5kaW5nX2FjY291bnRfaWQiLCJkaXZpZGVuZFRyYW5zZmVyIiwiY2F0ZWdvcnlfaWQiLCJ0cmFuc2FjdGlvbkRhdGEiLCJ0cmFuc2FjdGlvbl9zdGF0dXMiLCJ1c2VyX2lkIiwiaWQiLCJ0cmFuc2FjdGlvbiIsImVycm9yIiwiaW5zZXJ0IiwibWVzc2FnZSIsImdldFRyYW5zYWN0aW9ucyIsIm9wdGlvbnMiLCJxdWVyeSIsImNvdW50Iiwib3JkZXIiLCJhc2NlbmRpbmciLCJjYXRlZ29yeUlkIiwiYWNjb3VudElkIiwib3IiLCJzdGFydERhdGUiLCJndGUiLCJlbmREYXRlIiwibHRlIiwidHJhbnNhY3Rpb25UeXBlIiwiQXJyYXkiLCJpc0FycmF5IiwiaW4iLCJ0cmFuc2FjdGlvblN0YXR1cyIsImluY2x1ZGVUcmFuc2ZlcnMiLCJpbmNsdWRlSW52ZXN0bWVudHMiLCJuZXEiLCJub3QiLCJzZWFyY2hRdWVyeSIsImxpbWl0Iiwib2Zmc2V0IiwicmFuZ2UiLCJnZXRUcmFuc2FjdGlvbiIsInVwZGF0ZVRyYW5zYWN0aW9uIiwidXBkYXRlcyIsImV4aXN0aW5nVHJhbnNhY3Rpb24iLCJ1cGRhdGVEYXRhIiwidXBkYXRlZF9hdCIsIkRhdGUiLCJ1bmRlZmluZWQiLCJ1cGRhdGUiLCJkZWxldGVUcmFuc2FjdGlvbiIsImRlbGV0ZSIsImdldFRyYW5zYWN0aW9uU3VtbWFyeSIsInN1bW1hcnkiLCJ0b3RhbEluY29tZSIsInRvdGFsRXhwZW5zZXMiLCJ0b3RhbFRyYW5zZmVycyIsInRvdGFsSW52ZXN0bWVudHMiLCJuZXRGbG93IiwidHJhbnNhY3Rpb25Db3VudCIsImxlbmd0aCIsImZvckVhY2giXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/transactions.ts\n"));

/***/ })

});