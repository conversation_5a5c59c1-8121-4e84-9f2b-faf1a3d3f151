"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/budgets/page",{

/***/ "(app-pages-browser)/../../packages/shared/src/index.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/index.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: () => (/* reexport safe */ _lib_analytics__WEBPACK_IMPORTED_MODULE_7__.AnalyticsService),\n/* harmony export */   BiometricService: () => (/* reexport safe */ _lib_biometric_web__WEBPACK_IMPORTED_MODULE_10__.BiometricService),\n/* harmony export */   BudgetService: () => (/* reexport safe */ _lib_budget__WEBPACK_IMPORTED_MODULE_6__.BudgetService),\n/* harmony export */   CSVImportService: () => (/* reexport safe */ _lib_csv_import__WEBPACK_IMPORTED_MODULE_9__.CSVImportService),\n/* harmony export */   Constants: () => (/* reexport safe */ _database_types__WEBPACK_IMPORTED_MODULE_3__.Constants),\n/* harmony export */   ExpenseService: () => (/* reexport safe */ _lib_expenses__WEBPACK_IMPORTED_MODULE_5__.ExpenseService),\n/* harmony export */   RecurringTransactionService: () => (/* reexport safe */ _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__.RecurringTransactionService),\n/* harmony export */   accountSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.accountSchema),\n/* harmony export */   budgetFormInputSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_13__.budgetFormInputSchema),\n/* harmony export */   budgetFormSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_13__.budgetFormSchema),\n/* harmony export */   budgetSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_13__.budgetSchema),\n/* harmony export */   calculateBudgetProgress: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.calculateBudgetProgress),\n/* harmony export */   categorySchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.categorySchema),\n/* harmony export */   expenseFormInputSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_12__.expenseFormInputSchema),\n/* harmony export */   expenseFormSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_12__.expenseFormSchema),\n/* harmony export */   expenseSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_12__.expenseSchema),\n/* harmony export */   formatCurrency: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency),\n/* harmony export */   formatDate: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatDate),\n/* harmony export */   investmentSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.investmentSchema),\n/* harmony export */   legacyTransactionSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.legacyTransactionSchema),\n/* harmony export */   resetPasswordSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_11__.resetPasswordSchema),\n/* harmony export */   signInSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_11__.signInSchema),\n/* harmony export */   signUpSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_11__.signUpSchema),\n/* harmony export */   supabase: () => (/* reexport safe */ _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase),\n/* harmony export */   supabaseMobile: () => (/* reexport safe */ _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_15__.supabase),\n/* harmony export */   transferSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.transferSchema),\n/* harmony export */   useCurrencyStore: () => (/* reexport safe */ _stores_currencyStore__WEBPACK_IMPORTED_MODULE_14__.useCurrencyStore)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/../../packages/shared/src/types.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/../../packages/shared/src/utils.ts\");\n/* harmony import */ var _validators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./validators */ \"(app-pages-browser)/../../packages/shared/src/validators.ts\");\n/* harmony import */ var _database_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./database.types */ \"(app-pages-browser)/../../packages/shared/src/database.types.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _lib_expenses__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/expenses */ \"(app-pages-browser)/../../packages/shared/src/lib/expenses.ts\");\n/* harmony import */ var _lib_budget__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/budget */ \"(app-pages-browser)/../../packages/shared/src/lib/budget.ts\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/analytics */ \"(app-pages-browser)/../../packages/shared/src/lib/analytics.ts\");\n/* harmony import */ var _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/recurring-transactions */ \"(app-pages-browser)/../../packages/shared/src/lib/recurring-transactions.ts\");\n/* harmony import */ var _lib_csv_import__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/csv-import */ \"(app-pages-browser)/../../packages/shared/src/lib/csv-import.ts\");\n/* harmony import */ var _lib_biometric_web__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./lib/biometric.web */ \"(app-pages-browser)/../../packages/shared/src/lib/biometric.web.ts\");\n/* harmony import */ var _schemas_auth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./schemas/auth */ \"(app-pages-browser)/../../packages/shared/src/schemas/auth.ts\");\n/* harmony import */ var _schemas_expense__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./schemas/expense */ \"(app-pages-browser)/../../packages/shared/src/schemas/expense.ts\");\n/* harmony import */ var _schemas_budget__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./schemas/budget */ \"(app-pages-browser)/../../packages/shared/src/schemas/budget.ts\");\n/* harmony import */ var _stores_currencyStore__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./stores/currencyStore */ \"(app-pages-browser)/../../packages/shared/src/stores/currencyStore.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./lib/supabase.mobile */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.mobile.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(_lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_15__);\n// Shared business logic and utilities\n\n\n\n\n\n\n\n\n\n\n// Platform-specific biometric exports\n\n\n\n\n\n// Platform-specific exports (explicit re-export to avoid naming conflicts)\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsc0NBQXNDO0FBQ2Q7QUFDQTtBQUNLO0FBQ0k7QUFDRjtBQUNBO0FBQ0Y7QUFDRztBQUNhO0FBQ1o7QUFDakMsc0NBQXNDO0FBQ0Y7QUFDTDtBQUNHO0FBQ0Q7QUFDTTtBQUV2QywyRUFBMkU7QUFDUiIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9wYWNrYWdlcy9zaGFyZWQvc3JjL2luZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNoYXJlZCBidXNpbmVzcyBsb2dpYyBhbmQgdXRpbGl0aWVzXG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJztcbmV4cG9ydCAqIGZyb20gJy4vdXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi92YWxpZGF0b3JzJztcbmV4cG9ydCAqIGZyb20gJy4vZGF0YWJhc2UudHlwZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvc3VwYWJhc2UnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvZXhwZW5zZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvYnVkZ2V0JztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2FuYWx5dGljcyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9yZWN1cnJpbmctdHJhbnNhY3Rpb25zJztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2Nzdi1pbXBvcnQnO1xuLy8gUGxhdGZvcm0tc3BlY2lmaWMgYmlvbWV0cmljIGV4cG9ydHNcbmV4cG9ydCAqIGZyb20gJy4vbGliL2Jpb21ldHJpYy53ZWInO1xuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWFzL2F1dGgnO1xuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWFzL2V4cGVuc2UnO1xuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWFzL2J1ZGdldCc7XG5leHBvcnQgKiBmcm9tICcuL3N0b3Jlcy9jdXJyZW5jeVN0b3JlJztcblxuLy8gUGxhdGZvcm0tc3BlY2lmaWMgZXhwb3J0cyAoZXhwbGljaXQgcmUtZXhwb3J0IHRvIGF2b2lkIG5hbWluZyBjb25mbGljdHMpXG5leHBvcnQgeyBzdXBhYmFzZSBhcyBzdXBhYmFzZU1vYmlsZSB9IGZyb20gJy4vbGliL3N1cGFiYXNlLm1vYmlsZSc7Il0sIm5hbWVzIjpbInN1cGFiYXNlIiwic3VwYWJhc2VNb2JpbGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/assets.ts":
/*!***********************************************!*\
  !*** ../../packages/shared/src/lib/assets.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetClassService: () => (/* binding */ AssetClassService),\n/* harmony export */   AssetService: () => (/* binding */ AssetService),\n/* harmony export */   HoldingService: () => (/* binding */ HoldingService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n\nclass AssetClassService {\n    /**\n   * Get all asset classes\n   */ static async getAssetClasses() {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('asset_classes').select('*').eq('is_active', true).order('name');\n        if (error) {\n            throw new Error(\"Failed to fetch asset classes: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get a specific asset class by ID\n   */ static async getAssetClass(id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('asset_classes').select('*').eq('id', id).single();\n        if (error) {\n            throw new Error(\"Failed to fetch asset class: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Create a new asset class\n   */ static async createAssetClass(assetClassData) {\n        const insertData = {\n            ...assetClassData,\n            is_active: true\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('asset_classes').insert(insertData).select('*').single();\n        if (error) {\n            throw new Error(\"Failed to create asset class: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update an existing asset class\n   */ static async updateAssetClass(id, updates) {\n        const updateData = {\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('asset_classes').update(updateData).eq('id', id).select('*').single();\n        if (error) {\n            throw new Error(\"Failed to update asset class: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Initialize default asset classes\n   */ static async initializeDefaultAssetClasses() {\n        const { DEFAULT_ASSET_CLASSES } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_packages_shared_src_types_assets_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../types/assets */ \"(app-pages-browser)/../../packages/shared/src/types/assets.ts\"));\n        for (const assetClass of DEFAULT_ASSET_CLASSES){\n            try {\n                await this.createAssetClass(assetClass);\n            } catch (error) {\n                // Skip if already exists\n                console.log(\"Asset class \".concat(assetClass.name, \" may already exist\"));\n            }\n        }\n    }\n}\nclass AssetService {\n    /**\n   * Get all assets\n   */ static async getAssets(options) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('assets').select(\"\\n        *,\\n        asset_class:asset_classes(*)\\n      \").order('symbol');\n        if (options === null || options === void 0 ? void 0 : options.asset_class_id) {\n            query = query.eq('asset_class_id', options.asset_class_id);\n        }\n        if ((options === null || options === void 0 ? void 0 : options.is_active) !== undefined) {\n            query = query.eq('is_active', options.is_active);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch assets: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get a specific asset by ID\n   */ static async getAsset(id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('assets').select(\"\\n        *,\\n        asset_class:asset_classes(*)\\n      \").eq('id', id).single();\n        if (error) {\n            throw new Error(\"Failed to fetch asset: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get asset by symbol\n   */ static async getAssetBySymbol(symbol) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('assets').select(\"\\n        *,\\n        asset_class:asset_classes(*)\\n      \").eq('symbol', symbol.toUpperCase()).eq('is_active', true).single();\n        if (error && error.code !== 'PGRST116') {\n            throw new Error(\"Failed to fetch asset: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Create a new asset\n   */ static async createAsset(assetData) {\n        const insertData = {\n            ...assetData,\n            symbol: assetData.symbol.toUpperCase(),\n            is_active: true\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('assets').insert(insertData).select(\"\\n        *,\\n        asset_class:asset_classes(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to create asset: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update an existing asset\n   */ static async updateAsset(id, updates) {\n        const updateData = {\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        if (updates.symbol) {\n            updateData.symbol = updates.symbol.toUpperCase();\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('assets').update(updateData).eq('id', id).select(\"\\n        *,\\n        asset_class:asset_classes(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to update asset: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Search assets by symbol or name\n   */ static async searchAssets(query) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('assets').select(\"\\n        *,\\n        asset_class:asset_classes(*)\\n      \").or(\"symbol.ilike.%\".concat(query.toUpperCase(), \"%,name.ilike.%\").concat(query, \"%\")).eq('is_active', true).limit(10);\n        if (error) {\n            throw new Error(\"Failed to search assets: \".concat(error.message));\n        }\n        return data;\n    }\n}\nclass HoldingService {\n    /**\n   * Get all holdings for the current user\n   */ static async getHoldings(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('holdings').select(\"\\n        *,\\n        asset:assets(*,asset_class:asset_classes(*)),\\n        account:accounts(*)\\n      \").eq('user_id', user.id).gt('quantity', 0).order('created_at', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            query = query.eq('account_id', options.account_id);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch holdings: \".concat(error.message));\n        }\n        // Filter by asset class if specified\n        let holdings = data;\n        if (options === null || options === void 0 ? void 0 : options.asset_class_id) {\n            holdings = holdings.filter((h)=>{\n                var _h_asset;\n                return ((_h_asset = h.asset) === null || _h_asset === void 0 ? void 0 : _h_asset.asset_class_id) === options.asset_class_id;\n            });\n        }\n        return holdings;\n    }\n    /**\n   * Get holding for a specific asset in an account\n   */ static async getHolding(accountId, assetId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('holdings').select(\"\\n        *,\\n        asset:assets(*,asset_class:asset_classes(*)),\\n        account:accounts(*)\\n      \").eq('user_id', user.id).eq('account_id', accountId).eq('asset_id', assetId).single();\n        if (error && error.code !== 'PGRST116') {\n            throw new Error(\"Failed to fetch holding: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update or create holding after a transaction\n   */ static async updateHolding(accountId, assetId, transactionType, quantity, pricePerUnit) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const existingHolding = await this.getHolding(accountId, assetId);\n        if (transactionType === 'buy') {\n            if (existingHolding) {\n                // Update existing holding\n                const newQuantity = existingHolding.quantity + quantity;\n                const newTotalInvested = existingHolding.total_invested + quantity * pricePerUnit;\n                const newAverageCost = newTotalInvested / newQuantity;\n                const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('holdings').update({\n                    quantity: newQuantity,\n                    average_cost: newAverageCost,\n                    total_invested: newTotalInvested,\n                    last_updated: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                }).eq('id', existingHolding.id).select(\"\\n            *,\\n            asset:assets(*,asset_class:asset_classes(*)),\\n            account:accounts(*)\\n          \").single();\n                if (error) {\n                    throw new Error(\"Failed to update holding: \".concat(error.message));\n                }\n                return data;\n            } else {\n                // Create new holding\n                const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('holdings').insert({\n                    user_id: user.id,\n                    account_id: accountId,\n                    asset_id: assetId,\n                    quantity,\n                    average_cost: pricePerUnit,\n                    total_invested: quantity * pricePerUnit,\n                    last_updated: new Date().toISOString()\n                }).select(\"\\n            *,\\n            asset:assets(*,asset_class:asset_classes(*)),\\n            account:accounts(*)\\n          \").single();\n                if (error) {\n                    throw new Error(\"Failed to create holding: \".concat(error.message));\n                }\n                return data;\n            }\n        } else {\n            // Sell transaction\n            if (!existingHolding) {\n                throw new Error('Cannot sell asset that is not held');\n            }\n            if (existingHolding.quantity < quantity) {\n                throw new Error('Cannot sell more than current holding');\n            }\n            const newQuantity = existingHolding.quantity - quantity;\n            const soldValue = quantity * existingHolding.average_cost;\n            const newTotalInvested = existingHolding.total_invested - soldValue;\n            if (newQuantity === 0) {\n                // Delete holding if quantity becomes zero\n                const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('holdings').delete().eq('id', existingHolding.id);\n                if (error) {\n                    throw new Error(\"Failed to delete holding: \".concat(error.message));\n                }\n                return {\n                    ...existingHolding,\n                    quantity: 0,\n                    total_invested: 0\n                };\n            } else {\n                // Update holding\n                const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('holdings').update({\n                    quantity: newQuantity,\n                    total_invested: newTotalInvested,\n                    last_updated: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                }).eq('id', existingHolding.id).select(\"\\n            *,\\n            asset:assets(*,asset_class:asset_classes(*)),\\n            account:accounts(*)\\n          \").single();\n                if (error) {\n                    throw new Error(\"Failed to update holding: \".concat(error.message));\n                }\n                return data;\n            }\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL2xpYi9hc3NldHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFxQztBQVc5QixNQUFNQztJQUNYOztHQUVDLEdBQ0QsYUFBYUMsa0JBQThDO1FBQ3pELE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNSiwrQ0FBUUEsQ0FDbkNLLElBQUksQ0FBQyxpQkFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxhQUFhLE1BQ2hCQyxLQUFLLENBQUM7UUFFVCxJQUFJSixPQUFPO1lBQ1QsTUFBTSxJQUFJSyxNQUFNLGtDQUFnRCxPQUFkTCxNQUFNTSxPQUFPO1FBQ2pFO1FBRUEsT0FBT1A7SUFDVDtJQUVBOztHQUVDLEdBQ0QsYUFBYVEsY0FBY0MsRUFBVSxFQUE0QjtRQUMvRCxNQUFNLEVBQUVULElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUosK0NBQVFBLENBQ25DSyxJQUFJLENBQUMsaUJBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsTUFBTUssSUFDVEMsTUFBTTtRQUVULElBQUlULE9BQU87WUFDVCxNQUFNLElBQUlLLE1BQU0sZ0NBQThDLE9BQWRMLE1BQU1NLE9BQU87UUFDL0Q7UUFFQSxPQUFPUDtJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhVyxpQkFBaUJDLGNBQStCLEVBQTRCO1FBQ3ZGLE1BQU1DLGFBQTRDO1lBQ2hELEdBQUdELGNBQWM7WUFDakJFLFdBQVc7UUFDYjtRQUVBLE1BQU0sRUFBRWQsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNSiwrQ0FBUUEsQ0FDbkNLLElBQUksQ0FBQyxpQkFDTGEsTUFBTSxDQUFDRixZQUNQVixNQUFNLENBQUMsS0FDUE8sTUFBTTtRQUVULElBQUlULE9BQU87WUFDVCxNQUFNLElBQUlLLE1BQU0saUNBQStDLE9BQWRMLE1BQU1NLE9BQU87UUFDaEU7UUFFQSxPQUFPUDtJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhZ0IsaUJBQWlCUCxFQUFVLEVBQUVRLE9BQWlDLEVBQTRCO1FBQ3JHLE1BQU1DLGFBQTRDO1lBQ2hELEdBQUdELE9BQU87WUFDVkUsWUFBWSxJQUFJQyxPQUFPQyxXQUFXO1FBQ3BDO1FBRUEsTUFBTSxFQUFFckIsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNSiwrQ0FBUUEsQ0FDbkNLLElBQUksQ0FBQyxpQkFDTG9CLE1BQU0sQ0FBQ0osWUFDUGQsRUFBRSxDQUFDLE1BQU1LLElBQ1ROLE1BQU0sQ0FBQyxLQUNQTyxNQUFNO1FBRVQsSUFBSVQsT0FBTztZQUNULE1BQU0sSUFBSUssTUFBTSxpQ0FBK0MsT0FBZEwsTUFBTU0sT0FBTztRQUNoRTtRQUVBLE9BQU9QO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELGFBQWF1QixnQ0FBK0M7UUFDMUQsTUFBTSxFQUFFQyxxQkFBcUIsRUFBRSxHQUFHLE1BQU0sMk9BQXlCO1FBRWpFLEtBQUssTUFBTUMsY0FBY0Qsc0JBQXVCO1lBQzlDLElBQUk7Z0JBQ0YsTUFBTSxJQUFJLENBQUNiLGdCQUFnQixDQUFDYztZQUM5QixFQUFFLE9BQU94QixPQUFPO2dCQUNkLHlCQUF5QjtnQkFDekJ5QixRQUFRQyxHQUFHLENBQUMsZUFBK0IsT0FBaEJGLFdBQVdHLElBQUksRUFBQztZQUM3QztRQUNGO0lBQ0Y7QUFDRjtBQUVPLE1BQU1DO0lBQ1g7O0dBRUMsR0FDRCxhQUFhQyxVQUFVQyxPQUd0QixFQUFxQjtRQUNwQixJQUFJQyxRQUFRbkMsK0NBQVFBLENBQ2pCSyxJQUFJLENBQUMsVUFDTEMsTUFBTSxDQUFFLDhEQUlSRSxLQUFLLENBQUM7UUFFVCxJQUFJMEIsb0JBQUFBLDhCQUFBQSxRQUFTRSxjQUFjLEVBQUU7WUFDM0JELFFBQVFBLE1BQU01QixFQUFFLENBQUMsa0JBQWtCMkIsUUFBUUUsY0FBYztRQUMzRDtRQUVBLElBQUlGLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU2pCLFNBQVMsTUFBS29CLFdBQVc7WUFDcENGLFFBQVFBLE1BQU01QixFQUFFLENBQUMsYUFBYTJCLFFBQVFqQixTQUFTO1FBQ2pEO1FBRUEsTUFBTSxFQUFFZCxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU0rQjtRQUU5QixJQUFJL0IsT0FBTztZQUNULE1BQU0sSUFBSUssTUFBTSwyQkFBeUMsT0FBZEwsTUFBTU0sT0FBTztRQUMxRDtRQUVBLE9BQU9QO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELGFBQWFtQyxTQUFTMUIsRUFBVSxFQUFtQjtRQUNqRCxNQUFNLEVBQUVULElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUosK0NBQVFBLENBQ25DSyxJQUFJLENBQUMsVUFDTEMsTUFBTSxDQUFFLDhEQUlSQyxFQUFFLENBQUMsTUFBTUssSUFDVEMsTUFBTTtRQUVULElBQUlULE9BQU87WUFDVCxNQUFNLElBQUlLLE1BQU0sMEJBQXdDLE9BQWRMLE1BQU1NLE9BQU87UUFDekQ7UUFFQSxPQUFPUDtJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhb0MsaUJBQWlCQyxNQUFjLEVBQTBCO1FBQ3BFLE1BQU0sRUFBRXJDLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUosK0NBQVFBLENBQ25DSyxJQUFJLENBQUMsVUFDTEMsTUFBTSxDQUFFLDhEQUlSQyxFQUFFLENBQUMsVUFBVWlDLE9BQU9DLFdBQVcsSUFDL0JsQyxFQUFFLENBQUMsYUFBYSxNQUNoQk0sTUFBTTtRQUVULElBQUlULFNBQVNBLE1BQU1zQyxJQUFJLEtBQUssWUFBWTtZQUN0QyxNQUFNLElBQUlqQyxNQUFNLDBCQUF3QyxPQUFkTCxNQUFNTSxPQUFPO1FBQ3pEO1FBRUEsT0FBT1A7SUFDVDtJQUVBOztHQUVDLEdBQ0QsYUFBYXdDLFlBQVlDLFNBQXFCLEVBQW1CO1FBQy9ELE1BQU01QixhQUFxQztZQUN6QyxHQUFHNEIsU0FBUztZQUNaSixRQUFRSSxVQUFVSixNQUFNLENBQUNDLFdBQVc7WUFDcEN4QixXQUFXO1FBQ2I7UUFFQSxNQUFNLEVBQUVkLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUosK0NBQVFBLENBQ25DSyxJQUFJLENBQUMsVUFDTGEsTUFBTSxDQUFDRixZQUNQVixNQUFNLENBQUUsOERBSVJPLE1BQU07UUFFVCxJQUFJVCxPQUFPO1lBQ1QsTUFBTSxJQUFJSyxNQUFNLDJCQUF5QyxPQUFkTCxNQUFNTSxPQUFPO1FBQzFEO1FBRUEsT0FBT1A7SUFDVDtJQUVBOztHQUVDLEdBQ0QsYUFBYTBDLFlBQVlqQyxFQUFVLEVBQUVRLE9BQTRCLEVBQW1CO1FBQ2xGLE1BQU1DLGFBQXFDO1lBQ3pDLEdBQUdELE9BQU87WUFDVkUsWUFBWSxJQUFJQyxPQUFPQyxXQUFXO1FBQ3BDO1FBRUEsSUFBSUosUUFBUW9CLE1BQU0sRUFBRTtZQUNsQm5CLFdBQVdtQixNQUFNLEdBQUdwQixRQUFRb0IsTUFBTSxDQUFDQyxXQUFXO1FBQ2hEO1FBRUEsTUFBTSxFQUFFdEMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNSiwrQ0FBUUEsQ0FDbkNLLElBQUksQ0FBQyxVQUNMb0IsTUFBTSxDQUFDSixZQUNQZCxFQUFFLENBQUMsTUFBTUssSUFDVE4sTUFBTSxDQUFFLDhEQUlSTyxNQUFNO1FBRVQsSUFBSVQsT0FBTztZQUNULE1BQU0sSUFBSUssTUFBTSwyQkFBeUMsT0FBZEwsTUFBTU0sT0FBTztRQUMxRDtRQUVBLE9BQU9QO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELGFBQWEyQyxhQUFhWCxLQUFhLEVBQXFCO1FBQzFELE1BQU0sRUFBRWhDLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUosK0NBQVFBLENBQ25DSyxJQUFJLENBQUMsVUFDTEMsTUFBTSxDQUFFLDhEQUlSeUMsRUFBRSxDQUFDLGlCQUFxRFosT0FBcENBLE1BQU1NLFdBQVcsSUFBRyxrQkFBc0IsT0FBTk4sT0FBTSxNQUM5RDVCLEVBQUUsQ0FBQyxhQUFhLE1BQ2hCeUMsS0FBSyxDQUFDO1FBRVQsSUFBSTVDLE9BQU87WUFDVCxNQUFNLElBQUlLLE1BQU0sNEJBQTBDLE9BQWRMLE1BQU1NLE9BQU87UUFDM0Q7UUFFQSxPQUFPUDtJQUNUO0FBQ0Y7QUFFTyxNQUFNOEM7SUFDWDs7R0FFQyxHQUNELGFBQWFDLFlBQVloQixPQUd4QixFQUF1QjtRQUN0QixNQUFNLEVBQUUvQixNQUFNLEVBQUVnRCxJQUFJLEVBQUUsRUFBRSxHQUFHLE1BQU1uRCwrQ0FBUUEsQ0FBQ29ELElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVCxNQUFNLElBQUkxQyxNQUFNO1FBQ2xCO1FBRUEsSUFBSTBCLFFBQVFuQywrQ0FBUUEsQ0FDakJLLElBQUksQ0FBQyxZQUNMQyxNQUFNLENBQUUsNEdBS1JDLEVBQUUsQ0FBQyxXQUFXNEMsS0FBS3ZDLEVBQUUsRUFDckIwQyxFQUFFLENBQUMsWUFBWSxHQUNmOUMsS0FBSyxDQUFDLGNBQWM7WUFBRStDLFdBQVc7UUFBTTtRQUUxQyxJQUFJckIsb0JBQUFBLDhCQUFBQSxRQUFTc0IsVUFBVSxFQUFFO1lBQ3ZCckIsUUFBUUEsTUFBTTVCLEVBQUUsQ0FBQyxjQUFjMkIsUUFBUXNCLFVBQVU7UUFDbkQ7UUFFQSxNQUFNLEVBQUVyRCxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU0rQjtRQUU5QixJQUFJL0IsT0FBTztZQUNULE1BQU0sSUFBSUssTUFBTSw2QkFBMkMsT0FBZEwsTUFBTU0sT0FBTztRQUM1RDtRQUVBLHFDQUFxQztRQUNyQyxJQUFJK0MsV0FBV3REO1FBQ2YsSUFBSStCLG9CQUFBQSw4QkFBQUEsUUFBU0UsY0FBYyxFQUFFO1lBQzNCcUIsV0FBV0EsU0FBU0MsTUFBTSxDQUFDQyxDQUFBQTtvQkFBS0E7dUJBQUFBLEVBQUFBLFdBQUFBLEVBQUVDLEtBQUssY0FBUEQsK0JBQUFBLFNBQVN2QixjQUFjLE1BQUtGLFFBQVFFLGNBQWM7O1FBQ3BGO1FBRUEsT0FBT3FCO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELGFBQWFJLFdBQVdDLFNBQWlCLEVBQUVDLE9BQWUsRUFBNEI7UUFDcEYsTUFBTSxFQUFFNUQsTUFBTSxFQUFFZ0QsSUFBSSxFQUFFLEVBQUUsR0FBRyxNQUFNbkQsK0NBQVFBLENBQUNvRCxJQUFJLENBQUNDLE9BQU87UUFFdEQsSUFBSSxDQUFDRixNQUFNO1lBQ1QsTUFBTSxJQUFJMUMsTUFBTTtRQUNsQjtRQUVBLE1BQU0sRUFBRU4sSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNSiwrQ0FBUUEsQ0FDbkNLLElBQUksQ0FBQyxZQUNMQyxNQUFNLENBQUUsNEdBS1JDLEVBQUUsQ0FBQyxXQUFXNEMsS0FBS3ZDLEVBQUUsRUFDckJMLEVBQUUsQ0FBQyxjQUFjdUQsV0FDakJ2RCxFQUFFLENBQUMsWUFBWXdELFNBQ2ZsRCxNQUFNO1FBRVQsSUFBSVQsU0FBU0EsTUFBTXNDLElBQUksS0FBSyxZQUFZO1lBQ3RDLE1BQU0sSUFBSWpDLE1BQU0sNEJBQTBDLE9BQWRMLE1BQU1NLE9BQU87UUFDM0Q7UUFFQSxPQUFPUDtJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhNkQsY0FDWEYsU0FBaUIsRUFDakJDLE9BQWUsRUFDZkUsZUFBK0IsRUFDL0JDLFFBQWdCLEVBQ2hCQyxZQUFvQixFQUNEO1FBQ25CLE1BQU0sRUFBRWhFLE1BQU0sRUFBRWdELElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTW5ELCtDQUFRQSxDQUFDb0QsSUFBSSxDQUFDQyxPQUFPO1FBRXRELElBQUksQ0FBQ0YsTUFBTTtZQUNULE1BQU0sSUFBSTFDLE1BQU07UUFDbEI7UUFFQSxNQUFNMkQsa0JBQWtCLE1BQU0sSUFBSSxDQUFDUCxVQUFVLENBQUNDLFdBQVdDO1FBRXpELElBQUlFLG9CQUFvQixPQUFPO1lBQzdCLElBQUlHLGlCQUFpQjtnQkFDbkIsMEJBQTBCO2dCQUMxQixNQUFNQyxjQUFjRCxnQkFBZ0JGLFFBQVEsR0FBR0E7Z0JBQy9DLE1BQU1JLG1CQUFtQkYsZ0JBQWdCRyxjQUFjLEdBQUlMLFdBQVdDO2dCQUN0RSxNQUFNSyxpQkFBaUJGLG1CQUFtQkQ7Z0JBRTFDLE1BQU0sRUFBRWxFLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUosK0NBQVFBLENBQ25DSyxJQUFJLENBQUMsWUFDTG9CLE1BQU0sQ0FBQztvQkFDTnlDLFVBQVVHO29CQUNWSSxjQUFjRDtvQkFDZEQsZ0JBQWdCRDtvQkFDaEJJLGNBQWMsSUFBSW5ELE9BQU9DLFdBQVc7b0JBQ3BDRixZQUFZLElBQUlDLE9BQU9DLFdBQVc7Z0JBQ3BDLEdBQ0NqQixFQUFFLENBQUMsTUFBTTZELGdCQUFnQnhELEVBQUUsRUFDM0JOLE1BQU0sQ0FBRSw0SEFLUk8sTUFBTTtnQkFFVCxJQUFJVCxPQUFPO29CQUNULE1BQU0sSUFBSUssTUFBTSw2QkFBMkMsT0FBZEwsTUFBTU0sT0FBTztnQkFDNUQ7Z0JBRUEsT0FBT1A7WUFDVCxPQUFPO2dCQUNMLHFCQUFxQjtnQkFDckIsTUFBTSxFQUFFQSxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1KLCtDQUFRQSxDQUNuQ0ssSUFBSSxDQUFDLFlBQ0xhLE1BQU0sQ0FBQztvQkFDTnlELFNBQVN4QixLQUFLdkMsRUFBRTtvQkFDaEI0QyxZQUFZTTtvQkFDWmMsVUFBVWI7b0JBQ1ZHO29CQUNBTyxjQUFjTjtvQkFDZEksZ0JBQWdCTCxXQUFXQztvQkFDM0JPLGNBQWMsSUFBSW5ELE9BQU9DLFdBQVc7Z0JBQ3RDLEdBQ0NsQixNQUFNLENBQUUsNEhBS1JPLE1BQU07Z0JBRVQsSUFBSVQsT0FBTztvQkFDVCxNQUFNLElBQUlLLE1BQU0sNkJBQTJDLE9BQWRMLE1BQU1NLE9BQU87Z0JBQzVEO2dCQUVBLE9BQU9QO1lBQ1Q7UUFDRixPQUFPO1lBQ0wsbUJBQW1CO1lBQ25CLElBQUksQ0FBQ2lFLGlCQUFpQjtnQkFDcEIsTUFBTSxJQUFJM0QsTUFBTTtZQUNsQjtZQUVBLElBQUkyRCxnQkFBZ0JGLFFBQVEsR0FBR0EsVUFBVTtnQkFDdkMsTUFBTSxJQUFJekQsTUFBTTtZQUNsQjtZQUVBLE1BQU00RCxjQUFjRCxnQkFBZ0JGLFFBQVEsR0FBR0E7WUFDL0MsTUFBTVcsWUFBWVgsV0FBV0UsZ0JBQWdCSyxZQUFZO1lBQ3pELE1BQU1ILG1CQUFtQkYsZ0JBQWdCRyxjQUFjLEdBQUdNO1lBRTFELElBQUlSLGdCQUFnQixHQUFHO2dCQUNyQiwwQ0FBMEM7Z0JBQzFDLE1BQU0sRUFBRWpFLEtBQUssRUFBRSxHQUFHLE1BQU1KLCtDQUFRQSxDQUM3QkssSUFBSSxDQUFDLFlBQ0x5RSxNQUFNLEdBQ052RSxFQUFFLENBQUMsTUFBTTZELGdCQUFnQnhELEVBQUU7Z0JBRTlCLElBQUlSLE9BQU87b0JBQ1QsTUFBTSxJQUFJSyxNQUFNLDZCQUEyQyxPQUFkTCxNQUFNTSxPQUFPO2dCQUM1RDtnQkFFQSxPQUFPO29CQUFFLEdBQUcwRCxlQUFlO29CQUFFRixVQUFVO29CQUFHSyxnQkFBZ0I7Z0JBQUU7WUFDOUQsT0FBTztnQkFDTCxpQkFBaUI7Z0JBQ2pCLE1BQU0sRUFBRXBFLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUosK0NBQVFBLENBQ25DSyxJQUFJLENBQUMsWUFDTG9CLE1BQU0sQ0FBQztvQkFDTnlDLFVBQVVHO29CQUNWRSxnQkFBZ0JEO29CQUNoQkksY0FBYyxJQUFJbkQsT0FBT0MsV0FBVztvQkFDcENGLFlBQVksSUFBSUMsT0FBT0MsV0FBVztnQkFDcEMsR0FDQ2pCLEVBQUUsQ0FBQyxNQUFNNkQsZ0JBQWdCeEQsRUFBRSxFQUMzQk4sTUFBTSxDQUFFLDRIQUtSTyxNQUFNO2dCQUVULElBQUlULE9BQU87b0JBQ1QsTUFBTSxJQUFJSyxNQUFNLDZCQUEyQyxPQUFkTCxNQUFNTSxPQUFPO2dCQUM1RDtnQkFFQSxPQUFPUDtZQUNUO1FBQ0Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL3BhY2thZ2VzL3NoYXJlZC9zcmMvbGliL2Fzc2V0cy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdXBhYmFzZSB9IGZyb20gJy4vc3VwYWJhc2UnXG5pbXBvcnQgdHlwZSB7IFxuICBJQXNzZXRDbGFzc0luZm8sIFxuICBJQXNzZXQsIFxuICBJSG9sZGluZyxcbiAgSUFzc2V0Q2xhc3NGb3JtLFxuICBJQXNzZXRGb3JtLFxuICBERUZBVUxUX0FTU0VUX0NMQVNTRVNcbn0gZnJvbSAnLi4vdHlwZXMvYXNzZXRzJ1xuaW1wb3J0IHR5cGUgeyBUYWJsZXNJbnNlcnQsIFRhYmxlc1VwZGF0ZSB9IGZyb20gJy4uL2RhdGFiYXNlLnR5cGVzJ1xuXG5leHBvcnQgY2xhc3MgQXNzZXRDbGFzc1NlcnZpY2Uge1xuICAvKipcbiAgICogR2V0IGFsbCBhc3NldCBjbGFzc2VzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0QXNzZXRDbGFzc2VzKCk6IFByb21pc2U8SUFzc2V0Q2xhc3NJbmZvW10+IHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2Fzc2V0X2NsYXNzZXMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ2lzX2FjdGl2ZScsIHRydWUpXG4gICAgICAub3JkZXIoJ25hbWUnKVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBhc3NldCBjbGFzc2VzOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YSBhcyBJQXNzZXRDbGFzc0luZm9bXVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBhIHNwZWNpZmljIGFzc2V0IGNsYXNzIGJ5IElEXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0QXNzZXRDbGFzcyhpZDogc3RyaW5nKTogUHJvbWlzZTxJQXNzZXRDbGFzc0luZm8+IHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2Fzc2V0X2NsYXNzZXMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ2lkJywgaWQpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggYXNzZXQgY2xhc3M6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIHJldHVybiBkYXRhIGFzIElBc3NldENsYXNzSW5mb1xuICB9XG5cbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyBhc3NldCBjbGFzc1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGNyZWF0ZUFzc2V0Q2xhc3MoYXNzZXRDbGFzc0RhdGE6IElBc3NldENsYXNzRm9ybSk6IFByb21pc2U8SUFzc2V0Q2xhc3NJbmZvPiB7XG4gICAgY29uc3QgaW5zZXJ0RGF0YTogVGFibGVzSW5zZXJ0PCdhc3NldF9jbGFzc2VzJz4gPSB7XG4gICAgICAuLi5hc3NldENsYXNzRGF0YSxcbiAgICAgIGlzX2FjdGl2ZTogdHJ1ZVxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnYXNzZXRfY2xhc3NlcycpXG4gICAgICAuaW5zZXJ0KGluc2VydERhdGEpXG4gICAgICAuc2VsZWN0KCcqJylcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBjcmVhdGUgYXNzZXQgY2xhc3M6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIHJldHVybiBkYXRhIGFzIElBc3NldENsYXNzSW5mb1xuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZSBhbiBleGlzdGluZyBhc3NldCBjbGFzc1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIHVwZGF0ZUFzc2V0Q2xhc3MoaWQ6IHN0cmluZywgdXBkYXRlczogUGFydGlhbDxJQXNzZXRDbGFzc0Zvcm0+KTogUHJvbWlzZTxJQXNzZXRDbGFzc0luZm8+IHtcbiAgICBjb25zdCB1cGRhdGVEYXRhOiBUYWJsZXNVcGRhdGU8J2Fzc2V0X2NsYXNzZXMnPiA9IHtcbiAgICAgIC4uLnVwZGF0ZXMsXG4gICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2Fzc2V0X2NsYXNzZXMnKVxuICAgICAgLnVwZGF0ZSh1cGRhdGVEYXRhKVxuICAgICAgLmVxKCdpZCcsIGlkKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gdXBkYXRlIGFzc2V0IGNsYXNzOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YSBhcyBJQXNzZXRDbGFzc0luZm9cbiAgfVxuXG4gIC8qKlxuICAgKiBJbml0aWFsaXplIGRlZmF1bHQgYXNzZXQgY2xhc3Nlc1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGluaXRpYWxpemVEZWZhdWx0QXNzZXRDbGFzc2VzKCk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnN0IHsgREVGQVVMVF9BU1NFVF9DTEFTU0VTIH0gPSBhd2FpdCBpbXBvcnQoJy4uL3R5cGVzL2Fzc2V0cycpXG4gICAgXG4gICAgZm9yIChjb25zdCBhc3NldENsYXNzIG9mIERFRkFVTFRfQVNTRVRfQ0xBU1NFUykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgdGhpcy5jcmVhdGVBc3NldENsYXNzKGFzc2V0Q2xhc3MpXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAvLyBTa2lwIGlmIGFscmVhZHkgZXhpc3RzXG4gICAgICAgIGNvbnNvbGUubG9nKGBBc3NldCBjbGFzcyAke2Fzc2V0Q2xhc3MubmFtZX0gbWF5IGFscmVhZHkgZXhpc3RgKVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG5leHBvcnQgY2xhc3MgQXNzZXRTZXJ2aWNlIHtcbiAgLyoqXG4gICAqIEdldCBhbGwgYXNzZXRzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0QXNzZXRzKG9wdGlvbnM/OiB7XG4gICAgYXNzZXRfY2xhc3NfaWQ/OiBzdHJpbmdcbiAgICBpc19hY3RpdmU/OiBib29sZWFuXG4gIH0pOiBQcm9taXNlPElBc3NldFtdPiB7XG4gICAgbGV0IHF1ZXJ5ID0gc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdhc3NldHMnKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIGFzc2V0X2NsYXNzOmFzc2V0X2NsYXNzZXMoKilcbiAgICAgIGApXG4gICAgICAub3JkZXIoJ3N5bWJvbCcpXG5cbiAgICBpZiAob3B0aW9ucz8uYXNzZXRfY2xhc3NfaWQpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuZXEoJ2Fzc2V0X2NsYXNzX2lkJywgb3B0aW9ucy5hc3NldF9jbGFzc19pZClcbiAgICB9XG5cbiAgICBpZiAob3B0aW9ucz8uaXNfYWN0aXZlICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuZXEoJ2lzX2FjdGl2ZScsIG9wdGlvbnMuaXNfYWN0aXZlKVxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHF1ZXJ5XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIGFzc2V0czogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgSUFzc2V0W11cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgYSBzcGVjaWZpYyBhc3NldCBieSBJRFxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldEFzc2V0KGlkOiBzdHJpbmcpOiBQcm9taXNlPElBc3NldD4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnYXNzZXRzJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBhc3NldF9jbGFzczphc3NldF9jbGFzc2VzKCopXG4gICAgICBgKVxuICAgICAgLmVxKCdpZCcsIGlkKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIGFzc2V0OiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YSBhcyBJQXNzZXRcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgYXNzZXQgYnkgc3ltYm9sXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0QXNzZXRCeVN5bWJvbChzeW1ib2w6IHN0cmluZyk6IFByb21pc2U8SUFzc2V0IHwgbnVsbD4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnYXNzZXRzJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBhc3NldF9jbGFzczphc3NldF9jbGFzc2VzKCopXG4gICAgICBgKVxuICAgICAgLmVxKCdzeW1ib2wnLCBzeW1ib2wudG9VcHBlckNhc2UoKSlcbiAgICAgIC5lcSgnaXNfYWN0aXZlJywgdHJ1ZSlcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKGVycm9yICYmIGVycm9yLmNvZGUgIT09ICdQR1JTVDExNicpIHsgLy8gTm90IGZvdW5kIGVycm9yXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBhc3NldDogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgSUFzc2V0IHwgbnVsbFxuICB9XG5cbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyBhc3NldFxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGNyZWF0ZUFzc2V0KGFzc2V0RGF0YTogSUFzc2V0Rm9ybSk6IFByb21pc2U8SUFzc2V0PiB7XG4gICAgY29uc3QgaW5zZXJ0RGF0YTogVGFibGVzSW5zZXJ0PCdhc3NldHMnPiA9IHtcbiAgICAgIC4uLmFzc2V0RGF0YSxcbiAgICAgIHN5bWJvbDogYXNzZXREYXRhLnN5bWJvbC50b1VwcGVyQ2FzZSgpLFxuICAgICAgaXNfYWN0aXZlOiB0cnVlXG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdhc3NldHMnKVxuICAgICAgLmluc2VydChpbnNlcnREYXRhKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIGFzc2V0X2NsYXNzOmFzc2V0X2NsYXNzZXMoKilcbiAgICAgIGApXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gY3JlYXRlIGFzc2V0OiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YSBhcyBJQXNzZXRcbiAgfVxuXG4gIC8qKlxuICAgKiBVcGRhdGUgYW4gZXhpc3RpbmcgYXNzZXRcbiAgICovXG4gIHN0YXRpYyBhc3luYyB1cGRhdGVBc3NldChpZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPElBc3NldEZvcm0+KTogUHJvbWlzZTxJQXNzZXQ+IHtcbiAgICBjb25zdCB1cGRhdGVEYXRhOiBUYWJsZXNVcGRhdGU8J2Fzc2V0cyc+ID0ge1xuICAgICAgLi4udXBkYXRlcyxcbiAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH1cblxuICAgIGlmICh1cGRhdGVzLnN5bWJvbCkge1xuICAgICAgdXBkYXRlRGF0YS5zeW1ib2wgPSB1cGRhdGVzLnN5bWJvbC50b1VwcGVyQ2FzZSgpXG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdhc3NldHMnKVxuICAgICAgLnVwZGF0ZSh1cGRhdGVEYXRhKVxuICAgICAgLmVxKCdpZCcsIGlkKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIGFzc2V0X2NsYXNzOmFzc2V0X2NsYXNzZXMoKilcbiAgICAgIGApXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gdXBkYXRlIGFzc2V0OiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YSBhcyBJQXNzZXRcbiAgfVxuXG4gIC8qKlxuICAgKiBTZWFyY2ggYXNzZXRzIGJ5IHN5bWJvbCBvciBuYW1lXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgc2VhcmNoQXNzZXRzKHF1ZXJ5OiBzdHJpbmcpOiBQcm9taXNlPElBc3NldFtdPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdhc3NldHMnKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIGFzc2V0X2NsYXNzOmFzc2V0X2NsYXNzZXMoKilcbiAgICAgIGApXG4gICAgICAub3IoYHN5bWJvbC5pbGlrZS4lJHtxdWVyeS50b1VwcGVyQ2FzZSgpfSUsbmFtZS5pbGlrZS4lJHtxdWVyeX0lYClcbiAgICAgIC5lcSgnaXNfYWN0aXZlJywgdHJ1ZSlcbiAgICAgIC5saW1pdCgxMClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gc2VhcmNoIGFzc2V0czogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgSUFzc2V0W11cbiAgfVxufVxuXG5leHBvcnQgY2xhc3MgSG9sZGluZ1NlcnZpY2Uge1xuICAvKipcbiAgICogR2V0IGFsbCBob2xkaW5ncyBmb3IgdGhlIGN1cnJlbnQgdXNlclxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldEhvbGRpbmdzKG9wdGlvbnM/OiB7XG4gICAgYWNjb3VudF9pZD86IHN0cmluZ1xuICAgIGFzc2V0X2NsYXNzX2lkPzogc3RyaW5nXG4gIH0pOiBQcm9taXNlPElIb2xkaW5nW10+IHtcbiAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9IH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKVxuICAgIFxuICAgIGlmICghdXNlcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJylcbiAgICB9XG5cbiAgICBsZXQgcXVlcnkgPSBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2hvbGRpbmdzJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBhc3NldDphc3NldHMoKixhc3NldF9jbGFzczphc3NldF9jbGFzc2VzKCopKSxcbiAgICAgICAgYWNjb3VudDphY2NvdW50cygqKVxuICAgICAgYClcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuZ3QoJ3F1YW50aXR5JywgMClcbiAgICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuXG4gICAgaWYgKG9wdGlvbnM/LmFjY291bnRfaWQpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuZXEoJ2FjY291bnRfaWQnLCBvcHRpb25zLmFjY291bnRfaWQpXG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgcXVlcnlcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggaG9sZGluZ3M6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIC8vIEZpbHRlciBieSBhc3NldCBjbGFzcyBpZiBzcGVjaWZpZWRcbiAgICBsZXQgaG9sZGluZ3MgPSBkYXRhIGFzIElIb2xkaW5nW11cbiAgICBpZiAob3B0aW9ucz8uYXNzZXRfY2xhc3NfaWQpIHtcbiAgICAgIGhvbGRpbmdzID0gaG9sZGluZ3MuZmlsdGVyKGggPT4gaC5hc3NldD8uYXNzZXRfY2xhc3NfaWQgPT09IG9wdGlvbnMuYXNzZXRfY2xhc3NfaWQpXG4gICAgfVxuXG4gICAgcmV0dXJuIGhvbGRpbmdzXG4gIH1cblxuICAvKipcbiAgICogR2V0IGhvbGRpbmcgZm9yIGEgc3BlY2lmaWMgYXNzZXQgaW4gYW4gYWNjb3VudFxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldEhvbGRpbmcoYWNjb3VudElkOiBzdHJpbmcsIGFzc2V0SWQ6IHN0cmluZyk6IFByb21pc2U8SUhvbGRpbmcgfCBudWxsPiB7XG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKClcbiAgICBcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgYXV0aGVudGljYXRlZCcpXG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdob2xkaW5ncycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgYXNzZXQ6YXNzZXRzKCosYXNzZXRfY2xhc3M6YXNzZXRfY2xhc3NlcygqKSksXG4gICAgICAgIGFjY291bnQ6YWNjb3VudHMoKilcbiAgICAgIGApXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgLmVxKCdhY2NvdW50X2lkJywgYWNjb3VudElkKVxuICAgICAgLmVxKCdhc3NldF9pZCcsIGFzc2V0SWQpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvciAmJiBlcnJvci5jb2RlICE9PSAnUEdSU1QxMTYnKSB7IC8vIE5vdCBmb3VuZCBlcnJvclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggaG9sZGluZzogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgSUhvbGRpbmcgfCBudWxsXG4gIH1cblxuICAvKipcbiAgICogVXBkYXRlIG9yIGNyZWF0ZSBob2xkaW5nIGFmdGVyIGEgdHJhbnNhY3Rpb25cbiAgICovXG4gIHN0YXRpYyBhc3luYyB1cGRhdGVIb2xkaW5nKFxuICAgIGFjY291bnRJZDogc3RyaW5nLFxuICAgIGFzc2V0SWQ6IHN0cmluZyxcbiAgICB0cmFuc2FjdGlvblR5cGU6ICdidXknIHwgJ3NlbGwnLFxuICAgIHF1YW50aXR5OiBudW1iZXIsXG4gICAgcHJpY2VQZXJVbml0OiBudW1iZXJcbiAgKTogUHJvbWlzZTxJSG9sZGluZz4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIGNvbnN0IGV4aXN0aW5nSG9sZGluZyA9IGF3YWl0IHRoaXMuZ2V0SG9sZGluZyhhY2NvdW50SWQsIGFzc2V0SWQpXG5cbiAgICBpZiAodHJhbnNhY3Rpb25UeXBlID09PSAnYnV5Jykge1xuICAgICAgaWYgKGV4aXN0aW5nSG9sZGluZykge1xuICAgICAgICAvLyBVcGRhdGUgZXhpc3RpbmcgaG9sZGluZ1xuICAgICAgICBjb25zdCBuZXdRdWFudGl0eSA9IGV4aXN0aW5nSG9sZGluZy5xdWFudGl0eSArIHF1YW50aXR5XG4gICAgICAgIGNvbnN0IG5ld1RvdGFsSW52ZXN0ZWQgPSBleGlzdGluZ0hvbGRpbmcudG90YWxfaW52ZXN0ZWQgKyAocXVhbnRpdHkgKiBwcmljZVBlclVuaXQpXG4gICAgICAgIGNvbnN0IG5ld0F2ZXJhZ2VDb3N0ID0gbmV3VG90YWxJbnZlc3RlZCAvIG5ld1F1YW50aXR5XG5cbiAgICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgnaG9sZGluZ3MnKVxuICAgICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgICAgcXVhbnRpdHk6IG5ld1F1YW50aXR5LFxuICAgICAgICAgICAgYXZlcmFnZV9jb3N0OiBuZXdBdmVyYWdlQ29zdCxcbiAgICAgICAgICAgIHRvdGFsX2ludmVzdGVkOiBuZXdUb3RhbEludmVzdGVkLFxuICAgICAgICAgICAgbGFzdF91cGRhdGVkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICB9KVxuICAgICAgICAgIC5lcSgnaWQnLCBleGlzdGluZ0hvbGRpbmcuaWQpXG4gICAgICAgICAgLnNlbGVjdChgXG4gICAgICAgICAgICAqLFxuICAgICAgICAgICAgYXNzZXQ6YXNzZXRzKCosYXNzZXRfY2xhc3M6YXNzZXRfY2xhc3NlcygqKSksXG4gICAgICAgICAgICBhY2NvdW50OmFjY291bnRzKCopXG4gICAgICAgICAgYClcbiAgICAgICAgICAuc2luZ2xlKClcblxuICAgICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byB1cGRhdGUgaG9sZGluZzogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gZGF0YSBhcyBJSG9sZGluZ1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gQ3JlYXRlIG5ldyBob2xkaW5nXG4gICAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oJ2hvbGRpbmdzJylcbiAgICAgICAgICAuaW5zZXJ0KHtcbiAgICAgICAgICAgIHVzZXJfaWQ6IHVzZXIuaWQsXG4gICAgICAgICAgICBhY2NvdW50X2lkOiBhY2NvdW50SWQsXG4gICAgICAgICAgICBhc3NldF9pZDogYXNzZXRJZCxcbiAgICAgICAgICAgIHF1YW50aXR5LFxuICAgICAgICAgICAgYXZlcmFnZV9jb3N0OiBwcmljZVBlclVuaXQsXG4gICAgICAgICAgICB0b3RhbF9pbnZlc3RlZDogcXVhbnRpdHkgKiBwcmljZVBlclVuaXQsXG4gICAgICAgICAgICBsYXN0X3VwZGF0ZWQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgIH0pXG4gICAgICAgICAgLnNlbGVjdChgXG4gICAgICAgICAgICAqLFxuICAgICAgICAgICAgYXNzZXQ6YXNzZXRzKCosYXNzZXRfY2xhc3M6YXNzZXRfY2xhc3NlcygqKSksXG4gICAgICAgICAgICBhY2NvdW50OmFjY291bnRzKCopXG4gICAgICAgICAgYClcbiAgICAgICAgICAuc2luZ2xlKClcblxuICAgICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBjcmVhdGUgaG9sZGluZzogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gZGF0YSBhcyBJSG9sZGluZ1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBTZWxsIHRyYW5zYWN0aW9uXG4gICAgICBpZiAoIWV4aXN0aW5nSG9sZGluZykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0Nhbm5vdCBzZWxsIGFzc2V0IHRoYXQgaXMgbm90IGhlbGQnKVxuICAgICAgfVxuXG4gICAgICBpZiAoZXhpc3RpbmdIb2xkaW5nLnF1YW50aXR5IDwgcXVhbnRpdHkpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdDYW5ub3Qgc2VsbCBtb3JlIHRoYW4gY3VycmVudCBob2xkaW5nJylcbiAgICAgIH1cblxuICAgICAgY29uc3QgbmV3UXVhbnRpdHkgPSBleGlzdGluZ0hvbGRpbmcucXVhbnRpdHkgLSBxdWFudGl0eVxuICAgICAgY29uc3Qgc29sZFZhbHVlID0gcXVhbnRpdHkgKiBleGlzdGluZ0hvbGRpbmcuYXZlcmFnZV9jb3N0XG4gICAgICBjb25zdCBuZXdUb3RhbEludmVzdGVkID0gZXhpc3RpbmdIb2xkaW5nLnRvdGFsX2ludmVzdGVkIC0gc29sZFZhbHVlXG5cbiAgICAgIGlmIChuZXdRdWFudGl0eSA9PT0gMCkge1xuICAgICAgICAvLyBEZWxldGUgaG9sZGluZyBpZiBxdWFudGl0eSBiZWNvbWVzIHplcm9cbiAgICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgnaG9sZGluZ3MnKVxuICAgICAgICAgIC5kZWxldGUoKVxuICAgICAgICAgIC5lcSgnaWQnLCBleGlzdGluZ0hvbGRpbmcuaWQpXG5cbiAgICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZGVsZXRlIGhvbGRpbmc6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHsgLi4uZXhpc3RpbmdIb2xkaW5nLCBxdWFudGl0eTogMCwgdG90YWxfaW52ZXN0ZWQ6IDAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gVXBkYXRlIGhvbGRpbmdcbiAgICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgnaG9sZGluZ3MnKVxuICAgICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgICAgcXVhbnRpdHk6IG5ld1F1YW50aXR5LFxuICAgICAgICAgICAgdG90YWxfaW52ZXN0ZWQ6IG5ld1RvdGFsSW52ZXN0ZWQsXG4gICAgICAgICAgICBsYXN0X3VwZGF0ZWQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgIH0pXG4gICAgICAgICAgLmVxKCdpZCcsIGV4aXN0aW5nSG9sZGluZy5pZClcbiAgICAgICAgICAuc2VsZWN0KGBcbiAgICAgICAgICAgICosXG4gICAgICAgICAgICBhc3NldDphc3NldHMoKixhc3NldF9jbGFzczphc3NldF9jbGFzc2VzKCopKSxcbiAgICAgICAgICAgIGFjY291bnQ6YWNjb3VudHMoKilcbiAgICAgICAgICBgKVxuICAgICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICAgIGlmIChlcnJvcikge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHVwZGF0ZSBob2xkaW5nOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBkYXRhIGFzIElIb2xkaW5nXG4gICAgICB9XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsic3VwYWJhc2UiLCJBc3NldENsYXNzU2VydmljZSIsImdldEFzc2V0Q2xhc3NlcyIsImRhdGEiLCJlcnJvciIsImZyb20iLCJzZWxlY3QiLCJlcSIsIm9yZGVyIiwiRXJyb3IiLCJtZXNzYWdlIiwiZ2V0QXNzZXRDbGFzcyIsImlkIiwic2luZ2xlIiwiY3JlYXRlQXNzZXRDbGFzcyIsImFzc2V0Q2xhc3NEYXRhIiwiaW5zZXJ0RGF0YSIsImlzX2FjdGl2ZSIsImluc2VydCIsInVwZGF0ZUFzc2V0Q2xhc3MiLCJ1cGRhdGVzIiwidXBkYXRlRGF0YSIsInVwZGF0ZWRfYXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJ1cGRhdGUiLCJpbml0aWFsaXplRGVmYXVsdEFzc2V0Q2xhc3NlcyIsIkRFRkFVTFRfQVNTRVRfQ0xBU1NFUyIsImFzc2V0Q2xhc3MiLCJjb25zb2xlIiwibG9nIiwibmFtZSIsIkFzc2V0U2VydmljZSIsImdldEFzc2V0cyIsIm9wdGlvbnMiLCJxdWVyeSIsImFzc2V0X2NsYXNzX2lkIiwidW5kZWZpbmVkIiwiZ2V0QXNzZXQiLCJnZXRBc3NldEJ5U3ltYm9sIiwic3ltYm9sIiwidG9VcHBlckNhc2UiLCJjb2RlIiwiY3JlYXRlQXNzZXQiLCJhc3NldERhdGEiLCJ1cGRhdGVBc3NldCIsInNlYXJjaEFzc2V0cyIsIm9yIiwibGltaXQiLCJIb2xkaW5nU2VydmljZSIsImdldEhvbGRpbmdzIiwidXNlciIsImF1dGgiLCJnZXRVc2VyIiwiZ3QiLCJhc2NlbmRpbmciLCJhY2NvdW50X2lkIiwiaG9sZGluZ3MiLCJmaWx0ZXIiLCJoIiwiYXNzZXQiLCJnZXRIb2xkaW5nIiwiYWNjb3VudElkIiwiYXNzZXRJZCIsInVwZGF0ZUhvbGRpbmciLCJ0cmFuc2FjdGlvblR5cGUiLCJxdWFudGl0eSIsInByaWNlUGVyVW5pdCIsImV4aXN0aW5nSG9sZGluZyIsIm5ld1F1YW50aXR5IiwibmV3VG90YWxJbnZlc3RlZCIsInRvdGFsX2ludmVzdGVkIiwibmV3QXZlcmFnZUNvc3QiLCJhdmVyYWdlX2Nvc3QiLCJsYXN0X3VwZGF0ZWQiLCJ1c2VyX2lkIiwiYXNzZXRfaWQiLCJzb2xkVmFsdWUiLCJkZWxldGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/assets.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/csv-import.ts":
/*!***************************************************!*\
  !*** ../../packages/shared/src/lib/csv-import.ts ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSVImportService: () => (/* binding */ CSVImportService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _investments__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./investments */ \"(app-pages-browser)/../../packages/shared/src/lib/investments.ts\");\n\n\nclass CSVImportService {\n    /**\n   * Parse CSV content into rows\n   */ static parseCSV(csvContent) {\n        const lines = csvContent.trim().split('\\n');\n        if (lines.length < 2) {\n            throw new Error('CSV must have at least a header row and one data row');\n        }\n        const headers = this.parseCSVLine(lines[0]);\n        const rows = [];\n        for(let i = 1; i < lines.length; i++){\n            const values = this.parseCSVLine(lines[i]);\n            if (values.length === 0) continue; // Skip empty lines\n            const row = {};\n            headers.forEach((header, index)=>{\n                row[header.trim()] = (values[index] || '').trim();\n            });\n            rows.push(row);\n        }\n        return rows;\n    }\n    /**\n   * Parse a single CSV line, handling quoted values\n   */ static parseCSVLine(line) {\n        const result = [];\n        let current = '';\n        let inQuotes = false;\n        let i = 0;\n        while(i < line.length){\n            const char = line[i];\n            const nextChar = line[i + 1];\n            if (char === '\"') {\n                if (inQuotes && nextChar === '\"') {\n                    // Escaped quote\n                    current += '\"';\n                    i += 2;\n                } else {\n                    // Toggle quote state\n                    inQuotes = !inQuotes;\n                    i++;\n                }\n            } else if (char === ',' && !inQuotes) {\n                // End of field\n                result.push(current);\n                current = '';\n                i++;\n            } else {\n                current += char;\n                i++;\n            }\n        }\n        // Add the last field\n        result.push(current);\n        return result;\n    }\n    /**\n   * Get available column headers from CSV\n   */ static getCSVHeaders(csvContent) {\n        const lines = csvContent.trim().split('\\n');\n        if (lines.length === 0) {\n            throw new Error('CSV is empty');\n        }\n        return this.parseCSVLine(lines[0]).map((header)=>header.trim());\n    }\n    /**\n   * Auto-detect column mapping based on common header names\n   */ static autoDetectMapping(headers) {\n        const mapping = {};\n        const lowerHeaders = headers.map((h)=>h.toLowerCase());\n        // Date mapping\n        const datePatterns = [\n            'date',\n            'transaction_date',\n            'trade_date',\n            'settlement_date',\n            'timestamp'\n        ];\n        for (const pattern of datePatterns){\n            const index = lowerHeaders.findIndex((h)=>h.includes(pattern));\n            if (index !== -1) {\n                mapping.date = headers[index];\n                break;\n            }\n        }\n        // Symbol mapping\n        const symbolPatterns = [\n            'symbol',\n            'ticker',\n            'stock',\n            'security',\n            'instrument'\n        ];\n        for (const pattern of symbolPatterns){\n            const index = lowerHeaders.findIndex((h)=>h.includes(pattern));\n            if (index !== -1) {\n                mapping.symbol = headers[index];\n                break;\n            }\n        }\n        // Quantity mapping\n        const quantityPatterns = [\n            'quantity',\n            'shares',\n            'units',\n            'amount',\n            'qty'\n        ];\n        for (const pattern of quantityPatterns){\n            const index = lowerHeaders.findIndex((h)=>h.includes(pattern) && !h.includes('price'));\n            if (index !== -1) {\n                mapping.quantity = headers[index];\n                break;\n            }\n        }\n        // Price mapping\n        const pricePatterns = [\n            'price',\n            'unit_price',\n            'share_price',\n            'cost',\n            'value'\n        ];\n        for (const pattern of pricePatterns){\n            const index = lowerHeaders.findIndex((h)=>h.includes(pattern));\n            if (index !== -1) {\n                mapping.price = headers[index];\n                break;\n            }\n        }\n        // Type mapping\n        const typePatterns = [\n            'type',\n            'action',\n            'transaction_type',\n            'side',\n            'buy_sell'\n        ];\n        for (const pattern of typePatterns){\n            const index = lowerHeaders.findIndex((h)=>h.includes(pattern));\n            if (index !== -1) {\n                mapping.type = headers[index];\n                break;\n            }\n        }\n        // Description mapping\n        const descPatterns = [\n            'description',\n            'memo',\n            'note',\n            'comment',\n            'details'\n        ];\n        for (const pattern of descPatterns){\n            const index = lowerHeaders.findIndex((h)=>h.includes(pattern));\n            if (index !== -1) {\n                mapping.description = headers[index];\n                break;\n            }\n        }\n        // Fees mapping\n        const feePatterns = [\n            'fee',\n            'fees',\n            'commission',\n            'cost',\n            'charge'\n        ];\n        for (const pattern of feePatterns){\n            const index = lowerHeaders.findIndex((h)=>h.includes(pattern) && !h.includes('price'));\n            if (index !== -1) {\n                mapping.fees = headers[index];\n                break;\n            }\n        }\n        return mapping;\n    }\n    /**\n   * Parse CSV rows into investment transactions\n   */ static parseInvestmentTransactions(rows, mapping) {\n        const transactions = [];\n        for (const row of rows){\n            try {\n                const transaction = this.parseInvestmentTransaction(row, mapping);\n                transactions.push(transaction);\n            } catch (error) {\n                console.warn('Failed to parse row:', row, error);\n            // Continue with other rows\n            }\n        }\n        return transactions;\n    }\n    /**\n   * Parse a single row into an investment transaction\n   */ static parseInvestmentTransaction(row, mapping) {\n        var _row_mapping_symbol, _row_mapping_type;\n        // Parse date\n        const dateStr = row[mapping.date];\n        if (!dateStr) {\n            throw new Error('Date is required');\n        }\n        const date = this.parseDate(dateStr);\n        // Parse symbol\n        const symbol = (_row_mapping_symbol = row[mapping.symbol]) === null || _row_mapping_symbol === void 0 ? void 0 : _row_mapping_symbol.toUpperCase();\n        if (!symbol) {\n            throw new Error('Symbol is required');\n        }\n        // Parse quantity\n        const quantityStr = row[mapping.quantity];\n        if (!quantityStr) {\n            throw new Error('Quantity is required');\n        }\n        const quantity = Math.abs(parseFloat(quantityStr.replace(/[^\\d.-]/g, '')));\n        if (isNaN(quantity) || quantity <= 0) {\n            throw new Error('Invalid quantity');\n        }\n        // Parse price\n        const priceStr = row[mapping.price];\n        if (!priceStr) {\n            throw new Error('Price is required');\n        }\n        const price = parseFloat(priceStr.replace(/[^\\d.-]/g, ''));\n        if (isNaN(price) || price <= 0) {\n            throw new Error('Invalid price');\n        }\n        // Parse type\n        const typeStr = (_row_mapping_type = row[mapping.type]) === null || _row_mapping_type === void 0 ? void 0 : _row_mapping_type.toLowerCase();\n        if (!typeStr) {\n            throw new Error('Transaction type is required');\n        }\n        let type;\n        if (typeStr.includes('buy') || typeStr.includes('purchase') || typeStr.includes('acquire')) {\n            type = 'buy';\n        } else if (typeStr.includes('sell') || typeStr.includes('sale') || typeStr.includes('dispose')) {\n            type = 'sell';\n        } else {\n            throw new Error(\"Unknown transaction type: \".concat(typeStr));\n        }\n        // Parse optional fields\n        const description = mapping.description ? row[mapping.description] : undefined;\n        const feesStr = mapping.fees ? row[mapping.fees] : undefined;\n        const fees = feesStr ? parseFloat(feesStr.replace(/[^\\d.-]/g, '')) : 0;\n        return {\n            date,\n            symbol,\n            quantity,\n            price,\n            type,\n            description,\n            fees: isNaN(fees) ? 0 : Math.abs(fees)\n        };\n    }\n    /**\n   * Parse date string into ISO format\n   */ static parseDate(dateStr) {\n        // Try different date formats\n        const formats = [\n            /^\\d{4}-\\d{2}-\\d{2}$/,\n            /^\\d{2}\\/\\d{2}\\/\\d{4}$/,\n            /^\\d{2}-\\d{2}-\\d{4}$/,\n            /^\\d{4}\\/\\d{2}\\/\\d{2}$/\n        ];\n        let date;\n        if (formats[0].test(dateStr)) {\n            // YYYY-MM-DD\n            date = new Date(dateStr);\n        } else if (formats[1].test(dateStr)) {\n            // MM/DD/YYYY\n            const [month, day, year] = dateStr.split('/');\n            date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));\n        } else if (formats[2].test(dateStr)) {\n            // MM-DD-YYYY\n            const [month, day, year] = dateStr.split('-');\n            date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));\n        } else if (formats[3].test(dateStr)) {\n            // YYYY/MM/DD\n            const [year, month, day] = dateStr.split('/');\n            date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));\n        } else {\n            // Try to parse as-is\n            date = new Date(dateStr);\n        }\n        if (isNaN(date.getTime())) {\n            throw new Error(\"Invalid date format: \".concat(dateStr));\n        }\n        return date.toISOString().split('T')[0];\n    }\n    /**\n   * Import investment transactions from parsed data\n   */ static async importInvestmentTransactions(transactions, accountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const result = {\n            success: 0,\n            errors: [],\n            transactions: []\n        };\n        for(let i = 0; i < transactions.length; i++){\n            const transaction = transactions[i];\n            try {\n                const investmentTransaction = await _investments__WEBPACK_IMPORTED_MODULE_1__.InvestmentService.createInvestmentTransaction({\n                    amount: transaction.quantity * transaction.price,\n                    description: transaction.description || \"\".concat(transaction.type.toUpperCase(), \" \").concat(transaction.quantity, \" shares of \").concat(transaction.symbol),\n                    account_id: accountId,\n                    investment_symbol: transaction.symbol,\n                    investment_quantity: transaction.quantity,\n                    investment_price: transaction.price,\n                    transaction_type: transaction.type === 'buy' ? 'investment_buy' : 'investment_sell',\n                    transaction_date: transaction.date,\n                    fees: transaction.fees || 0\n                });\n                result.transactions.push(investmentTransaction);\n                result.success++;\n            } catch (error) {\n                result.errors.push({\n                    row: i + 1,\n                    error: error instanceof Error ? error.message : 'Unknown error',\n                    data: transaction\n                });\n            }\n        }\n        return result;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/csv-import.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/investments.ts":
/*!****************************************************!*\
  !*** ../../packages/shared/src/lib/investments.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvestmentService: () => (/* binding */ InvestmentService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _tax_calculator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tax-calculator */ \"(app-pages-browser)/../../packages/shared/src/lib/tax-calculator.ts\");\n/* harmony import */ var _assets__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./assets */ \"(app-pages-browser)/../../packages/shared/src/lib/assets.ts\");\n/* harmony import */ var _profit_loss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./profit-loss */ \"(app-pages-browser)/../../packages/shared/src/lib/profit-loss.ts\");\n\n\n\n\nclass InvestmentService {\n    /**\n   * Create an investment transaction (buy/sell)\n   * This also creates a transfer from a funding account for buy transactions\n   */ static async createInvestmentTransaction(investmentData, fundingAccountId// Required for buy transactions\n    ) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Validate investment account exists and is an investment account\n        const { data: investmentAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', investmentData.account_id).eq('user_id', user.id).single();\n        if (!investmentAccount || investmentAccount.account_type !== 'investment') {\n            throw new Error('Invalid investment account specified');\n        }\n        // For buy transactions, validate funding account and check balance\n        if (investmentData.transaction_type === 'investment_buy') {\n            if (!fundingAccountId) {\n                throw new Error('Funding account required for investment purchases');\n            }\n            const { data: fundingAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', fundingAccountId).eq('user_id', user.id).single();\n            if (!fundingAccount) {\n                throw new Error('Invalid funding account specified');\n            }\n            const totalCost = investmentData.amount + (investmentData.fees || 0);\n            if (fundingAccount.current_balance < totalCost) {\n                throw new Error('Insufficient balance in funding account');\n            }\n        }\n        // Get investment category\n        const categoryName = investmentData.transaction_type === 'investment_buy' ? 'Investment Purchase' : 'Investment Sale';\n        const { data: category } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', categoryName).eq('is_system', true).single();\n        if (!category) {\n            throw new Error(\"\".concat(categoryName, \" category not found\"));\n        }\n        // Create the investment transaction\n        const transactionData = {\n            amount: investmentData.amount,\n            description: investmentData.description || \"\".concat(investmentData.transaction_type === 'investment_buy' ? 'Buy' : 'Sell', \" \").concat(investmentData.investment_quantity, \" shares of \").concat(investmentData.investment_symbol),\n            category_id: category.id,\n            account_id: investmentData.account_id,\n            transaction_type: investmentData.transaction_type,\n            transaction_date: investmentData.transaction_date,\n            transaction_status: 'completed',\n            fees: investmentData.fees || 0,\n            investment_symbol: investmentData.investment_symbol,\n            investment_quantity: investmentData.investment_quantity,\n            investment_price: investmentData.investment_price,\n            user_id: user.id\n        };\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \").single();\n        if (transactionError) {\n            throw new Error(\"Failed to create investment transaction: \".concat(transactionError.message));\n        }\n        // For buy transactions, create a transfer from funding account to investment account\n        if (investmentData.transaction_type === 'investment_buy' && fundingAccountId) {\n            const { TransferService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_packages_shared_src_lib_transfers_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\"));\n            try {\n                await TransferService.createTransfer({\n                    amount: investmentData.amount + (investmentData.fees || 0),\n                    description: \"Investment purchase: \".concat(investmentData.investment_symbol),\n                    from_account_id: fundingAccountId,\n                    to_account_id: investmentData.account_id,\n                    transaction_date: investmentData.transaction_date,\n                    fees: 0\n                });\n            } catch (error) {\n                // If transfer fails, rollback the investment transaction\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', transaction.id);\n                throw new Error(\"Failed to create funding transfer: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n            }\n        }\n        return transaction;\n    }\n    /**\n   * Create investment transaction with enhanced asset tracking and tax calculations\n   */ static async createInvestmentTransactionWithAssets(investmentData, fundingAccountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Get asset information\n        const asset = await _assets__WEBPACK_IMPORTED_MODULE_2__.AssetService.getAsset(investmentData.asset_id);\n        if (!asset) {\n            throw new Error('Asset not found');\n        }\n        // For sell transactions, calculate tax implications\n        let taxCalculation = null;\n        let realizedGainLoss = null;\n        if (investmentData.transaction_type === 'investment_sell') {\n            // Get the holding to determine purchase details\n            const holding = await _assets__WEBPACK_IMPORTED_MODULE_2__.HoldingService.getHolding(investmentData.account_id, investmentData.asset_id);\n            if (!holding) {\n                throw new Error('No holding found for this asset');\n            }\n            if (holding.quantity < investmentData.investment_quantity) {\n                throw new Error('Cannot sell more than current holding');\n            }\n            // Calculate tax implications\n            taxCalculation = await _tax_calculator__WEBPACK_IMPORTED_MODULE_1__.TaxCalculatorService.calculateCapitalGainsTax({\n                asset_class_id: asset.asset_class_id,\n                purchase_date: holding.created_at,\n                sale_date: investmentData.transaction_date,\n                purchase_price: holding.average_cost,\n                sale_price: investmentData.investment_price,\n                quantity: investmentData.investment_quantity,\n                fees: investmentData.fees\n            });\n            // Calculate realized gain/loss\n            realizedGainLoss = await _profit_loss__WEBPACK_IMPORTED_MODULE_3__.ProfitLossService.calculateRealizedGainLoss(investmentData.asset_id, investmentData.investment_quantity, investmentData.investment_price, investmentData.transaction_date, holding.average_cost, holding.created_at);\n        }\n        // Create the investment transaction using the existing method\n        const transaction = await this.createInvestmentTransaction(investmentData, fundingAccountId);\n        // Update holdings\n        const holding = await _assets__WEBPACK_IMPORTED_MODULE_2__.HoldingService.updateHolding(investmentData.account_id, investmentData.asset_id, investmentData.transaction_type === 'investment_buy' ? 'buy' : 'sell', investmentData.investment_quantity, investmentData.investment_price);\n        return {\n            transaction,\n            holding,\n            taxCalculation,\n            realizedGainLoss\n        };\n    }\n    /**\n   * Get investment transactions for a user or specific account\n   */ static async getInvestmentTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*)\\n      \", {\n            count: 'exact'\n        }).eq('user_id', user.id).in('transaction_type', [\n            'investment_buy',\n            'investment_sell'\n        ]).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            query = query.eq('account_id', options.account_id);\n        }\n        if (options === null || options === void 0 ? void 0 : options.symbol) {\n            query = query.eq('investment_symbol', options.symbol);\n        }\n        if (options === null || options === void 0 ? void 0 : options.transaction_type) {\n            query = query.eq('transaction_type', options.transaction_type);\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 20) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch investment transactions: \".concat(error.message));\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    /**\n   * Get investment holdings for a user or specific account\n   */ static async getInvestmentHoldings(accountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_holdings').select(\"\\n        *,\\n        account:accounts(*)\\n      \").order('symbol');\n        if (accountId) {\n            query = query.eq('account_id', accountId);\n        } else {\n            // Filter by user's accounts\n            const { data: userAccounts } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('id').eq('user_id', user.id).eq('account_type', 'investment');\n            if (userAccounts && userAccounts.length > 0) {\n                const accountIds = userAccounts.map((acc)=>acc.id);\n                query = query.in('account_id', accountIds);\n            } else {\n                return [];\n            }\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch investment holdings: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get portfolio summary for all investment accounts\n   */ static async getPortfolioSummary() {\n        const holdings = await this.getInvestmentHoldings();\n        let totalValue = 0;\n        let totalCost = 0;\n        const holdingsBySymbol = {};\n        holdings.forEach((holding)=>{\n            const symbol = holding.symbol;\n            const quantity = holding.quantity;\n            const avgCost = holding.average_cost;\n            const currentPrice = holding.current_price || avgCost;\n            const marketValue = quantity * currentPrice;\n            const costBasis = quantity * avgCost;\n            const gainLoss = marketValue - costBasis;\n            const gainLossPercent = costBasis > 0 ? gainLoss / costBasis * 100 : 0;\n            if (!holdingsBySymbol[symbol]) {\n                holdingsBySymbol[symbol] = {\n                    symbol,\n                    totalQuantity: 0,\n                    averageCost: 0,\n                    currentPrice,\n                    marketValue: 0,\n                    gainLoss: 0,\n                    gainLossPercent: 0\n                };\n            }\n            // Aggregate holdings for the same symbol across accounts\n            const existing = holdingsBySymbol[symbol];\n            const newTotalQuantity = existing.totalQuantity + quantity;\n            const newTotalCost = existing.totalQuantity * existing.averageCost + quantity * avgCost;\n            holdingsBySymbol[symbol] = {\n                ...existing,\n                totalQuantity: newTotalQuantity,\n                averageCost: newTotalQuantity > 0 ? newTotalCost / newTotalQuantity : 0,\n                marketValue: existing.marketValue + marketValue,\n                gainLoss: existing.gainLoss + gainLoss\n            };\n            // Recalculate percentage\n            const totalCostBasis = holdingsBySymbol[symbol].totalQuantity * holdingsBySymbol[symbol].averageCost;\n            holdingsBySymbol[symbol].gainLossPercent = totalCostBasis > 0 ? holdingsBySymbol[symbol].gainLoss / totalCostBasis * 100 : 0;\n            totalValue += marketValue;\n            totalCost += costBasis;\n        });\n        const totalGainLoss = totalValue - totalCost;\n        const totalGainLossPercent = totalCost > 0 ? totalGainLoss / totalCost * 100 : 0;\n        return {\n            totalValue,\n            totalCost,\n            totalGainLoss,\n            totalGainLossPercent,\n            holdingsBySymbol\n        };\n    }\n    /**\n   * Update current prices for holdings (would typically be called by a background job)\n   */ static async updateHoldingPrices(priceUpdates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        for (const update of priceUpdates){\n            const marketValue = update.price // Will be calculated by trigger\n            ;\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_holdings').update({\n                current_price: update.price,\n                market_value: marketValue,\n                last_updated: new Date().toISOString()\n            }).eq('symbol', update.symbol);\n            if (error) {\n                console.error(\"Failed to update price for \".concat(update.symbol, \":\"), error);\n            }\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL2xpYi9pbnZlc3RtZW50cy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFxQztBQUNrQjtBQUNBO0FBQ047QUFJMUMsTUFBTUs7SUFDWDs7O0dBR0MsR0FDRCxhQUFhQyw0QkFDWEMsY0FBK0IsRUFDL0JDLGdCQUF5QixnQ0FBaUM7TUFDekI7UUFDakMsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTVYsK0NBQVFBLENBQUNXLElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVCxNQUFNLElBQUlHLE1BQU07UUFDbEI7UUFFQSxrRUFBa0U7UUFDbEUsTUFBTSxFQUFFSixNQUFNSyxpQkFBaUIsRUFBRSxHQUFHLE1BQU1kLCtDQUFRQSxDQUMvQ2UsSUFBSSxDQUFDLFlBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsTUFBTVYsZUFBZVcsVUFBVSxFQUNsQ0QsRUFBRSxDQUFDLFdBQVdQLEtBQUtTLEVBQUUsRUFDckJDLE1BQU07UUFFVCxJQUFJLENBQUNOLHFCQUFxQkEsa0JBQWtCTyxZQUFZLEtBQUssY0FBYztZQUN6RSxNQUFNLElBQUlSLE1BQU07UUFDbEI7UUFFQSxtRUFBbUU7UUFDbkUsSUFBSU4sZUFBZWUsZ0JBQWdCLEtBQUssa0JBQWtCO1lBQ3hELElBQUksQ0FBQ2Qsa0JBQWtCO2dCQUNyQixNQUFNLElBQUlLLE1BQU07WUFDbEI7WUFFQSxNQUFNLEVBQUVKLE1BQU1jLGNBQWMsRUFBRSxHQUFHLE1BQU12QiwrQ0FBUUEsQ0FDNUNlLElBQUksQ0FBQyxZQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLE1BQU1ULGtCQUNUUyxFQUFFLENBQUMsV0FBV1AsS0FBS1MsRUFBRSxFQUNyQkMsTUFBTTtZQUVULElBQUksQ0FBQ0csZ0JBQWdCO2dCQUNuQixNQUFNLElBQUlWLE1BQU07WUFDbEI7WUFFQSxNQUFNVyxZQUFZakIsZUFBZWtCLE1BQU0sR0FBSWxCLENBQUFBLGVBQWVtQixJQUFJLElBQUk7WUFDbEUsSUFBSUgsZUFBZUksZUFBZSxHQUFHSCxXQUFXO2dCQUM5QyxNQUFNLElBQUlYLE1BQU07WUFDbEI7UUFDRjtRQUVBLDBCQUEwQjtRQUMxQixNQUFNZSxlQUFlckIsZUFBZWUsZ0JBQWdCLEtBQUssbUJBQ3JELHdCQUNBO1FBRUosTUFBTSxFQUFFYixNQUFNb0IsUUFBUSxFQUFFLEdBQUcsTUFBTTdCLCtDQUFRQSxDQUN0Q2UsSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBQyxNQUNQQyxFQUFFLENBQUMsUUFBUVcsY0FDWFgsRUFBRSxDQUFDLGFBQWEsTUFDaEJHLE1BQU07UUFFVCxJQUFJLENBQUNTLFVBQVU7WUFDYixNQUFNLElBQUloQixNQUFNLEdBQWdCLE9BQWJlLGNBQWE7UUFDbEM7UUFFQSxvQ0FBb0M7UUFDcEMsTUFBTUUsa0JBQWdEO1lBQ3BETCxRQUFRbEIsZUFBZWtCLE1BQU07WUFDN0JNLGFBQWF4QixlQUFld0IsV0FBVyxJQUNyQyxHQUE0RXhCLE9BQXpFQSxlQUFlZSxnQkFBZ0IsS0FBSyxtQkFBbUIsUUFBUSxRQUFPLEtBQW1EZixPQUFoREEsZUFBZXlCLG1CQUFtQixFQUFDLGVBQThDLE9BQWpDekIsZUFBZTBCLGlCQUFpQjtZQUM5SkMsYUFBYUwsU0FBU1YsRUFBRTtZQUN4QkQsWUFBWVgsZUFBZVcsVUFBVTtZQUNyQ0ksa0JBQWtCZixlQUFlZSxnQkFBZ0I7WUFDakRhLGtCQUFrQjVCLGVBQWU0QixnQkFBZ0I7WUFDakRDLG9CQUFvQjtZQUNwQlYsTUFBTW5CLGVBQWVtQixJQUFJLElBQUk7WUFDN0JPLG1CQUFtQjFCLGVBQWUwQixpQkFBaUI7WUFDbkRELHFCQUFxQnpCLGVBQWV5QixtQkFBbUI7WUFDdkRLLGtCQUFrQjlCLGVBQWU4QixnQkFBZ0I7WUFDakRDLFNBQVM1QixLQUFLUyxFQUFFO1FBQ2xCO1FBRUEsTUFBTSxFQUFFVixNQUFNOEIsV0FBVyxFQUFFQyxPQUFPQyxnQkFBZ0IsRUFBRSxHQUFHLE1BQU16QywrQ0FBUUEsQ0FDbEVlLElBQUksQ0FBQyxnQkFDTDJCLE1BQU0sQ0FBQ1osaUJBQ1BkLE1BQU0sQ0FBRSxzRkFLUkksTUFBTTtRQUVULElBQUlxQixrQkFBa0I7WUFDcEIsTUFBTSxJQUFJNUIsTUFBTSw0Q0FBcUUsT0FBekI0QixpQkFBaUJFLE9BQU87UUFDdEY7UUFFQSxxRkFBcUY7UUFDckYsSUFBSXBDLGVBQWVlLGdCQUFnQixLQUFLLG9CQUFvQmQsa0JBQWtCO1lBQzVFLE1BQU0sRUFBRW9DLGVBQWUsRUFBRSxHQUFHLE1BQU0seU9BQXFCO1lBRXZELElBQUk7Z0JBQ0YsTUFBTUEsZ0JBQWdCQyxjQUFjLENBQUM7b0JBQ25DcEIsUUFBUWxCLGVBQWVrQixNQUFNLEdBQUlsQixDQUFBQSxlQUFlbUIsSUFBSSxJQUFJO29CQUN4REssYUFBYSx3QkFBeUQsT0FBakN4QixlQUFlMEIsaUJBQWlCO29CQUNyRWEsaUJBQWlCdEM7b0JBQ2pCdUMsZUFBZXhDLGVBQWVXLFVBQVU7b0JBQ3hDaUIsa0JBQWtCNUIsZUFBZTRCLGdCQUFnQjtvQkFDakRULE1BQU07Z0JBQ1I7WUFDRixFQUFFLE9BQU9jLE9BQU87Z0JBQ2QseURBQXlEO2dCQUN6RCxNQUFNeEMsK0NBQVFBLENBQ1hlLElBQUksQ0FBQyxnQkFDTGlDLE1BQU0sR0FDTi9CLEVBQUUsQ0FBQyxNQUFNc0IsWUFBWXBCLEVBQUU7Z0JBRTFCLE1BQU0sSUFBSU4sTUFBTSxzQ0FBK0YsT0FBekQyQixpQkFBaUIzQixRQUFRMkIsTUFBTUcsT0FBTyxHQUFHO1lBQ2pHO1FBQ0Y7UUFFQSxPQUFPSjtJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhVSxzQ0FDWDFDLGNBQXNELEVBQ3REQyxnQkFBeUIsRUFNeEI7UUFDRCxNQUFNLEVBQUVDLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUUsR0FBRyxNQUFNViwrQ0FBUUEsQ0FBQ1csSUFBSSxDQUFDQyxPQUFPO1FBRXRELElBQUksQ0FBQ0YsTUFBTTtZQUNULE1BQU0sSUFBSUcsTUFBTTtRQUNsQjtRQUVBLHdCQUF3QjtRQUN4QixNQUFNcUMsUUFBUSxNQUFNL0MsaURBQVlBLENBQUNnRCxRQUFRLENBQUM1QyxlQUFlNkMsUUFBUTtRQUVqRSxJQUFJLENBQUNGLE9BQU87WUFDVixNQUFNLElBQUlyQyxNQUFNO1FBQ2xCO1FBRUEsb0RBQW9EO1FBQ3BELElBQUl3QyxpQkFBaUI7UUFDckIsSUFBSUMsbUJBQW1CO1FBRXZCLElBQUkvQyxlQUFlZSxnQkFBZ0IsS0FBSyxtQkFBbUI7WUFDekQsZ0RBQWdEO1lBQ2hELE1BQU1pQyxVQUFVLE1BQU1yRCxtREFBY0EsQ0FBQ3NELFVBQVUsQ0FBQ2pELGVBQWVXLFVBQVUsRUFBRVgsZUFBZTZDLFFBQVE7WUFFbEcsSUFBSSxDQUFDRyxTQUFTO2dCQUNaLE1BQU0sSUFBSTFDLE1BQU07WUFDbEI7WUFFQSxJQUFJMEMsUUFBUUUsUUFBUSxHQUFHbEQsZUFBZXlCLG1CQUFtQixFQUFFO2dCQUN6RCxNQUFNLElBQUluQixNQUFNO1lBQ2xCO1lBRUEsNkJBQTZCO1lBQzdCd0MsaUJBQWlCLE1BQU1wRCxpRUFBb0JBLENBQUN5RCx3QkFBd0IsQ0FBQztnQkFDbkVDLGdCQUFnQlQsTUFBTVMsY0FBYztnQkFDcENDLGVBQWVMLFFBQVFNLFVBQVU7Z0JBQ2pDQyxXQUFXdkQsZUFBZTRCLGdCQUFnQjtnQkFDMUM0QixnQkFBZ0JSLFFBQVFTLFlBQVk7Z0JBQ3BDQyxZQUFZMUQsZUFBZThCLGdCQUFnQjtnQkFDM0NvQixVQUFVbEQsZUFBZXlCLG1CQUFtQjtnQkFDNUNOLE1BQU1uQixlQUFlbUIsSUFBSTtZQUMzQjtZQUVBLCtCQUErQjtZQUMvQjRCLG1CQUFtQixNQUFNbEQsMkRBQWlCQSxDQUFDOEQseUJBQXlCLENBQ2xFM0QsZUFBZTZDLFFBQVEsRUFDdkI3QyxlQUFleUIsbUJBQW1CLEVBQ2xDekIsZUFBZThCLGdCQUFnQixFQUMvQjlCLGVBQWU0QixnQkFBZ0IsRUFDL0JvQixRQUFRUyxZQUFZLEVBQ3BCVCxRQUFRTSxVQUFVO1FBRXRCO1FBRUEsOERBQThEO1FBQzlELE1BQU10QixjQUFjLE1BQU0sSUFBSSxDQUFDakMsMkJBQTJCLENBQUNDLGdCQUFnQkM7UUFFM0Usa0JBQWtCO1FBQ2xCLE1BQU0rQyxVQUFVLE1BQU1yRCxtREFBY0EsQ0FBQ2lFLGFBQWEsQ0FDaEQ1RCxlQUFlVyxVQUFVLEVBQ3pCWCxlQUFlNkMsUUFBUSxFQUN2QjdDLGVBQWVlLGdCQUFnQixLQUFLLG1CQUFtQixRQUFRLFFBQy9EZixlQUFleUIsbUJBQW1CLEVBQ2xDekIsZUFBZThCLGdCQUFnQjtRQUdqQyxPQUFPO1lBQ0xFO1lBQ0FnQjtZQUNBRjtZQUNBQztRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFjLDBCQUEwQkMsT0FRdEMsRUFBOEQ7UUFDN0QsTUFBTSxFQUFFNUQsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRSxHQUFHLE1BQU1WLCtDQUFRQSxDQUFDVyxJQUFJLENBQUNDLE9BQU87UUFFdEQsSUFBSSxDQUFDRixNQUFNO1lBQ1QsTUFBTSxJQUFJRyxNQUFNO1FBQ2xCO1FBRUEsSUFBSXlELFFBQVF0RSwrQ0FBUUEsQ0FDakJlLElBQUksQ0FBQyxnQkFDTEMsTUFBTSxDQUFFLG1IQUlOO1lBQUV1RCxPQUFPO1FBQVEsR0FDbkJ0RCxFQUFFLENBQUMsV0FBV1AsS0FBS1MsRUFBRSxFQUNyQnFELEVBQUUsQ0FBQyxvQkFBb0I7WUFBQztZQUFrQjtTQUFrQixFQUM1REMsS0FBSyxDQUFDLG9CQUFvQjtZQUFFQyxXQUFXO1FBQU0sR0FDN0NELEtBQUssQ0FBQyxjQUFjO1lBQUVDLFdBQVc7UUFBTTtRQUUxQyxJQUFJTCxvQkFBQUEsOEJBQUFBLFFBQVNuRCxVQUFVLEVBQUU7WUFDdkJvRCxRQUFRQSxNQUFNckQsRUFBRSxDQUFDLGNBQWNvRCxRQUFRbkQsVUFBVTtRQUNuRDtRQUVBLElBQUltRCxvQkFBQUEsOEJBQUFBLFFBQVNNLE1BQU0sRUFBRTtZQUNuQkwsUUFBUUEsTUFBTXJELEVBQUUsQ0FBQyxxQkFBcUJvRCxRQUFRTSxNQUFNO1FBQ3REO1FBRUEsSUFBSU4sb0JBQUFBLDhCQUFBQSxRQUFTL0MsZ0JBQWdCLEVBQUU7WUFDN0JnRCxRQUFRQSxNQUFNckQsRUFBRSxDQUFDLG9CQUFvQm9ELFFBQVEvQyxnQkFBZ0I7UUFDL0Q7UUFFQSxJQUFJK0Msb0JBQUFBLDhCQUFBQSxRQUFTTyxTQUFTLEVBQUU7WUFDdEJOLFFBQVFBLE1BQU1PLEdBQUcsQ0FBQyxvQkFBb0JSLFFBQVFPLFNBQVM7UUFDekQ7UUFFQSxJQUFJUCxvQkFBQUEsOEJBQUFBLFFBQVNTLE9BQU8sRUFBRTtZQUNwQlIsUUFBUUEsTUFBTVMsR0FBRyxDQUFDLG9CQUFvQlYsUUFBUVMsT0FBTztRQUN2RDtRQUVBLElBQUlULG9CQUFBQSw4QkFBQUEsUUFBU1csS0FBSyxFQUFFO1lBQ2xCVixRQUFRQSxNQUFNVSxLQUFLLENBQUNYLFFBQVFXLEtBQUs7UUFDbkM7UUFFQSxJQUFJWCxvQkFBQUEsOEJBQUFBLFFBQVNZLE1BQU0sRUFBRTtZQUNuQlgsUUFBUUEsTUFBTVksS0FBSyxDQUFDYixRQUFRWSxNQUFNLEVBQUVaLFFBQVFZLE1BQU0sR0FBSVosQ0FBQUEsUUFBUVcsS0FBSyxJQUFJLEVBQUMsSUFBSztRQUMvRTtRQUVBLE1BQU0sRUFBRXZFLElBQUksRUFBRStCLEtBQUssRUFBRStCLEtBQUssRUFBRSxHQUFHLE1BQU1EO1FBRXJDLElBQUk5QixPQUFPO1lBQ1QsTUFBTSxJQUFJM0IsTUFBTSw0Q0FBMEQsT0FBZDJCLE1BQU1HLE9BQU87UUFDM0U7UUFFQSxPQUFPO1lBQUVsQyxNQUFNQTtZQUFrQzhELE9BQU9BLFNBQVM7UUFBRTtJQUNyRTtJQUVBOztHQUVDLEdBQ0QsYUFBYVksc0JBQXNCQyxTQUFrQixFQUFpQztRQUNwRixNQUFNLEVBQUUzRSxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTVYsK0NBQVFBLENBQUNXLElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVCxNQUFNLElBQUlHLE1BQU07UUFDbEI7UUFFQSxJQUFJeUQsUUFBUXRFLCtDQUFRQSxDQUNqQmUsSUFBSSxDQUFDLHVCQUNMQyxNQUFNLENBQUUscURBSVJ5RCxLQUFLLENBQUM7UUFFVCxJQUFJVyxXQUFXO1lBQ2JkLFFBQVFBLE1BQU1yRCxFQUFFLENBQUMsY0FBY21FO1FBQ2pDLE9BQU87WUFDTCw0QkFBNEI7WUFDNUIsTUFBTSxFQUFFM0UsTUFBTTRFLFlBQVksRUFBRSxHQUFHLE1BQU1yRiwrQ0FBUUEsQ0FDMUNlLElBQUksQ0FBQyxZQUNMQyxNQUFNLENBQUMsTUFDUEMsRUFBRSxDQUFDLFdBQVdQLEtBQUtTLEVBQUUsRUFDckJGLEVBQUUsQ0FBQyxnQkFBZ0I7WUFFdEIsSUFBSW9FLGdCQUFnQkEsYUFBYUMsTUFBTSxHQUFHLEdBQUc7Z0JBQzNDLE1BQU1DLGFBQWFGLGFBQWFHLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSXRFLEVBQUU7Z0JBQ2pEbUQsUUFBUUEsTUFBTUUsRUFBRSxDQUFDLGNBQWNlO1lBQ2pDLE9BQU87Z0JBQ0wsT0FBTyxFQUFFO1lBQ1g7UUFDRjtRQUVBLE1BQU0sRUFBRTlFLElBQUksRUFBRStCLEtBQUssRUFBRSxHQUFHLE1BQU04QjtRQUU5QixJQUFJOUIsT0FBTztZQUNULE1BQU0sSUFBSTNCLE1BQU0sd0NBQXNELE9BQWQyQixNQUFNRyxPQUFPO1FBQ3ZFO1FBRUEsT0FBT2xDO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELGFBQWFpRixzQkFjVjtRQUNELE1BQU1DLFdBQVcsTUFBTSxJQUFJLENBQUNSLHFCQUFxQjtRQUVqRCxJQUFJUyxhQUFhO1FBQ2pCLElBQUlwRSxZQUFZO1FBQ2hCLE1BQU1xRSxtQkFBd0MsQ0FBQztRQUUvQ0YsU0FBU0csT0FBTyxDQUFDdkMsQ0FBQUE7WUFDZixNQUFNb0IsU0FBU3BCLFFBQVFvQixNQUFNO1lBQzdCLE1BQU1sQixXQUFXRixRQUFRRSxRQUFRO1lBQ2pDLE1BQU1zQyxVQUFVeEMsUUFBUVMsWUFBWTtZQUNwQyxNQUFNZ0MsZUFBZXpDLFFBQVEwQyxhQUFhLElBQUlGO1lBQzlDLE1BQU1HLGNBQWN6QyxXQUFXdUM7WUFDL0IsTUFBTUcsWUFBWTFDLFdBQVdzQztZQUM3QixNQUFNSyxXQUFXRixjQUFjQztZQUMvQixNQUFNRSxrQkFBa0JGLFlBQVksSUFBSSxXQUFZQSxZQUFhLE1BQU07WUFFdkUsSUFBSSxDQUFDTixnQkFBZ0IsQ0FBQ2xCLE9BQU8sRUFBRTtnQkFDN0JrQixnQkFBZ0IsQ0FBQ2xCLE9BQU8sR0FBRztvQkFDekJBO29CQUNBMkIsZUFBZTtvQkFDZkMsYUFBYTtvQkFDYlA7b0JBQ0FFLGFBQWE7b0JBQ2JFLFVBQVU7b0JBQ1ZDLGlCQUFpQjtnQkFDbkI7WUFDRjtZQUVBLHlEQUF5RDtZQUN6RCxNQUFNRyxXQUFXWCxnQkFBZ0IsQ0FBQ2xCLE9BQU87WUFDekMsTUFBTThCLG1CQUFtQkQsU0FBU0YsYUFBYSxHQUFHN0M7WUFDbEQsTUFBTWlELGVBQWUsU0FBVUosYUFBYSxHQUFHRSxTQUFTRCxXQUFXLEdBQUs5QyxXQUFXc0M7WUFFbkZGLGdCQUFnQixDQUFDbEIsT0FBTyxHQUFHO2dCQUN6QixHQUFHNkIsUUFBUTtnQkFDWEYsZUFBZUc7Z0JBQ2ZGLGFBQWFFLG1CQUFtQixJQUFJQyxlQUFlRCxtQkFBbUI7Z0JBQ3RFUCxhQUFhTSxTQUFTTixXQUFXLEdBQUdBO2dCQUNwQ0UsVUFBVUksU0FBU0osUUFBUSxHQUFHQTtZQUNoQztZQUVBLHlCQUF5QjtZQUN6QixNQUFNTyxpQkFBaUJkLGdCQUFnQixDQUFDbEIsT0FBTyxDQUFDMkIsYUFBYSxHQUFHVCxnQkFBZ0IsQ0FBQ2xCLE9BQU8sQ0FBQzRCLFdBQVc7WUFDcEdWLGdCQUFnQixDQUFDbEIsT0FBTyxDQUFDMEIsZUFBZSxHQUFHTSxpQkFBaUIsSUFDeEQsZ0JBQWlCLENBQUNoQyxPQUFPLENBQUN5QixRQUFRLEdBQUdPLGlCQUFrQixNQUN2RDtZQUVKZixjQUFjTTtZQUNkMUUsYUFBYTJFO1FBQ2Y7UUFFQSxNQUFNUyxnQkFBZ0JoQixhQUFhcEU7UUFDbkMsTUFBTXFGLHVCQUF1QnJGLFlBQVksSUFBSSxnQkFBaUJBLFlBQWEsTUFBTTtRQUVqRixPQUFPO1lBQ0xvRTtZQUNBcEU7WUFDQW9GO1lBQ0FDO1lBQ0FoQjtRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFpQixvQkFBb0JDLFlBQWlELEVBQWlCO1FBQ2pHLE1BQU0sRUFBRXRHLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUUsR0FBRyxNQUFNViwrQ0FBUUEsQ0FBQ1csSUFBSSxDQUFDQyxPQUFPO1FBRXRELElBQUksQ0FBQ0YsTUFBTTtZQUNULE1BQU0sSUFBSUcsTUFBTTtRQUNsQjtRQUVBLEtBQUssTUFBTW1HLFVBQVVELGFBQWM7WUFDakMsTUFBTWIsY0FBY2MsT0FBT0MsS0FBSyxDQUFDLGdDQUFnQzs7WUFFakUsTUFBTSxFQUFFekUsS0FBSyxFQUFFLEdBQUcsTUFBTXhDLCtDQUFRQSxDQUM3QmUsSUFBSSxDQUFDLHVCQUNMaUcsTUFBTSxDQUFDO2dCQUNOZixlQUFlZSxPQUFPQyxLQUFLO2dCQUMzQkMsY0FBY2hCO2dCQUNkaUIsY0FBYyxJQUFJQyxPQUFPQyxXQUFXO1lBQ3RDLEdBQ0NwRyxFQUFFLENBQUMsVUFBVStGLE9BQU9yQyxNQUFNO1lBRTdCLElBQUluQyxPQUFPO2dCQUNUOEUsUUFBUTlFLEtBQUssQ0FBQyw4QkFBNEMsT0FBZHdFLE9BQU9yQyxNQUFNLEVBQUMsTUFBSW5DO1lBQ2hFO1FBQ0Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL3BhY2thZ2VzL3NoYXJlZC9zcmMvbGliL2ludmVzdG1lbnRzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN1cGFiYXNlIH0gZnJvbSAnLi9zdXBhYmFzZSdcbmltcG9ydCB7IFRheENhbGN1bGF0b3JTZXJ2aWNlIH0gZnJvbSAnLi90YXgtY2FsY3VsYXRvcidcbmltcG9ydCB7IEhvbGRpbmdTZXJ2aWNlLCBBc3NldFNlcnZpY2UgfSBmcm9tICcuL2Fzc2V0cydcbmltcG9ydCB7IFByb2ZpdExvc3NTZXJ2aWNlIH0gZnJvbSAnLi9wcm9maXQtbG9zcydcbmltcG9ydCB0eXBlIHsgSUludmVzdG1lbnRUcmFuc2FjdGlvbiwgSUludmVzdG1lbnRGb3JtLCBJSW52ZXN0bWVudEhvbGRpbmcsIElBY2NvdW50IH0gZnJvbSAnLi4vdHlwZXMnXG5pbXBvcnQgdHlwZSB7IFRhYmxlc0luc2VydCB9IGZyb20gJy4uL2RhdGFiYXNlLnR5cGVzJ1xuXG5leHBvcnQgY2xhc3MgSW52ZXN0bWVudFNlcnZpY2Uge1xuICAvKipcbiAgICogQ3JlYXRlIGFuIGludmVzdG1lbnQgdHJhbnNhY3Rpb24gKGJ1eS9zZWxsKVxuICAgKiBUaGlzIGFsc28gY3JlYXRlcyBhIHRyYW5zZmVyIGZyb20gYSBmdW5kaW5nIGFjY291bnQgZm9yIGJ1eSB0cmFuc2FjdGlvbnNcbiAgICovXG4gIHN0YXRpYyBhc3luYyBjcmVhdGVJbnZlc3RtZW50VHJhbnNhY3Rpb24oXG4gICAgaW52ZXN0bWVudERhdGE6IElJbnZlc3RtZW50Rm9ybSxcbiAgICBmdW5kaW5nQWNjb3VudElkPzogc3RyaW5nIC8vIFJlcXVpcmVkIGZvciBidXkgdHJhbnNhY3Rpb25zXG4gICk6IFByb21pc2U8SUludmVzdG1lbnRUcmFuc2FjdGlvbj4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIGludmVzdG1lbnQgYWNjb3VudCBleGlzdHMgYW5kIGlzIGFuIGludmVzdG1lbnQgYWNjb3VudFxuICAgIGNvbnN0IHsgZGF0YTogaW52ZXN0bWVudEFjY291bnQgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnYWNjb3VudHMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ2lkJywgaW52ZXN0bWVudERhdGEuYWNjb3VudF9pZClcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmICghaW52ZXN0bWVudEFjY291bnQgfHwgaW52ZXN0bWVudEFjY291bnQuYWNjb3VudF90eXBlICE9PSAnaW52ZXN0bWVudCcpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBpbnZlc3RtZW50IGFjY291bnQgc3BlY2lmaWVkJylcbiAgICB9XG5cbiAgICAvLyBGb3IgYnV5IHRyYW5zYWN0aW9ucywgdmFsaWRhdGUgZnVuZGluZyBhY2NvdW50IGFuZCBjaGVjayBiYWxhbmNlXG4gICAgaWYgKGludmVzdG1lbnREYXRhLnRyYW5zYWN0aW9uX3R5cGUgPT09ICdpbnZlc3RtZW50X2J1eScpIHtcbiAgICAgIGlmICghZnVuZGluZ0FjY291bnRJZCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0Z1bmRpbmcgYWNjb3VudCByZXF1aXJlZCBmb3IgaW52ZXN0bWVudCBwdXJjaGFzZXMnKVxuICAgICAgfVxuXG4gICAgICBjb25zdCB7IGRhdGE6IGZ1bmRpbmdBY2NvdW50IH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnYWNjb3VudHMnKVxuICAgICAgICAuc2VsZWN0KCcqJylcbiAgICAgICAgLmVxKCdpZCcsIGZ1bmRpbmdBY2NvdW50SWQpXG4gICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICBpZiAoIWZ1bmRpbmdBY2NvdW50KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBmdW5kaW5nIGFjY291bnQgc3BlY2lmaWVkJylcbiAgICAgIH1cblxuICAgICAgY29uc3QgdG90YWxDb3N0ID0gaW52ZXN0bWVudERhdGEuYW1vdW50ICsgKGludmVzdG1lbnREYXRhLmZlZXMgfHwgMClcbiAgICAgIGlmIChmdW5kaW5nQWNjb3VudC5jdXJyZW50X2JhbGFuY2UgPCB0b3RhbENvc3QpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnN1ZmZpY2llbnQgYmFsYW5jZSBpbiBmdW5kaW5nIGFjY291bnQnKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEdldCBpbnZlc3RtZW50IGNhdGVnb3J5XG4gICAgY29uc3QgY2F0ZWdvcnlOYW1lID0gaW52ZXN0bWVudERhdGEudHJhbnNhY3Rpb25fdHlwZSA9PT0gJ2ludmVzdG1lbnRfYnV5JyBcbiAgICAgID8gJ0ludmVzdG1lbnQgUHVyY2hhc2UnIFxuICAgICAgOiAnSW52ZXN0bWVudCBTYWxlJ1xuICAgIFxuICAgIGNvbnN0IHsgZGF0YTogY2F0ZWdvcnkgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnY2F0ZWdvcmllcycpXG4gICAgICAuc2VsZWN0KCdpZCcpXG4gICAgICAuZXEoJ25hbWUnLCBjYXRlZ29yeU5hbWUpXG4gICAgICAuZXEoJ2lzX3N5c3RlbScsIHRydWUpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmICghY2F0ZWdvcnkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgJHtjYXRlZ29yeU5hbWV9IGNhdGVnb3J5IG5vdCBmb3VuZGApXG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIHRoZSBpbnZlc3RtZW50IHRyYW5zYWN0aW9uXG4gICAgY29uc3QgdHJhbnNhY3Rpb25EYXRhOiBUYWJsZXNJbnNlcnQ8J3RyYW5zYWN0aW9ucyc+ID0ge1xuICAgICAgYW1vdW50OiBpbnZlc3RtZW50RGF0YS5hbW91bnQsXG4gICAgICBkZXNjcmlwdGlvbjogaW52ZXN0bWVudERhdGEuZGVzY3JpcHRpb24gfHwgXG4gICAgICAgIGAke2ludmVzdG1lbnREYXRhLnRyYW5zYWN0aW9uX3R5cGUgPT09ICdpbnZlc3RtZW50X2J1eScgPyAnQnV5JyA6ICdTZWxsJ30gJHtpbnZlc3RtZW50RGF0YS5pbnZlc3RtZW50X3F1YW50aXR5fSBzaGFyZXMgb2YgJHtpbnZlc3RtZW50RGF0YS5pbnZlc3RtZW50X3N5bWJvbH1gLFxuICAgICAgY2F0ZWdvcnlfaWQ6IGNhdGVnb3J5LmlkLFxuICAgICAgYWNjb3VudF9pZDogaW52ZXN0bWVudERhdGEuYWNjb3VudF9pZCxcbiAgICAgIHRyYW5zYWN0aW9uX3R5cGU6IGludmVzdG1lbnREYXRhLnRyYW5zYWN0aW9uX3R5cGUsXG4gICAgICB0cmFuc2FjdGlvbl9kYXRlOiBpbnZlc3RtZW50RGF0YS50cmFuc2FjdGlvbl9kYXRlLFxuICAgICAgdHJhbnNhY3Rpb25fc3RhdHVzOiAnY29tcGxldGVkJyxcbiAgICAgIGZlZXM6IGludmVzdG1lbnREYXRhLmZlZXMgfHwgMCxcbiAgICAgIGludmVzdG1lbnRfc3ltYm9sOiBpbnZlc3RtZW50RGF0YS5pbnZlc3RtZW50X3N5bWJvbCxcbiAgICAgIGludmVzdG1lbnRfcXVhbnRpdHk6IGludmVzdG1lbnREYXRhLmludmVzdG1lbnRfcXVhbnRpdHksXG4gICAgICBpbnZlc3RtZW50X3ByaWNlOiBpbnZlc3RtZW50RGF0YS5pbnZlc3RtZW50X3ByaWNlLFxuICAgICAgdXNlcl9pZDogdXNlci5pZCxcbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGE6IHRyYW5zYWN0aW9uLCBlcnJvcjogdHJhbnNhY3Rpb25FcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd0cmFuc2FjdGlvbnMnKVxuICAgICAgLmluc2VydCh0cmFuc2FjdGlvbkRhdGEpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgY2F0ZWdvcnk6Y2F0ZWdvcmllcygqKSxcbiAgICAgICAgYWNjb3VudDphY2NvdW50cygqKVxuICAgICAgYClcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKHRyYW5zYWN0aW9uRXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSBpbnZlc3RtZW50IHRyYW5zYWN0aW9uOiAke3RyYW5zYWN0aW9uRXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIC8vIEZvciBidXkgdHJhbnNhY3Rpb25zLCBjcmVhdGUgYSB0cmFuc2ZlciBmcm9tIGZ1bmRpbmcgYWNjb3VudCB0byBpbnZlc3RtZW50IGFjY291bnRcbiAgICBpZiAoaW52ZXN0bWVudERhdGEudHJhbnNhY3Rpb25fdHlwZSA9PT0gJ2ludmVzdG1lbnRfYnV5JyAmJiBmdW5kaW5nQWNjb3VudElkKSB7XG4gICAgICBjb25zdCB7IFRyYW5zZmVyU2VydmljZSB9ID0gYXdhaXQgaW1wb3J0KCcuL3RyYW5zZmVycycpXG4gICAgICBcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IFRyYW5zZmVyU2VydmljZS5jcmVhdGVUcmFuc2Zlcih7XG4gICAgICAgICAgYW1vdW50OiBpbnZlc3RtZW50RGF0YS5hbW91bnQgKyAoaW52ZXN0bWVudERhdGEuZmVlcyB8fCAwKSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogYEludmVzdG1lbnQgcHVyY2hhc2U6ICR7aW52ZXN0bWVudERhdGEuaW52ZXN0bWVudF9zeW1ib2x9YCxcbiAgICAgICAgICBmcm9tX2FjY291bnRfaWQ6IGZ1bmRpbmdBY2NvdW50SWQsXG4gICAgICAgICAgdG9fYWNjb3VudF9pZDogaW52ZXN0bWVudERhdGEuYWNjb3VudF9pZCxcbiAgICAgICAgICB0cmFuc2FjdGlvbl9kYXRlOiBpbnZlc3RtZW50RGF0YS50cmFuc2FjdGlvbl9kYXRlLFxuICAgICAgICAgIGZlZXM6IDAsIC8vIEZlZXMgYWxyZWFkeSBhY2NvdW50ZWQgZm9yIGluIGludmVzdG1lbnQgdHJhbnNhY3Rpb25cbiAgICAgICAgfSlcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIC8vIElmIHRyYW5zZmVyIGZhaWxzLCByb2xsYmFjayB0aGUgaW52ZXN0bWVudCB0cmFuc2FjdGlvblxuICAgICAgICBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAgIC5mcm9tKCd0cmFuc2FjdGlvbnMnKVxuICAgICAgICAgIC5kZWxldGUoKVxuICAgICAgICAgIC5lcSgnaWQnLCB0cmFuc2FjdGlvbi5pZClcbiAgICAgICAgXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSBmdW5kaW5nIHRyYW5zZmVyOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWApXG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHRyYW5zYWN0aW9uIGFzIElJbnZlc3RtZW50VHJhbnNhY3Rpb25cbiAgfVxuXG4gIC8qKlxuICAgKiBDcmVhdGUgaW52ZXN0bWVudCB0cmFuc2FjdGlvbiB3aXRoIGVuaGFuY2VkIGFzc2V0IHRyYWNraW5nIGFuZCB0YXggY2FsY3VsYXRpb25zXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgY3JlYXRlSW52ZXN0bWVudFRyYW5zYWN0aW9uV2l0aEFzc2V0cyhcbiAgICBpbnZlc3RtZW50RGF0YTogSUludmVzdG1lbnRGb3JtICYgeyBhc3NldF9pZDogc3RyaW5nIH0sXG4gICAgZnVuZGluZ0FjY291bnRJZD86IHN0cmluZ1xuICApOiBQcm9taXNlPHtcbiAgICB0cmFuc2FjdGlvbjogSUludmVzdG1lbnRUcmFuc2FjdGlvblxuICAgIGhvbGRpbmc6IGFueVxuICAgIHRheENhbGN1bGF0aW9uPzogYW55XG4gICAgcmVhbGl6ZWRHYWluTG9zcz86IGFueVxuICB9PiB7XG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKClcblxuICAgIGlmICghdXNlcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJylcbiAgICB9XG5cbiAgICAvLyBHZXQgYXNzZXQgaW5mb3JtYXRpb25cbiAgICBjb25zdCBhc3NldCA9IGF3YWl0IEFzc2V0U2VydmljZS5nZXRBc3NldChpbnZlc3RtZW50RGF0YS5hc3NldF9pZClcblxuICAgIGlmICghYXNzZXQpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignQXNzZXQgbm90IGZvdW5kJylcbiAgICB9XG5cbiAgICAvLyBGb3Igc2VsbCB0cmFuc2FjdGlvbnMsIGNhbGN1bGF0ZSB0YXggaW1wbGljYXRpb25zXG4gICAgbGV0IHRheENhbGN1bGF0aW9uID0gbnVsbFxuICAgIGxldCByZWFsaXplZEdhaW5Mb3NzID0gbnVsbFxuXG4gICAgaWYgKGludmVzdG1lbnREYXRhLnRyYW5zYWN0aW9uX3R5cGUgPT09ICdpbnZlc3RtZW50X3NlbGwnKSB7XG4gICAgICAvLyBHZXQgdGhlIGhvbGRpbmcgdG8gZGV0ZXJtaW5lIHB1cmNoYXNlIGRldGFpbHNcbiAgICAgIGNvbnN0IGhvbGRpbmcgPSBhd2FpdCBIb2xkaW5nU2VydmljZS5nZXRIb2xkaW5nKGludmVzdG1lbnREYXRhLmFjY291bnRfaWQsIGludmVzdG1lbnREYXRhLmFzc2V0X2lkKVxuXG4gICAgICBpZiAoIWhvbGRpbmcpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdObyBob2xkaW5nIGZvdW5kIGZvciB0aGlzIGFzc2V0JylcbiAgICAgIH1cblxuICAgICAgaWYgKGhvbGRpbmcucXVhbnRpdHkgPCBpbnZlc3RtZW50RGF0YS5pbnZlc3RtZW50X3F1YW50aXR5KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignQ2Fubm90IHNlbGwgbW9yZSB0aGFuIGN1cnJlbnQgaG9sZGluZycpXG4gICAgICB9XG5cbiAgICAgIC8vIENhbGN1bGF0ZSB0YXggaW1wbGljYXRpb25zXG4gICAgICB0YXhDYWxjdWxhdGlvbiA9IGF3YWl0IFRheENhbGN1bGF0b3JTZXJ2aWNlLmNhbGN1bGF0ZUNhcGl0YWxHYWluc1RheCh7XG4gICAgICAgIGFzc2V0X2NsYXNzX2lkOiBhc3NldC5hc3NldF9jbGFzc19pZCxcbiAgICAgICAgcHVyY2hhc2VfZGF0ZTogaG9sZGluZy5jcmVhdGVkX2F0LFxuICAgICAgICBzYWxlX2RhdGU6IGludmVzdG1lbnREYXRhLnRyYW5zYWN0aW9uX2RhdGUsXG4gICAgICAgIHB1cmNoYXNlX3ByaWNlOiBob2xkaW5nLmF2ZXJhZ2VfY29zdCxcbiAgICAgICAgc2FsZV9wcmljZTogaW52ZXN0bWVudERhdGEuaW52ZXN0bWVudF9wcmljZSxcbiAgICAgICAgcXVhbnRpdHk6IGludmVzdG1lbnREYXRhLmludmVzdG1lbnRfcXVhbnRpdHksXG4gICAgICAgIGZlZXM6IGludmVzdG1lbnREYXRhLmZlZXNcbiAgICAgIH0pXG5cbiAgICAgIC8vIENhbGN1bGF0ZSByZWFsaXplZCBnYWluL2xvc3NcbiAgICAgIHJlYWxpemVkR2Fpbkxvc3MgPSBhd2FpdCBQcm9maXRMb3NzU2VydmljZS5jYWxjdWxhdGVSZWFsaXplZEdhaW5Mb3NzKFxuICAgICAgICBpbnZlc3RtZW50RGF0YS5hc3NldF9pZCxcbiAgICAgICAgaW52ZXN0bWVudERhdGEuaW52ZXN0bWVudF9xdWFudGl0eSxcbiAgICAgICAgaW52ZXN0bWVudERhdGEuaW52ZXN0bWVudF9wcmljZSxcbiAgICAgICAgaW52ZXN0bWVudERhdGEudHJhbnNhY3Rpb25fZGF0ZSxcbiAgICAgICAgaG9sZGluZy5hdmVyYWdlX2Nvc3QsXG4gICAgICAgIGhvbGRpbmcuY3JlYXRlZF9hdFxuICAgICAgKVxuICAgIH1cblxuICAgIC8vIENyZWF0ZSB0aGUgaW52ZXN0bWVudCB0cmFuc2FjdGlvbiB1c2luZyB0aGUgZXhpc3RpbmcgbWV0aG9kXG4gICAgY29uc3QgdHJhbnNhY3Rpb24gPSBhd2FpdCB0aGlzLmNyZWF0ZUludmVzdG1lbnRUcmFuc2FjdGlvbihpbnZlc3RtZW50RGF0YSwgZnVuZGluZ0FjY291bnRJZClcblxuICAgIC8vIFVwZGF0ZSBob2xkaW5nc1xuICAgIGNvbnN0IGhvbGRpbmcgPSBhd2FpdCBIb2xkaW5nU2VydmljZS51cGRhdGVIb2xkaW5nKFxuICAgICAgaW52ZXN0bWVudERhdGEuYWNjb3VudF9pZCxcbiAgICAgIGludmVzdG1lbnREYXRhLmFzc2V0X2lkLFxuICAgICAgaW52ZXN0bWVudERhdGEudHJhbnNhY3Rpb25fdHlwZSA9PT0gJ2ludmVzdG1lbnRfYnV5JyA/ICdidXknIDogJ3NlbGwnLFxuICAgICAgaW52ZXN0bWVudERhdGEuaW52ZXN0bWVudF9xdWFudGl0eSxcbiAgICAgIGludmVzdG1lbnREYXRhLmludmVzdG1lbnRfcHJpY2VcbiAgICApXG5cbiAgICByZXR1cm4ge1xuICAgICAgdHJhbnNhY3Rpb24sXG4gICAgICBob2xkaW5nLFxuICAgICAgdGF4Q2FsY3VsYXRpb24sXG4gICAgICByZWFsaXplZEdhaW5Mb3NzXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBpbnZlc3RtZW50IHRyYW5zYWN0aW9ucyBmb3IgYSB1c2VyIG9yIHNwZWNpZmljIGFjY291bnRcbiAgICovXG4gIHN0YXRpYyBhc3luYyBnZXRJbnZlc3RtZW50VHJhbnNhY3Rpb25zKG9wdGlvbnM/OiB7XG4gICAgYWNjb3VudF9pZD86IHN0cmluZ1xuICAgIHN5bWJvbD86IHN0cmluZ1xuICAgIHRyYW5zYWN0aW9uX3R5cGU/OiAnaW52ZXN0bWVudF9idXknIHwgJ2ludmVzdG1lbnRfc2VsbCdcbiAgICBzdGFydERhdGU/OiBzdHJpbmdcbiAgICBlbmREYXRlPzogc3RyaW5nXG4gICAgbGltaXQ/OiBudW1iZXJcbiAgICBvZmZzZXQ/OiBudW1iZXJcbiAgfSk6IFByb21pc2U8eyBkYXRhOiBJSW52ZXN0bWVudFRyYW5zYWN0aW9uW107IGNvdW50OiBudW1iZXIgfT4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIGxldCBxdWVyeSA9IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndHJhbnNhY3Rpb25zJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopLFxuICAgICAgICBhY2NvdW50OmFjY291bnRzIXRyYW5zYWN0aW9uc19hY2NvdW50X2lkX2ZrZXkoKilcbiAgICAgIGAsIHsgY291bnQ6ICdleGFjdCcgfSlcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuaW4oJ3RyYW5zYWN0aW9uX3R5cGUnLCBbJ2ludmVzdG1lbnRfYnV5JywgJ2ludmVzdG1lbnRfc2VsbCddKVxuICAgICAgLm9yZGVyKCd0cmFuc2FjdGlvbl9kYXRlJywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG4gICAgICAub3JkZXIoJ2NyZWF0ZWRfYXQnLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcblxuICAgIGlmIChvcHRpb25zPy5hY2NvdW50X2lkKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdhY2NvdW50X2lkJywgb3B0aW9ucy5hY2NvdW50X2lkKVxuICAgIH1cblxuICAgIGlmIChvcHRpb25zPy5zeW1ib2wpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuZXEoJ2ludmVzdG1lbnRfc3ltYm9sJywgb3B0aW9ucy5zeW1ib2wpXG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnM/LnRyYW5zYWN0aW9uX3R5cGUpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuZXEoJ3RyYW5zYWN0aW9uX3R5cGUnLCBvcHRpb25zLnRyYW5zYWN0aW9uX3R5cGUpXG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnM/LnN0YXJ0RGF0ZSkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5ndGUoJ3RyYW5zYWN0aW9uX2RhdGUnLCBvcHRpb25zLnN0YXJ0RGF0ZSlcbiAgICB9XG5cbiAgICBpZiAob3B0aW9ucz8uZW5kRGF0ZSkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5sdGUoJ3RyYW5zYWN0aW9uX2RhdGUnLCBvcHRpb25zLmVuZERhdGUpXG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnM/LmxpbWl0KSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmxpbWl0KG9wdGlvbnMubGltaXQpXG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnM/Lm9mZnNldCkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5yYW5nZShvcHRpb25zLm9mZnNldCwgb3B0aW9ucy5vZmZzZXQgKyAob3B0aW9ucy5saW1pdCB8fCAyMCkgLSAxKVxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IsIGNvdW50IH0gPSBhd2FpdCBxdWVyeVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBpbnZlc3RtZW50IHRyYW5zYWN0aW9uczogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIHsgZGF0YTogZGF0YSBhcyBJSW52ZXN0bWVudFRyYW5zYWN0aW9uW10sIGNvdW50OiBjb3VudCB8fCAwIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgaW52ZXN0bWVudCBob2xkaW5ncyBmb3IgYSB1c2VyIG9yIHNwZWNpZmljIGFjY291bnRcbiAgICovXG4gIHN0YXRpYyBhc3luYyBnZXRJbnZlc3RtZW50SG9sZGluZ3MoYWNjb3VudElkPzogc3RyaW5nKTogUHJvbWlzZTxJSW52ZXN0bWVudEhvbGRpbmdbXT4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIGxldCBxdWVyeSA9IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnaW52ZXN0bWVudF9ob2xkaW5ncycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgYWNjb3VudDphY2NvdW50cygqKVxuICAgICAgYClcbiAgICAgIC5vcmRlcignc3ltYm9sJylcblxuICAgIGlmIChhY2NvdW50SWQpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuZXEoJ2FjY291bnRfaWQnLCBhY2NvdW50SWQpXG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIEZpbHRlciBieSB1c2VyJ3MgYWNjb3VudHNcbiAgICAgIGNvbnN0IHsgZGF0YTogdXNlckFjY291bnRzIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnYWNjb3VudHMnKVxuICAgICAgICAuc2VsZWN0KCdpZCcpXG4gICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAgIC5lcSgnYWNjb3VudF90eXBlJywgJ2ludmVzdG1lbnQnKVxuXG4gICAgICBpZiAodXNlckFjY291bnRzICYmIHVzZXJBY2NvdW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnN0IGFjY291bnRJZHMgPSB1c2VyQWNjb3VudHMubWFwKGFjYyA9PiBhY2MuaWQpXG4gICAgICAgIHF1ZXJ5ID0gcXVlcnkuaW4oJ2FjY291bnRfaWQnLCBhY2NvdW50SWRzKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuIFtdXG4gICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgcXVlcnlcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggaW52ZXN0bWVudCBob2xkaW5nczogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgSUludmVzdG1lbnRIb2xkaW5nW11cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgcG9ydGZvbGlvIHN1bW1hcnkgZm9yIGFsbCBpbnZlc3RtZW50IGFjY291bnRzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0UG9ydGZvbGlvU3VtbWFyeSgpOiBQcm9taXNlPHtcbiAgICB0b3RhbFZhbHVlOiBudW1iZXJcbiAgICB0b3RhbENvc3Q6IG51bWJlclxuICAgIHRvdGFsR2Fpbkxvc3M6IG51bWJlclxuICAgIHRvdGFsR2Fpbkxvc3NQZXJjZW50OiBudW1iZXJcbiAgICBob2xkaW5nc0J5U3ltYm9sOiBSZWNvcmQ8c3RyaW5nLCB7XG4gICAgICBzeW1ib2w6IHN0cmluZ1xuICAgICAgdG90YWxRdWFudGl0eTogbnVtYmVyXG4gICAgICBhdmVyYWdlQ29zdDogbnVtYmVyXG4gICAgICBjdXJyZW50UHJpY2U/OiBudW1iZXJcbiAgICAgIG1hcmtldFZhbHVlPzogbnVtYmVyXG4gICAgICBnYWluTG9zcz86IG51bWJlclxuICAgICAgZ2Fpbkxvc3NQZXJjZW50PzogbnVtYmVyXG4gICAgfT5cbiAgfT4ge1xuICAgIGNvbnN0IGhvbGRpbmdzID0gYXdhaXQgdGhpcy5nZXRJbnZlc3RtZW50SG9sZGluZ3MoKVxuXG4gICAgbGV0IHRvdGFsVmFsdWUgPSAwXG4gICAgbGV0IHRvdGFsQ29zdCA9IDBcbiAgICBjb25zdCBob2xkaW5nc0J5U3ltYm9sOiBSZWNvcmQ8c3RyaW5nLCBhbnk+ID0ge31cblxuICAgIGhvbGRpbmdzLmZvckVhY2goaG9sZGluZyA9PiB7XG4gICAgICBjb25zdCBzeW1ib2wgPSBob2xkaW5nLnN5bWJvbFxuICAgICAgY29uc3QgcXVhbnRpdHkgPSBob2xkaW5nLnF1YW50aXR5XG4gICAgICBjb25zdCBhdmdDb3N0ID0gaG9sZGluZy5hdmVyYWdlX2Nvc3RcbiAgICAgIGNvbnN0IGN1cnJlbnRQcmljZSA9IGhvbGRpbmcuY3VycmVudF9wcmljZSB8fCBhdmdDb3N0XG4gICAgICBjb25zdCBtYXJrZXRWYWx1ZSA9IHF1YW50aXR5ICogY3VycmVudFByaWNlXG4gICAgICBjb25zdCBjb3N0QmFzaXMgPSBxdWFudGl0eSAqIGF2Z0Nvc3RcbiAgICAgIGNvbnN0IGdhaW5Mb3NzID0gbWFya2V0VmFsdWUgLSBjb3N0QmFzaXNcbiAgICAgIGNvbnN0IGdhaW5Mb3NzUGVyY2VudCA9IGNvc3RCYXNpcyA+IDAgPyAoZ2Fpbkxvc3MgLyBjb3N0QmFzaXMpICogMTAwIDogMFxuXG4gICAgICBpZiAoIWhvbGRpbmdzQnlTeW1ib2xbc3ltYm9sXSkge1xuICAgICAgICBob2xkaW5nc0J5U3ltYm9sW3N5bWJvbF0gPSB7XG4gICAgICAgICAgc3ltYm9sLFxuICAgICAgICAgIHRvdGFsUXVhbnRpdHk6IDAsXG4gICAgICAgICAgYXZlcmFnZUNvc3Q6IDAsXG4gICAgICAgICAgY3VycmVudFByaWNlLFxuICAgICAgICAgIG1hcmtldFZhbHVlOiAwLFxuICAgICAgICAgIGdhaW5Mb3NzOiAwLFxuICAgICAgICAgIGdhaW5Mb3NzUGVyY2VudDogMCxcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBBZ2dyZWdhdGUgaG9sZGluZ3MgZm9yIHRoZSBzYW1lIHN5bWJvbCBhY3Jvc3MgYWNjb3VudHNcbiAgICAgIGNvbnN0IGV4aXN0aW5nID0gaG9sZGluZ3NCeVN5bWJvbFtzeW1ib2xdXG4gICAgICBjb25zdCBuZXdUb3RhbFF1YW50aXR5ID0gZXhpc3RpbmcudG90YWxRdWFudGl0eSArIHF1YW50aXR5XG4gICAgICBjb25zdCBuZXdUb3RhbENvc3QgPSAoZXhpc3RpbmcudG90YWxRdWFudGl0eSAqIGV4aXN0aW5nLmF2ZXJhZ2VDb3N0KSArIChxdWFudGl0eSAqIGF2Z0Nvc3QpXG4gICAgICBcbiAgICAgIGhvbGRpbmdzQnlTeW1ib2xbc3ltYm9sXSA9IHtcbiAgICAgICAgLi4uZXhpc3RpbmcsXG4gICAgICAgIHRvdGFsUXVhbnRpdHk6IG5ld1RvdGFsUXVhbnRpdHksXG4gICAgICAgIGF2ZXJhZ2VDb3N0OiBuZXdUb3RhbFF1YW50aXR5ID4gMCA/IG5ld1RvdGFsQ29zdCAvIG5ld1RvdGFsUXVhbnRpdHkgOiAwLFxuICAgICAgICBtYXJrZXRWYWx1ZTogZXhpc3RpbmcubWFya2V0VmFsdWUgKyBtYXJrZXRWYWx1ZSxcbiAgICAgICAgZ2Fpbkxvc3M6IGV4aXN0aW5nLmdhaW5Mb3NzICsgZ2Fpbkxvc3MsXG4gICAgICB9XG5cbiAgICAgIC8vIFJlY2FsY3VsYXRlIHBlcmNlbnRhZ2VcbiAgICAgIGNvbnN0IHRvdGFsQ29zdEJhc2lzID0gaG9sZGluZ3NCeVN5bWJvbFtzeW1ib2xdLnRvdGFsUXVhbnRpdHkgKiBob2xkaW5nc0J5U3ltYm9sW3N5bWJvbF0uYXZlcmFnZUNvc3RcbiAgICAgIGhvbGRpbmdzQnlTeW1ib2xbc3ltYm9sXS5nYWluTG9zc1BlcmNlbnQgPSB0b3RhbENvc3RCYXNpcyA+IDAgXG4gICAgICAgID8gKGhvbGRpbmdzQnlTeW1ib2xbc3ltYm9sXS5nYWluTG9zcyAvIHRvdGFsQ29zdEJhc2lzKSAqIDEwMCBcbiAgICAgICAgOiAwXG5cbiAgICAgIHRvdGFsVmFsdWUgKz0gbWFya2V0VmFsdWVcbiAgICAgIHRvdGFsQ29zdCArPSBjb3N0QmFzaXNcbiAgICB9KVxuXG4gICAgY29uc3QgdG90YWxHYWluTG9zcyA9IHRvdGFsVmFsdWUgLSB0b3RhbENvc3RcbiAgICBjb25zdCB0b3RhbEdhaW5Mb3NzUGVyY2VudCA9IHRvdGFsQ29zdCA+IDAgPyAodG90YWxHYWluTG9zcyAvIHRvdGFsQ29zdCkgKiAxMDAgOiAwXG5cbiAgICByZXR1cm4ge1xuICAgICAgdG90YWxWYWx1ZSxcbiAgICAgIHRvdGFsQ29zdCxcbiAgICAgIHRvdGFsR2Fpbkxvc3MsXG4gICAgICB0b3RhbEdhaW5Mb3NzUGVyY2VudCxcbiAgICAgIGhvbGRpbmdzQnlTeW1ib2wsXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZSBjdXJyZW50IHByaWNlcyBmb3IgaG9sZGluZ3MgKHdvdWxkIHR5cGljYWxseSBiZSBjYWxsZWQgYnkgYSBiYWNrZ3JvdW5kIGpvYilcbiAgICovXG4gIHN0YXRpYyBhc3luYyB1cGRhdGVIb2xkaW5nUHJpY2VzKHByaWNlVXBkYXRlczogeyBzeW1ib2w6IHN0cmluZzsgcHJpY2U6IG51bWJlciB9W10pOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9IH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKVxuICAgIFxuICAgIGlmICghdXNlcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJylcbiAgICB9XG5cbiAgICBmb3IgKGNvbnN0IHVwZGF0ZSBvZiBwcmljZVVwZGF0ZXMpIHtcbiAgICAgIGNvbnN0IG1hcmtldFZhbHVlID0gdXBkYXRlLnByaWNlIC8vIFdpbGwgYmUgY2FsY3VsYXRlZCBieSB0cmlnZ2VyXG4gICAgICBcbiAgICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKCdpbnZlc3RtZW50X2hvbGRpbmdzJylcbiAgICAgICAgLnVwZGF0ZSh7XG4gICAgICAgICAgY3VycmVudF9wcmljZTogdXBkYXRlLnByaWNlLFxuICAgICAgICAgIG1hcmtldF92YWx1ZTogbWFya2V0VmFsdWUsXG4gICAgICAgICAgbGFzdF91cGRhdGVkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIH0pXG4gICAgICAgIC5lcSgnc3ltYm9sJywgdXBkYXRlLnN5bWJvbClcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEZhaWxlZCB0byB1cGRhdGUgcHJpY2UgZm9yICR7dXBkYXRlLnN5bWJvbH06YCwgZXJyb3IpXG4gICAgICB9XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsic3VwYWJhc2UiLCJUYXhDYWxjdWxhdG9yU2VydmljZSIsIkhvbGRpbmdTZXJ2aWNlIiwiQXNzZXRTZXJ2aWNlIiwiUHJvZml0TG9zc1NlcnZpY2UiLCJJbnZlc3RtZW50U2VydmljZSIsImNyZWF0ZUludmVzdG1lbnRUcmFuc2FjdGlvbiIsImludmVzdG1lbnREYXRhIiwiZnVuZGluZ0FjY291bnRJZCIsImRhdGEiLCJ1c2VyIiwiYXV0aCIsImdldFVzZXIiLCJFcnJvciIsImludmVzdG1lbnRBY2NvdW50IiwiZnJvbSIsInNlbGVjdCIsImVxIiwiYWNjb3VudF9pZCIsImlkIiwic2luZ2xlIiwiYWNjb3VudF90eXBlIiwidHJhbnNhY3Rpb25fdHlwZSIsImZ1bmRpbmdBY2NvdW50IiwidG90YWxDb3N0IiwiYW1vdW50IiwiZmVlcyIsImN1cnJlbnRfYmFsYW5jZSIsImNhdGVnb3J5TmFtZSIsImNhdGVnb3J5IiwidHJhbnNhY3Rpb25EYXRhIiwiZGVzY3JpcHRpb24iLCJpbnZlc3RtZW50X3F1YW50aXR5IiwiaW52ZXN0bWVudF9zeW1ib2wiLCJjYXRlZ29yeV9pZCIsInRyYW5zYWN0aW9uX2RhdGUiLCJ0cmFuc2FjdGlvbl9zdGF0dXMiLCJpbnZlc3RtZW50X3ByaWNlIiwidXNlcl9pZCIsInRyYW5zYWN0aW9uIiwiZXJyb3IiLCJ0cmFuc2FjdGlvbkVycm9yIiwiaW5zZXJ0IiwibWVzc2FnZSIsIlRyYW5zZmVyU2VydmljZSIsImNyZWF0ZVRyYW5zZmVyIiwiZnJvbV9hY2NvdW50X2lkIiwidG9fYWNjb3VudF9pZCIsImRlbGV0ZSIsImNyZWF0ZUludmVzdG1lbnRUcmFuc2FjdGlvbldpdGhBc3NldHMiLCJhc3NldCIsImdldEFzc2V0IiwiYXNzZXRfaWQiLCJ0YXhDYWxjdWxhdGlvbiIsInJlYWxpemVkR2Fpbkxvc3MiLCJob2xkaW5nIiwiZ2V0SG9sZGluZyIsInF1YW50aXR5IiwiY2FsY3VsYXRlQ2FwaXRhbEdhaW5zVGF4IiwiYXNzZXRfY2xhc3NfaWQiLCJwdXJjaGFzZV9kYXRlIiwiY3JlYXRlZF9hdCIsInNhbGVfZGF0ZSIsInB1cmNoYXNlX3ByaWNlIiwiYXZlcmFnZV9jb3N0Iiwic2FsZV9wcmljZSIsImNhbGN1bGF0ZVJlYWxpemVkR2Fpbkxvc3MiLCJ1cGRhdGVIb2xkaW5nIiwiZ2V0SW52ZXN0bWVudFRyYW5zYWN0aW9ucyIsIm9wdGlvbnMiLCJxdWVyeSIsImNvdW50IiwiaW4iLCJvcmRlciIsImFzY2VuZGluZyIsInN5bWJvbCIsInN0YXJ0RGF0ZSIsImd0ZSIsImVuZERhdGUiLCJsdGUiLCJsaW1pdCIsIm9mZnNldCIsInJhbmdlIiwiZ2V0SW52ZXN0bWVudEhvbGRpbmdzIiwiYWNjb3VudElkIiwidXNlckFjY291bnRzIiwibGVuZ3RoIiwiYWNjb3VudElkcyIsIm1hcCIsImFjYyIsImdldFBvcnRmb2xpb1N1bW1hcnkiLCJob2xkaW5ncyIsInRvdGFsVmFsdWUiLCJob2xkaW5nc0J5U3ltYm9sIiwiZm9yRWFjaCIsImF2Z0Nvc3QiLCJjdXJyZW50UHJpY2UiLCJjdXJyZW50X3ByaWNlIiwibWFya2V0VmFsdWUiLCJjb3N0QmFzaXMiLCJnYWluTG9zcyIsImdhaW5Mb3NzUGVyY2VudCIsInRvdGFsUXVhbnRpdHkiLCJhdmVyYWdlQ29zdCIsImV4aXN0aW5nIiwibmV3VG90YWxRdWFudGl0eSIsIm5ld1RvdGFsQ29zdCIsInRvdGFsQ29zdEJhc2lzIiwidG90YWxHYWluTG9zcyIsInRvdGFsR2Fpbkxvc3NQZXJjZW50IiwidXBkYXRlSG9sZGluZ1ByaWNlcyIsInByaWNlVXBkYXRlcyIsInVwZGF0ZSIsInByaWNlIiwibWFya2V0X3ZhbHVlIiwibGFzdF91cGRhdGVkIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiY29uc29sZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/investments.ts\n"));

/***/ })

});