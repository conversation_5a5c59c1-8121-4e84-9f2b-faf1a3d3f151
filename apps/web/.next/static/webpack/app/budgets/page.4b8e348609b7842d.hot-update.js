"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/budgets/page",{

/***/ "(app-pages-browser)/../../packages/shared/src/types.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/types.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// Core types for the application\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL3R5cGVzLnRzIiwibWFwcGluZ3MiOiI7QUFBQSxpQ0FBaUM7QUE2T2hDIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL3BhY2thZ2VzL3NoYXJlZC9zcmMvdHlwZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29yZSB0eXBlcyBmb3IgdGhlIGFwcGxpY2F0aW9uXG5leHBvcnQgaW50ZXJmYWNlIElVc2VyIHtcbiAgaWQ6IHN0cmluZztcbiAgZW1haWw6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBhdmF0YXJfdXJsPzogc3RyaW5nO1xuICBjdXJyZW5jeV9wcmVmZXJlbmNlOiBzdHJpbmc7XG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIElVc2VyUHJvZmlsZSB7XG4gIGlkOiBzdHJpbmc7XG4gIHVzZXJfaWQ6IHN0cmluZztcbiAgZGlzcGxheV9uYW1lPzogc3RyaW5nO1xuICBhdmF0YXJfdXJsPzogc3RyaW5nO1xuICBjdXJyZW5jeV9wcmVmZXJlbmNlOiBzdHJpbmc7XG4gIGNvdW50cnk/OiBzdHJpbmc7XG4gIG5vdGlmaWNhdGlvbl9wcmVmZXJlbmNlczoge1xuICAgIGVtYWlsX25vdGlmaWNhdGlvbnM/OiBib29sZWFuO1xuICAgIHB1c2hfbm90aWZpY2F0aW9ucz86IGJvb2xlYW47XG4gICAgYnVkZ2V0X2FsZXJ0cz86IGJvb2xlYW47XG4gICAgd2Vla2x5X3N1bW1hcnk/OiBib29sZWFuO1xuICB9O1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIHVwZGF0ZWRfYXQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBJQ2F0ZWdvcnkge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGljb24/OiBzdHJpbmc7XG4gIGNvbG9yPzogc3RyaW5nO1xuICB0eXBlPzogJ2luY29tZScgfCAnZXhwZW5zZSc7XG4gIHVzZXJfaWQ/OiBzdHJpbmc7XG4gIGlzX2RlZmF1bHQ6IGJvb2xlYW47XG4gIGlzX3N5c3RlbTogYm9vbGVhbjsgLy8gU3lzdGVtIGNhdGVnb3JpZXMgY2Fubm90IGJlIGRlbGV0ZWRcbiAgaXNfYWN0aXZlOiBib29sZWFuO1xuICBwYXJlbnRfY2F0ZWdvcnlfaWQ/OiBzdHJpbmc7XG4gIHNvcnRfb3JkZXI6IG51bWJlcjtcbiAgY2F0ZWdvcnlfbWV0YWRhdGE/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIHVwZGF0ZWRfYXQ6IHN0cmluZztcbiAgLy8gUmVsYXRpb25zXG4gIHBhcmVudF9jYXRlZ29yeT86IElDYXRlZ29yeTtcbiAgc3ViY2F0ZWdvcmllcz86IElDYXRlZ29yeVtdO1xufVxuXG4vLyBBY2NvdW50IHR5cGVzXG5leHBvcnQgdHlwZSBBY2NvdW50VHlwZSA9ICdiYW5rJyB8ICdpbnZlc3RtZW50JyB8ICdzYXZpbmdzJyB8ICdjcmVkaXRfY2FyZCcgfCAnY2FzaCc7XG5leHBvcnQgdHlwZSBUcmFuc2FjdGlvblR5cGUgPSAnaW5jb21lJyB8ICdleHBlbnNlJyB8ICd0cmFuc2ZlcicgfCAnaW52ZXN0bWVudF9idXknIHwgJ2ludmVzdG1lbnRfc2VsbCcgfCAnZGl2aWRlbmQnO1xuZXhwb3J0IHR5cGUgVHJhbnNhY3Rpb25TdGF0dXMgPSAncGVuZGluZycgfCAnY29tcGxldGVkJyB8ICdjYW5jZWxsZWQnIHwgJ2ZhaWxlZCc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgSUFjY291bnQge1xuICBpZDogc3RyaW5nO1xuICB1c2VyX2lkOiBzdHJpbmcgfCBudWxsO1xuICBuYW1lOiBzdHJpbmc7XG4gIGFjY291bnRfdHlwZTogQWNjb3VudFR5cGU7XG4gIGFjY291bnRfbnVtYmVyPzogc3RyaW5nIHwgbnVsbDtcbiAgaW5zdGl0dXRpb25fbmFtZT86IHN0cmluZyB8IG51bGw7XG4gIGN1cnJlbmN5OiBzdHJpbmcgfCBudWxsO1xuICBjdXJyZW50X2JhbGFuY2U6IG51bWJlciB8IG51bGw7XG4gIGF2YWlsYWJsZV9iYWxhbmNlPzogbnVtYmVyIHwgbnVsbDsgLy8gRm9yIGNyZWRpdCBjYXJkc1xuICBjcmVkaXRfbGltaXQ/OiBudW1iZXIgfCBudWxsOyAvLyBGb3IgY3JlZGl0IGNhcmRzXG4gIGludGVyZXN0X3JhdGU/OiBudW1iZXIgfCBudWxsOyAvLyBGb3Igc2F2aW5ncy9pbnZlc3RtZW50IGFjY291bnRzXG4gIGlzX2FjdGl2ZTogYm9vbGVhbiB8IG51bGw7XG4gIGlzX3ByaW1hcnk6IGJvb2xlYW4gfCBudWxsO1xuICBhY2NvdW50X21ldGFkYXRhPzogUmVjb3JkPHN0cmluZywgYW55PiB8IG51bGw7XG4gIGNyZWF0ZWRfYXQ6IHN0cmluZyB8IG51bGw7XG4gIHVwZGF0ZWRfYXQ6IHN0cmluZyB8IG51bGw7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgSVRyYW5zYWN0aW9uIHtcbiAgaWQ6IHN0cmluZztcbiAgYW1vdW50OiBudW1iZXI7XG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xuICBjYXRlZ29yeV9pZD86IHN0cmluZztcbiAgYWNjb3VudF9pZD86IHN0cmluZztcbiAgdG9fYWNjb3VudF9pZD86IHN0cmluZzsgLy8gRm9yIHRyYW5zZmVyc1xuICB0cmFuc2Zlcl9pZD86IHN0cmluZzsgLy8gTGlua3MgcmVsYXRlZCB0cmFuc2ZlciB0cmFuc2FjdGlvbnNcbiAgdHJhbnNhY3Rpb25fdHlwZTogVHJhbnNhY3Rpb25UeXBlO1xuICB0cmFuc2FjdGlvbl9kYXRlOiBzdHJpbmc7XG4gIHRyYW5zYWN0aW9uX3N0YXR1czogVHJhbnNhY3Rpb25TdGF0dXM7XG4gIGZlZXM/OiBudW1iZXI7XG4gIGJhbGFuY2VfYWZ0ZXI/OiBudW1iZXI7XG4gIC8vIEludmVzdG1lbnQtc3BlY2lmaWMgZmllbGRzXG4gIGludmVzdG1lbnRfc3ltYm9sPzogc3RyaW5nO1xuICBpbnZlc3RtZW50X3F1YW50aXR5PzogbnVtYmVyO1xuICBpbnZlc3RtZW50X3ByaWNlPzogbnVtYmVyO1xuICAvLyBTdGFuZGFyZCBmaWVsZHNcbiAgdXNlcl9pZDogc3RyaW5nO1xuICByZWNlaXB0X3VybD86IHN0cmluZztcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xuICB1cGRhdGVkX2F0OiBzdHJpbmc7XG4gIC8vIFJlbGF0aW9uc1xuICBjYXRlZ29yeT86IElDYXRlZ29yeTtcbiAgYWNjb3VudD86IElBY2NvdW50O1xuICB0b19hY2NvdW50PzogSUFjY291bnQ7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgSVRyYW5zZmVyVHJhbnNhY3Rpb24ge1xuICBpZDogc3RyaW5nO1xuICBhbW91bnQ6IG51bWJlcjtcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gIGZyb21fYWNjb3VudF9pZDogc3RyaW5nO1xuICB0b19hY2NvdW50X2lkOiBzdHJpbmc7XG4gIHRyYW5zZmVyX2lkOiBzdHJpbmc7XG4gIHRyYW5zYWN0aW9uX2RhdGU6IHN0cmluZztcbiAgZmVlcz86IG51bWJlcjtcbiAgdXNlcl9pZDogc3RyaW5nO1xuICBmcm9tX2FjY291bnQ/OiBJQWNjb3VudDtcbiAgdG9fYWNjb3VudD86IElBY2NvdW50O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIElJbnZlc3RtZW50VHJhbnNhY3Rpb24ge1xuICBpZDogc3RyaW5nO1xuICBhbW91bnQ6IG51bWJlcjtcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gIGFjY291bnRfaWQ6IHN0cmluZztcbiAgaW52ZXN0bWVudF9zeW1ib2w6IHN0cmluZztcbiAgaW52ZXN0bWVudF9xdWFudGl0eTogbnVtYmVyO1xuICBpbnZlc3RtZW50X3ByaWNlOiBudW1iZXI7XG4gIHRyYW5zYWN0aW9uX3R5cGU6ICdpbnZlc3RtZW50X2J1eScgfCAnaW52ZXN0bWVudF9zZWxsJztcbiAgdHJhbnNhY3Rpb25fZGF0ZTogc3RyaW5nO1xuICBmZWVzPzogbnVtYmVyO1xuICB1c2VyX2lkOiBzdHJpbmc7XG4gIGFjY291bnQ/OiBJQWNjb3VudDtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBJQnVkZ2V0IHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBhbW91bnQ6IG51bWJlcjtcbiAgcGVyaW9kOiAnd2Vla2x5JyB8ICdtb250aGx5JyB8ICd5ZWFybHknO1xuICBjYXRlZ29yeV9pZD86IHN0cmluZztcbiAgdXNlcl9pZDogc3RyaW5nO1xuICBzdGFydF9kYXRlOiBzdHJpbmc7XG4gIGVuZF9kYXRlPzogc3RyaW5nO1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIHVwZGF0ZWRfYXQ6IHN0cmluZztcbiAgY2F0ZWdvcnk/OiBJQ2F0ZWdvcnk7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgSUFuYWx5dGljc0RhdGEge1xuICB0b3RhbEluY29tZTogbnVtYmVyO1xuICB0b3RhbEV4cGVuc2VzOiBudW1iZXI7XG4gIG5ldEluY29tZTogbnVtYmVyO1xuICBjYXRlZ29yeUJyZWFrZG93bjogQXJyYXk8e1xuICAgIGNhdGVnb3J5OiBJQ2F0ZWdvcnk7XG4gICAgdG90YWw6IG51bWJlcjtcbiAgICBwZXJjZW50YWdlOiBudW1iZXI7XG4gIH0+O1xuICBtb250aGx5VHJlbmRzOiBBcnJheTx7XG4gICAgbW9udGg6IHN0cmluZztcbiAgICBpbmNvbWU6IG51bWJlcjtcbiAgICBleHBlbnNlczogbnVtYmVyO1xuICAgIG5ldDogbnVtYmVyO1xuICB9PjtcbiAgdG9wQ2F0ZWdvcmllczogQXJyYXk8e1xuICAgIGNhdGVnb3J5OiBJQ2F0ZWdvcnk7XG4gICAgdG90YWw6IG51bWJlcjtcbiAgICB0cmFuc2FjdGlvbkNvdW50OiBudW1iZXI7XG4gIH0+O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIElJbnZlc3RtZW50SG9sZGluZyB7XG4gIGlkOiBzdHJpbmc7XG4gIGFjY291bnRfaWQ6IHN0cmluZztcbiAgc3ltYm9sOiBzdHJpbmc7XG4gIHF1YW50aXR5OiBudW1iZXI7XG4gIGF2ZXJhZ2VfY29zdDogbnVtYmVyO1xuICBjdXJyZW50X3ByaWNlPzogbnVtYmVyO1xuICBtYXJrZXRfdmFsdWU/OiBudW1iZXI7XG4gIHVucmVhbGl6ZWRfZ2Fpbl9sb3NzPzogbnVtYmVyO1xuICBsYXN0X3VwZGF0ZWQ6IHN0cmluZztcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xuICBhY2NvdW50PzogSUFjY291bnQ7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgSUFjY291bnRCYWxhbmNlSGlzdG9yeSB7XG4gIGlkOiBzdHJpbmc7XG4gIGFjY291bnRfaWQ6IHN0cmluZztcbiAgYmFsYW5jZTogbnVtYmVyO1xuICBiYWxhbmNlX2RhdGU6IHN0cmluZztcbiAgdHJhbnNhY3Rpb25faWQ/OiBzdHJpbmc7XG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcbiAgYWNjb3VudD86IElBY2NvdW50O1xuICB0cmFuc2FjdGlvbj86IElUcmFuc2FjdGlvbjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBJRGF0ZVJhbmdlIHtcbiAgc3RhcnREYXRlOiBzdHJpbmc7XG4gIGVuZERhdGU6IHN0cmluZztcbn1cblxuLy8gRm9ybSBpbnRlcmZhY2VzIGZvciBjcmVhdGluZy91cGRhdGluZyBlbnRpdGllc1xuZXhwb3J0IGludGVyZmFjZSBJQWNjb3VudEZvcm0ge1xuICBuYW1lOiBzdHJpbmc7XG4gIGFjY291bnRfdHlwZTogQWNjb3VudFR5cGU7XG4gIGFjY291bnRfbnVtYmVyPzogc3RyaW5nO1xuICBpbnN0aXR1dGlvbl9uYW1lPzogc3RyaW5nO1xuICBjdXJyZW5jeT86IHN0cmluZztcbiAgY3VycmVudF9iYWxhbmNlPzogbnVtYmVyO1xuICBhdmFpbGFibGVfYmFsYW5jZT86IG51bWJlcjtcbiAgY3JlZGl0X2xpbWl0PzogbnVtYmVyO1xuICBpbnRlcmVzdF9yYXRlPzogbnVtYmVyO1xuICBpc19wcmltYXJ5PzogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBJVHJhbnNmZXJGb3JtIHtcbiAgYW1vdW50OiBudW1iZXI7XG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xuICBmcm9tX2FjY291bnRfaWQ6IHN0cmluZztcbiAgdG9fYWNjb3VudF9pZDogc3RyaW5nO1xuICB0cmFuc2FjdGlvbl9kYXRlOiBzdHJpbmc7XG4gIGZlZXM/OiBudW1iZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgSUludmVzdG1lbnRGb3JtIHtcbiAgYW1vdW50OiBudW1iZXI7XG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xuICBhY2NvdW50X2lkOiBzdHJpbmc7XG4gIGludmVzdG1lbnRfc3ltYm9sOiBzdHJpbmc7XG4gIGludmVzdG1lbnRfcXVhbnRpdHk6IG51bWJlcjtcbiAgaW52ZXN0bWVudF9wcmljZTogbnVtYmVyO1xuICB0cmFuc2FjdGlvbl90eXBlOiAnaW52ZXN0bWVudF9idXknIHwgJ2ludmVzdG1lbnRfc2VsbCc7XG4gIHRyYW5zYWN0aW9uX2RhdGU6IHN0cmluZztcbiAgZmVlcz86IG51bWJlcjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBJQ2F0ZWdvcnlGb3JtIHtcbiAgbmFtZTogc3RyaW5nO1xuICBpY29uPzogc3RyaW5nO1xuICBjb2xvcj86IHN0cmluZztcbiAgdHlwZT86ICdpbmNvbWUnIHwgJ2V4cGVuc2UnO1xuICBwYXJlbnRfY2F0ZWdvcnlfaWQ/OiBzdHJpbmc7XG4gIHNvcnRfb3JkZXI/OiBudW1iZXI7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/types.ts\n"));

/***/ })

});