"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/budgets/page",{

/***/ "(app-pages-browser)/../../packages/shared/src/lib/transactions.ts":
/*!*****************************************************!*\
  !*** ../../packages/shared/src/lib/transactions.ts ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionService: () => (/* binding */ TransactionService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _transfers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\");\n/* harmony import */ var _investments__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./investments */ \"(app-pages-browser)/../../packages/shared/src/lib/investments.ts\");\n\n\n\nclass TransactionService {\n    /**\n   * Create a transaction of any type\n   */ static async createTransaction(data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Handle different transaction types\n        switch(data.transaction_type){\n            case 'transfer':\n                if (!data.account_id || !data.to_account_id) {\n                    throw new Error('Source and destination accounts are required for transfers');\n                }\n                const transfer = await _transfers__WEBPACK_IMPORTED_MODULE_1__.TransferService.createTransfer({\n                    amount: data.amount,\n                    description: data.description,\n                    from_account_id: data.account_id,\n                    to_account_id: data.to_account_id,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees\n                });\n                // Return the outgoing transaction as the primary transaction\n                const { data: transferTransactions } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n            *,\\n            category:categories(*),\\n            account:accounts(*),\\n            to_account:accounts!transactions_to_account_id_fkey(*)\\n          \").eq('transfer_id', transfer.transfer_id).eq('account_id', data.account_id).single();\n                return transferTransactions;\n            case 'investment_buy':\n            case 'investment_sell':\n                if (!data.account_id || !data.investment_symbol || !data.investment_quantity || !data.investment_price) {\n                    throw new Error('Investment account, symbol, quantity, and price are required for investment transactions');\n                }\n                const investment = await _investments__WEBPACK_IMPORTED_MODULE_2__.InvestmentService.createInvestmentTransaction({\n                    amount: data.amount,\n                    description: data.description,\n                    account_id: data.account_id,\n                    investment_symbol: data.investment_symbol,\n                    investment_quantity: data.investment_quantity,\n                    investment_price: data.investment_price,\n                    transaction_type: data.transaction_type,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees\n                }, data.funding_account_id);\n                return investment;\n            case 'dividend':\n                // Handle dividend as a transfer from investment account to bank account\n                if (!data.account_id || !data.to_account_id) {\n                    throw new Error('Investment account and receiving account are required for dividend transactions');\n                }\n                const { TransferService: TransferService1 } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\"));\n                const dividendTransfer = await TransferService1.createTransfer({\n                    amount: data.amount,\n                    description: data.description || \"Dividend payment: \".concat(data.investment_symbol || 'Investment'),\n                    from_account_id: data.account_id,\n                    to_account_id: data.to_account_id,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees || 0\n                });\n                return dividendTransfer;\n            case 'income':\n            case 'expense':\n                if (!data.category_id || !data.account_id) {\n                    throw new Error('Category and account are required for income/expense transactions');\n                }\n                const transactionData = {\n                    amount: data.amount,\n                    description: data.description || null,\n                    category_id: data.category_id,\n                    account_id: data.account_id,\n                    transaction_type: data.transaction_type,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    transaction_status: 'completed',\n                    fees: data.fees || 0,\n                    user_id: user.id\n                };\n                const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(\"\\n            *,\\n            category:categories(*),\\n            account:accounts(*)\\n          \").single();\n                if (error) {\n                    throw new Error(\"Failed to create transaction: \".concat(error.message));\n                }\n                return transaction;\n            default:\n                throw new Error(\"Unsupported transaction type: \".concat(data.transaction_type));\n        }\n    }\n    /**\n   * Get all transactions for the current user with enhanced filtering\n   */ static async getTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \", {\n            count: 'exact'\n        }).eq('user_id', user.id).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        // Apply filters\n        if (options === null || options === void 0 ? void 0 : options.categoryId) {\n            query = query.eq('category_id', options.categoryId);\n        }\n        if (options === null || options === void 0 ? void 0 : options.accountId) {\n            query = query.or(\"account_id.eq.\".concat(options.accountId, \",to_account_id.eq.\").concat(options.accountId));\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.transactionType) {\n            if (Array.isArray(options.transactionType)) {\n                query = query.in('transaction_type', options.transactionType);\n            } else {\n                query = query.eq('transaction_type', options.transactionType);\n            }\n        }\n        if (options === null || options === void 0 ? void 0 : options.transactionStatus) {\n            query = query.eq('transaction_status', options.transactionStatus);\n        }\n        // Filter out transfers and investments if not explicitly requested\n        if (!(options === null || options === void 0 ? void 0 : options.includeTransfers) && !(options === null || options === void 0 ? void 0 : options.includeInvestments)) {\n            query = query.in('transaction_type', [\n                'income',\n                'expense',\n                'dividend'\n            ]);\n        } else if (!(options === null || options === void 0 ? void 0 : options.includeTransfers)) {\n            query = query.neq('transaction_type', 'transfer');\n        } else if (!(options === null || options === void 0 ? void 0 : options.includeInvestments)) {\n            query = query.not('transaction_type', 'in', '(investment_buy,investment_sell)');\n        }\n        if (options === null || options === void 0 ? void 0 : options.searchQuery) {\n            // Search in description, category name, and investment symbol\n            query = query.or(\"description.ilike.%\".concat(options.searchQuery, \"%,\") + \"investment_symbol.ilike.%\".concat(options.searchQuery, \"%\"));\n        }\n        // Apply pagination\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch transactions: \".concat(error.message));\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    /**\n   * Get a specific transaction by ID\n   */ static async getTransaction(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            throw new Error(\"Failed to fetch transaction: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update a transaction (limited to basic transactions, not transfers or investments)\n   */ static async updateTransaction(id, updates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // First check if this is a basic transaction (not transfer or investment)\n        const { data: existingTransaction } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, transfer_id').eq('id', id).eq('user_id', user.id).single();\n        if (!existingTransaction) {\n            throw new Error('Transaction not found');\n        }\n        if (existingTransaction.transaction_type === 'transfer' || existingTransaction.transaction_type === 'investment_buy' || existingTransaction.transaction_type === 'investment_sell') {\n            throw new Error('Cannot update transfer or investment transactions through this method');\n        }\n        const updateData = {\n            updated_at: new Date().toISOString()\n        };\n        if (updates.amount !== undefined) updateData.amount = updates.amount;\n        if (updates.description !== undefined) updateData.description = updates.description;\n        if (updates.category_id !== undefined) updateData.category_id = updates.category_id;\n        if (updates.fees !== undefined) updateData.fees = updates.fees;\n        if (updates.transaction_date !== undefined) {\n            updateData.transaction_date = updates.transaction_date.toISOString().split('T')[0];\n        }\n        const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').update(updateData).eq('id', id).eq('user_id', user.id).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to update transaction: \".concat(error.message));\n        }\n        return transaction;\n    }\n    /**\n   * Delete a transaction\n   */ static async deleteTransaction(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if this is a transfer transaction\n        const { data: transaction } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, transfer_id').eq('id', id).eq('user_id', user.id).single();\n        if (!transaction) {\n            throw new Error('Transaction not found');\n        }\n        if (transaction.transaction_type === 'transfer' && transaction.transfer_id) {\n            // Delete all transactions with the same transfer_id\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('transfer_id', transaction.transfer_id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete transfer: \".concat(error.message));\n            }\n        } else {\n            // Delete single transaction\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete transaction: \".concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get transaction summary for a date range\n   */ static async getTransactionSummary(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, amount').eq('user_id', user.id).eq('transaction_status', 'completed');\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.accountId) {\n            query = query.or(\"account_id.eq.\".concat(options.accountId, \",to_account_id.eq.\").concat(options.accountId));\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch transaction summary: \".concat(error.message));\n        }\n        const summary = {\n            totalIncome: 0,\n            totalExpenses: 0,\n            totalTransfers: 0,\n            totalInvestments: 0,\n            netFlow: 0,\n            transactionCount: (data === null || data === void 0 ? void 0 : data.length) || 0\n        };\n        data === null || data === void 0 ? void 0 : data.forEach((transaction)=>{\n            switch(transaction.transaction_type){\n                case 'income':\n                case 'dividend':\n                    summary.totalIncome += transaction.amount;\n                    break;\n                case 'expense':\n                    summary.totalExpenses += transaction.amount;\n                    break;\n                case 'transfer':\n                    summary.totalTransfers += transaction.amount;\n                    break;\n                case 'investment_buy':\n                case 'investment_sell':\n                    summary.totalInvestments += transaction.amount;\n                    break;\n            }\n        });\n        summary.netFlow = summary.totalIncome - summary.totalExpenses;\n        return summary;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/transactions.ts\n"));

/***/ })

});