"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/budgets/page",{

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/native.js":
/*!**********************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/native.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({ randomUUID });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL25hdGl2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxpRUFBZSxFQUFFLFlBQVksRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL25hdGl2ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCByYW5kb21VVUlEID0gdHlwZW9mIGNyeXB0byAhPT0gJ3VuZGVmaW5lZCcgJiYgY3J5cHRvLnJhbmRvbVVVSUQgJiYgY3J5cHRvLnJhbmRvbVVVSUQuYmluZChjcnlwdG8pO1xuZXhwb3J0IGRlZmF1bHQgeyByYW5kb21VVUlEIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/native.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/regex.js":
/*!*********************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/regex.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3JlZ2V4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjLEVBQUUsVUFBVSxFQUFFLGVBQWUsRUFBRSxnQkFBZ0IsRUFBRSxVQUFVLEdBQUcsOEVBQThFLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tYnJvd3Nlci9yZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAvXig/OlswLTlhLWZdezh9LVswLTlhLWZdezR9LVsxLThdWzAtOWEtZl17M30tWzg5YWJdWzAtOWEtZl17M30tWzAtOWEtZl17MTJ9fDAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMHxmZmZmZmZmZi1mZmZmLWZmZmYtZmZmZi1mZmZmZmZmZmZmZmYpJC9pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/regex.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/rng.js":
/*!*******************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/rng.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rng)\n/* harmony export */ });\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nfunction rng() {\n    if (!getRandomValues) {\n        if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\n            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n        }\n        getRandomValues = crypto.getRandomValues.bind(crypto);\n    }\n    return getRandomValues(rnds8);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3JuZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3JuZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgZ2V0UmFuZG9tVmFsdWVzO1xuY29uc3Qgcm5kczggPSBuZXcgVWludDhBcnJheSgxNik7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBybmcoKSB7XG4gICAgaWYgKCFnZXRSYW5kb21WYWx1ZXMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBjcnlwdG8gPT09ICd1bmRlZmluZWQnIHx8ICFjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2NyeXB0by5nZXRSYW5kb21WYWx1ZXMoKSBub3Qgc3VwcG9ydGVkLiBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3V1aWRqcy91dWlkI2dldHJhbmRvbXZhbHVlcy1ub3Qtc3VwcG9ydGVkJyk7XG4gICAgICAgIH1cbiAgICAgICAgZ2V0UmFuZG9tVmFsdWVzID0gY3J5cHRvLmdldFJhbmRvbVZhbHVlcy5iaW5kKGNyeXB0byk7XG4gICAgfVxuICAgIHJldHVybiBnZXRSYW5kb21WYWx1ZXMocm5kczgpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/rng.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/stringify.js":
/*!*************************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/stringify.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   unsafeStringify: () => (/* binding */ unsafeStringify)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/validate.js\");\n\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nfunction unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stringify);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/stringify.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/v4.js":
/*!******************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/v4.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _native_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./native.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/native.js\");\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rng.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stringify.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/stringify.js\");\n\n\n\nfunction v4(options, buf, offset) {\n    if (_native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID && !buf && !options) {\n        return _native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? (0,_rng_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.unsafeStringify)(rnds);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v4);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3Y0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUM7QUFDTjtBQUNzQjtBQUNqRDtBQUNBLFFBQVEsa0RBQU07QUFDZCxlQUFlLGtEQUFNO0FBQ3JCO0FBQ0E7QUFDQSxzREFBc0QsbURBQUc7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9EQUFvRCxPQUFPLEdBQUcsYUFBYTtBQUMzRTtBQUNBLHdCQUF3QixRQUFRO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyw4REFBZTtBQUMxQjtBQUNBLGlFQUFlLEVBQUUsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3Y0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBuYXRpdmUgZnJvbSAnLi9uYXRpdmUuanMnO1xuaW1wb3J0IHJuZyBmcm9tICcuL3JuZy5qcyc7XG5pbXBvcnQgeyB1bnNhZmVTdHJpbmdpZnkgfSBmcm9tICcuL3N0cmluZ2lmeS5qcyc7XG5mdW5jdGlvbiB2NChvcHRpb25zLCBidWYsIG9mZnNldCkge1xuICAgIGlmIChuYXRpdmUucmFuZG9tVVVJRCAmJiAhYnVmICYmICFvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiBuYXRpdmUucmFuZG9tVVVJRCgpO1xuICAgIH1cbiAgICBvcHRpb25zID0gb3B0aW9ucyB8fCB7fTtcbiAgICBjb25zdCBybmRzID0gb3B0aW9ucy5yYW5kb20gPz8gb3B0aW9ucy5ybmc/LigpID8/IHJuZygpO1xuICAgIGlmIChybmRzLmxlbmd0aCA8IDE2KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignUmFuZG9tIGJ5dGVzIGxlbmd0aCBtdXN0IGJlID49IDE2Jyk7XG4gICAgfVxuICAgIHJuZHNbNl0gPSAocm5kc1s2XSAmIDB4MGYpIHwgMHg0MDtcbiAgICBybmRzWzhdID0gKHJuZHNbOF0gJiAweDNmKSB8IDB4ODA7XG4gICAgaWYgKGJ1Zikge1xuICAgICAgICBvZmZzZXQgPSBvZmZzZXQgfHwgMDtcbiAgICAgICAgaWYgKG9mZnNldCA8IDAgfHwgb2Zmc2V0ICsgMTYgPiBidWYubGVuZ3RoKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgUmFuZ2VFcnJvcihgVVVJRCBieXRlIHJhbmdlICR7b2Zmc2V0fToke29mZnNldCArIDE1fSBpcyBvdXQgb2YgYnVmZmVyIGJvdW5kc2ApO1xuICAgICAgICB9XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgMTY7ICsraSkge1xuICAgICAgICAgICAgYnVmW29mZnNldCArIGldID0gcm5kc1tpXTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYnVmO1xuICAgIH1cbiAgICByZXR1cm4gdW5zYWZlU3RyaW5naWZ5KHJuZHMpO1xufVxuZXhwb3J0IGRlZmF1bHQgdjQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/v4.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/validate.js":
/*!************************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/validate.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/regex.js\");\n\nfunction validate(uuid) {\n    return typeof uuid === 'string' && _regex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].test(uuid);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validate);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3ZhbGlkYXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStCO0FBQy9CO0FBQ0EsdUNBQXVDLGlEQUFLO0FBQzVDO0FBQ0EsaUVBQWUsUUFBUSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdmFsaWRhdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJFR0VYIGZyb20gJy4vcmVnZXguanMnO1xuZnVuY3Rpb24gdmFsaWRhdGUodXVpZCkge1xuICAgIHJldHVybiB0eXBlb2YgdXVpZCA9PT0gJ3N0cmluZycgJiYgUkVHRVgudGVzdCh1dWlkKTtcbn1cbmV4cG9ydCBkZWZhdWx0IHZhbGlkYXRlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/validate.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/index.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/index.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccountService: () => (/* reexport safe */ _lib_accounts__WEBPACK_IMPORTED_MODULE_9__.AccountService),\n/* harmony export */   AnalyticsService: () => (/* reexport safe */ _lib_analytics__WEBPACK_IMPORTED_MODULE_7__.AnalyticsService),\n/* harmony export */   BiometricService: () => (/* reexport safe */ _lib_biometric_web__WEBPACK_IMPORTED_MODULE_13__.BiometricService),\n/* harmony export */   BudgetService: () => (/* reexport safe */ _lib_budget__WEBPACK_IMPORTED_MODULE_6__.BudgetService),\n/* harmony export */   CategoryService: () => (/* reexport safe */ _lib_categories__WEBPACK_IMPORTED_MODULE_12__.CategoryService),\n/* harmony export */   Constants: () => (/* reexport safe */ _database_types__WEBPACK_IMPORTED_MODULE_3__.Constants),\n/* harmony export */   ExpenseService: () => (/* reexport safe */ _lib_expenses__WEBPACK_IMPORTED_MODULE_5__.ExpenseService),\n/* harmony export */   InvestmentService: () => (/* reexport safe */ _lib_investments__WEBPACK_IMPORTED_MODULE_11__.InvestmentService),\n/* harmony export */   RecurringTransactionService: () => (/* reexport safe */ _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__.RecurringTransactionService),\n/* harmony export */   TransferService: () => (/* reexport safe */ _lib_transfers__WEBPACK_IMPORTED_MODULE_10__.TransferService),\n/* harmony export */   budgetFormInputSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_16__.budgetFormInputSchema),\n/* harmony export */   budgetFormSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_16__.budgetFormSchema),\n/* harmony export */   budgetSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_16__.budgetSchema),\n/* harmony export */   calculateBudgetProgress: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.calculateBudgetProgress),\n/* harmony export */   categorySchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.categorySchema),\n/* harmony export */   expenseFormInputSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_15__.expenseFormInputSchema),\n/* harmony export */   expenseFormSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_15__.expenseFormSchema),\n/* harmony export */   expenseSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_15__.expenseSchema),\n/* harmony export */   formatCurrency: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency),\n/* harmony export */   formatDate: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatDate),\n/* harmony export */   resetPasswordSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_14__.resetPasswordSchema),\n/* harmony export */   signInSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_14__.signInSchema),\n/* harmony export */   signUpSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_14__.signUpSchema),\n/* harmony export */   supabase: () => (/* reexport safe */ _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase),\n/* harmony export */   supabaseMobile: () => (/* reexport safe */ _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_18__.supabase),\n/* harmony export */   transactionSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.transactionSchema),\n/* harmony export */   useCurrencyStore: () => (/* reexport safe */ _stores_currencyStore__WEBPACK_IMPORTED_MODULE_17__.useCurrencyStore)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/../../packages/shared/src/types.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/../../packages/shared/src/utils.ts\");\n/* harmony import */ var _validators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./validators */ \"(app-pages-browser)/../../packages/shared/src/validators.ts\");\n/* harmony import */ var _database_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./database.types */ \"(app-pages-browser)/../../packages/shared/src/database.types.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _lib_expenses__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/expenses */ \"(app-pages-browser)/../../packages/shared/src/lib/expenses.ts\");\n/* harmony import */ var _lib_budget__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/budget */ \"(app-pages-browser)/../../packages/shared/src/lib/budget.ts\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/analytics */ \"(app-pages-browser)/../../packages/shared/src/lib/analytics.ts\");\n/* harmony import */ var _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/recurring-transactions */ \"(app-pages-browser)/../../packages/shared/src/lib/recurring-transactions.ts\");\n/* harmony import */ var _lib_accounts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/accounts */ \"(app-pages-browser)/../../packages/shared/src/lib/accounts.ts\");\n/* harmony import */ var _lib_transfers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./lib/transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\");\n/* harmony import */ var _lib_investments__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./lib/investments */ \"(app-pages-browser)/../../packages/shared/src/lib/investments.ts\");\n/* harmony import */ var _lib_categories__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./lib/categories */ \"(app-pages-browser)/../../packages/shared/src/lib/categories.ts\");\n/* harmony import */ var _lib_biometric_web__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./lib/biometric.web */ \"(app-pages-browser)/../../packages/shared/src/lib/biometric.web.ts\");\n/* harmony import */ var _schemas_auth__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./schemas/auth */ \"(app-pages-browser)/../../packages/shared/src/schemas/auth.ts\");\n/* harmony import */ var _schemas_expense__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./schemas/expense */ \"(app-pages-browser)/../../packages/shared/src/schemas/expense.ts\");\n/* harmony import */ var _schemas_budget__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./schemas/budget */ \"(app-pages-browser)/../../packages/shared/src/schemas/budget.ts\");\n/* harmony import */ var _stores_currencyStore__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./stores/currencyStore */ \"(app-pages-browser)/../../packages/shared/src/stores/currencyStore.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./lib/supabase.mobile */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.mobile.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(_lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_18__);\n// Shared business logic and utilities\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Platform-specific biometric exports\n\n\n\n\n\n// Platform-specific exports (explicit re-export to avoid naming conflicts)\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsc0NBQXNDO0FBQ2Q7QUFDQTtBQUNLO0FBQ0k7QUFDRjtBQUNBO0FBQ0Y7QUFDRztBQUNhO0FBQ2Q7QUFDQztBQUNFO0FBQ0Q7QUFDakMsc0NBQXNDO0FBQ0Y7QUFDTDtBQUNHO0FBQ0Q7QUFDTTtBQUV2QywyRUFBMkU7QUFDUiIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9wYWNrYWdlcy9zaGFyZWQvc3JjL2luZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNoYXJlZCBidXNpbmVzcyBsb2dpYyBhbmQgdXRpbGl0aWVzXG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJztcbmV4cG9ydCAqIGZyb20gJy4vdXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi92YWxpZGF0b3JzJztcbmV4cG9ydCAqIGZyb20gJy4vZGF0YWJhc2UudHlwZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvc3VwYWJhc2UnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvZXhwZW5zZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvYnVkZ2V0JztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2FuYWx5dGljcyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9yZWN1cnJpbmctdHJhbnNhY3Rpb25zJztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2FjY291bnRzJztcbmV4cG9ydCAqIGZyb20gJy4vbGliL3RyYW5zZmVycyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9pbnZlc3RtZW50cyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9jYXRlZ29yaWVzJztcbi8vIFBsYXRmb3JtLXNwZWNpZmljIGJpb21ldHJpYyBleHBvcnRzXG5leHBvcnQgKiBmcm9tICcuL2xpYi9iaW9tZXRyaWMud2ViJztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy9hdXRoJztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy9leHBlbnNlJztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy9idWRnZXQnO1xuZXhwb3J0ICogZnJvbSAnLi9zdG9yZXMvY3VycmVuY3lTdG9yZSc7XG5cbi8vIFBsYXRmb3JtLXNwZWNpZmljIGV4cG9ydHMgKGV4cGxpY2l0IHJlLWV4cG9ydCB0byBhdm9pZCBuYW1pbmcgY29uZmxpY3RzKVxuZXhwb3J0IHsgc3VwYWJhc2UgYXMgc3VwYWJhc2VNb2JpbGUgfSBmcm9tICcuL2xpYi9zdXBhYmFzZS5tb2JpbGUnOyJdLCJuYW1lcyI6WyJzdXBhYmFzZSIsInN1cGFiYXNlTW9iaWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/accounts.ts":
/*!*************************************************!*\
  !*** ../../packages/shared/src/lib/accounts.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccountService: () => (/* binding */ AccountService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n\nclass AccountService {\n    /**\n   * Get all accounts for the current user\n   */ static async getAccounts(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('user_id', user.id).order('is_primary', {\n            ascending: false\n        }).order('account_type').order('name');\n        if (options === null || options === void 0 ? void 0 : options.account_type) {\n            query = query.eq('account_type', options.account_type);\n        }\n        if ((options === null || options === void 0 ? void 0 : options.is_active) !== undefined) {\n            query = query.eq('is_active', options.is_active);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch accounts: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get a specific account by ID\n   */ static async getAccount(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            throw new Error(\"Failed to fetch account: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Create a new account\n   */ static async createAccount(accountData) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if account name already exists for this user\n        const { data: existingAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('id').eq('user_id', user.id).eq('name', accountData.name).single();\n        if (existingAccount) {\n            throw new Error('Account name already exists');\n        }\n        // If this is set as primary, unset other primary accounts of the same type\n        if (accountData.is_primary) {\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').update({\n                is_primary: false\n            }).eq('user_id', user.id).eq('account_type', accountData.account_type);\n        }\n        const insertData = {\n            ...accountData,\n            user_id: user.id,\n            currency: accountData.currency || 'USD',\n            current_balance: accountData.current_balance || 0,\n            is_active: true,\n            is_primary: accountData.is_primary || false\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').insert(insertData).select('*').single();\n        if (error) {\n            throw new Error(\"Failed to create account: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update an existing account\n   */ static async updateAccount(id, updates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // If updating to primary, unset other primary accounts of the same type\n        if (updates.is_primary) {\n            const account = await this.getAccount(id);\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').update({\n                is_primary: false\n            }).eq('user_id', user.id).eq('account_type', account.account_type).neq('id', id);\n        }\n        const updateData = {\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').update(updateData).eq('id', id).eq('user_id', user.id).select('*').single();\n        if (error) {\n            throw new Error(\"Failed to update account: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Delete an account (soft delete by setting is_active to false)\n   */ static async deleteAccount(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if account has transactions\n        const { data: transactions } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('id').or(\"account_id.eq.\".concat(id, \",to_account_id.eq.\").concat(id)).limit(1);\n        if (transactions && transactions.length > 0) {\n            // Soft delete if account has transactions\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').update({\n                is_active: false,\n                updated_at: new Date().toISOString()\n            }).eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to deactivate account: \".concat(error.message));\n            }\n        } else {\n            // Hard delete if no transactions\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').delete().eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete account: \".concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get account balance history\n   */ static async getAccountBalanceHistory(accountId, options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('account_balance_history').select(\"\\n        *,\\n        transaction:transactions(*)\\n      \").eq('account_id', accountId).order('balance_date', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('balance_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('balance_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch balance history: \".concat(error.message));\n        }\n        return data || [];\n    }\n    /**\n   * Create default accounts for a new user\n   */ static async createDefaultAccounts() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const defaultAccounts = [\n            {\n                name: 'Cash',\n                account_type: 'cash',\n                currency: 'USD',\n                current_balance: 0,\n                is_primary: true\n            }\n        ];\n        const createdAccounts = [];\n        for (const accountData of defaultAccounts){\n            try {\n                const account = await this.createAccount(accountData);\n                createdAccounts.push(account);\n            } catch (error) {\n                console.error('Failed to create default account:', error);\n            }\n        }\n        return createdAccounts;\n    }\n    /**\n   * Get account summary with balances by type\n   */ static async getAccountSummary() {\n        const accounts = await this.getAccounts({\n            is_active: true\n        });\n        let totalAssets = 0;\n        let totalLiabilities = 0;\n        const accountsByType = {\n            bank: {\n                count: 0,\n                balance: 0\n            },\n            investment: {\n                count: 0,\n                balance: 0\n            },\n            savings: {\n                count: 0,\n                balance: 0\n            },\n            credit_card: {\n                count: 0,\n                balance: 0\n            },\n            cash: {\n                count: 0,\n                balance: 0\n            }\n        };\n        accounts.forEach((account)=>{\n            const balance = account.current_balance || 0;\n            accountsByType[account.account_type].count++;\n            accountsByType[account.account_type].balance += balance;\n            if (account.account_type === 'credit_card') {\n                // Credit card balances are liabilities (negative is debt)\n                totalLiabilities += Math.abs(balance);\n            } else {\n                totalAssets += balance;\n            }\n        });\n        return {\n            totalAssets,\n            totalLiabilities,\n            netWorth: totalAssets - totalLiabilities,\n            accountsByType\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/accounts.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/categories.ts":
/*!***************************************************!*\
  !*** ../../packages/shared/src/lib/categories.ts ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoryService: () => (/* binding */ CategoryService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n\nclass CategoryService {\n    /**\n   * Get all categories for the current user (including system categories)\n   */ static async getCategories(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select(\"\\n        *,\\n        parent_category:categories!categories_parent_category_id_fkey(*),\\n        subcategories:categories!categories_parent_category_id_fkey(*)\\n      \").or(\"user_id.eq.\".concat(user.id, \",and(is_system.eq.true,user_id.is.null)\")).order('sort_order').order('name');\n        if (options === null || options === void 0 ? void 0 : options.type) {\n            query = query.eq('type', options.type);\n        }\n        if ((options === null || options === void 0 ? void 0 : options.is_active) !== undefined) {\n            query = query.eq('is_active', options.is_active);\n        }\n        if ((options === null || options === void 0 ? void 0 : options.include_system) === false) {\n            query = query.eq('is_system', false);\n        }\n        if ((options === null || options === void 0 ? void 0 : options.parent_id) !== undefined) {\n            if (options.parent_id === null) {\n                query = query.is('parent_category_id', null);\n            } else {\n                query = query.eq('parent_category_id', options.parent_id);\n            }\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch categories: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get a specific category by ID\n   */ static async getCategory(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select(\"\\n        *,\\n        parent_category:categories!categories_parent_category_id_fkey(*),\\n        subcategories:categories!categories_parent_category_id_fkey(*)\\n      \").eq('id', id).or(\"user_id.eq.\".concat(user.id, \",and(is_system.eq.true,user_id.is.null)\")).single();\n        if (error) {\n            throw new Error(\"Failed to fetch category: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Create a new category\n   */ static async createCategory(categoryData) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if category name already exists for this user\n        const { data: existingCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('user_id', user.id).eq('name', categoryData.name).single();\n        if (existingCategory) {\n            throw new Error('Category name already exists');\n        }\n        // Validate parent category if specified\n        if (categoryData.parent_category_id) {\n            const { data: parentCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id, user_id, is_system').eq('id', categoryData.parent_category_id).single();\n            if (!parentCategory || parentCategory.user_id !== user.id && !parentCategory.is_system) {\n                throw new Error('Invalid parent category specified');\n            }\n        }\n        const insertData = {\n            ...categoryData,\n            user_id: user.id,\n            is_default: false,\n            is_system: false,\n            is_active: true,\n            sort_order: categoryData.sort_order || 0\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').insert(insertData).select(\"\\n        *,\\n        parent_category:categories!categories_parent_category_id_fkey(*),\\n        subcategories:categories!categories_parent_category_id_fkey(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to create category: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update an existing category\n   */ static async updateCategory(id, updates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if category exists and belongs to user (can't update system categories)\n        const { data: existingCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('*').eq('id', id).eq('user_id', user.id).eq('is_system', false).single();\n        if (!existingCategory) {\n            throw new Error('Category not found or cannot be modified');\n        }\n        // Check for name conflicts if name is being updated\n        if (updates.name && updates.name !== existingCategory.name) {\n            const { data: nameConflict } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('user_id', user.id).eq('name', updates.name).neq('id', id).single();\n            if (nameConflict) {\n                throw new Error('Category name already exists');\n            }\n        }\n        // Validate parent category if being updated\n        if (updates.parent_category_id) {\n            const { data: parentCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id, user_id, is_system').eq('id', updates.parent_category_id).single();\n            if (!parentCategory || parentCategory.user_id !== user.id && !parentCategory.is_system) {\n                throw new Error('Invalid parent category specified');\n            }\n            // Prevent circular references\n            if (updates.parent_category_id === id) {\n                throw new Error('Category cannot be its own parent');\n            }\n        }\n        const updateData = {\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').update(updateData).eq('id', id).eq('user_id', user.id).select(\"\\n        *,\\n        parent_category:categories!categories_parent_category_id_fkey(*),\\n        subcategories:categories!categories_parent_category_id_fkey(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to update category: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Delete a category (soft delete by setting is_active to false)\n   */ static async deleteCategory(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if category exists and belongs to user (can't delete system categories)\n        const { data: category } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('*').eq('id', id).eq('user_id', user.id).eq('is_system', false).single();\n        if (!category) {\n            throw new Error('Category not found or cannot be deleted');\n        }\n        // Check if category has transactions\n        const { data: transactions } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('id').eq('category_id', id).limit(1);\n        // Check if category has subcategories\n        const { data: subcategories } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('parent_category_id', id).eq('is_active', true).limit(1);\n        if (transactions && transactions.length > 0) {\n            // Soft delete if category has transactions\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').update({\n                is_active: false,\n                updated_at: new Date().toISOString()\n            }).eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to deactivate category: \".concat(error.message));\n            }\n        } else if (subcategories && subcategories.length > 0) {\n            throw new Error('Cannot delete category with active subcategories');\n        } else {\n            // Hard delete if no transactions or subcategories\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').delete().eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete category: \".concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get category hierarchy (parent categories with their subcategories)\n   */ static async getCategoryHierarchy(type) {\n        const categories = await this.getCategories({\n            type,\n            is_active: true,\n            include_system: true\n        });\n        // Build hierarchy\n        const categoryMap = new Map();\n        const rootCategories = [];\n        // First pass: create map and identify root categories\n        categories.forEach((category)=>{\n            categoryMap.set(category.id, {\n                ...category,\n                subcategories: []\n            });\n            if (!category.parent_category_id) {\n                rootCategories.push(categoryMap.get(category.id));\n            }\n        });\n        // Second pass: build parent-child relationships\n        categories.forEach((category)=>{\n            if (category.parent_category_id) {\n                const parent = categoryMap.get(category.parent_category_id);\n                const child = categoryMap.get(category.id);\n                if (parent && child) {\n                    parent.subcategories = parent.subcategories || [];\n                    parent.subcategories.push(child);\n                }\n            }\n        });\n        return rootCategories;\n    }\n    /**\n   * Get category usage statistics\n   */ static async getCategoryStats(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        category_id,\\n        amount,\\n        category:categories(*)\\n      \").eq('user_id', user.id).not('category_id', 'is', null);\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch category statistics: \".concat(error.message));\n        }\n        // Group by category and calculate stats\n        const statsMap = new Map();\n        data === null || data === void 0 ? void 0 : data.forEach((transaction)=>{\n            if (transaction.category) {\n                const categoryId = transaction.category_id;\n                const existing = statsMap.get(categoryId) || {\n                    category: transaction.category,\n                    transactionCount: 0,\n                    totalAmount: 0\n                };\n                existing.transactionCount++;\n                existing.totalAmount += transaction.amount;\n                statsMap.set(categoryId, existing);\n            }\n        });\n        // Convert to array and add average\n        return Array.from(statsMap.values()).map((stat)=>({\n                ...stat,\n                averageAmount: stat.transactionCount > 0 ? stat.totalAmount / stat.transactionCount : 0\n            })).sort((a, b)=>b.totalAmount - a.totalAmount);\n    }\n    /**\n   * Reorder categories\n   */ static async reorderCategories(categoryOrders) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Update sort orders in batch\n        for (const { id, sort_order } of categoryOrders){\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').update({\n                sort_order,\n                updated_at: new Date().toISOString()\n            }).eq('id', id).eq('user_id', user.id).eq('is_system', false) // Only allow reordering user categories\n            ;\n            if (error) {\n                console.error(\"Failed to update sort order for category \".concat(id, \":\"), error);\n            }\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/categories.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/investments.ts":
/*!****************************************************!*\
  !*** ../../packages/shared/src/lib/investments.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvestmentService: () => (/* binding */ InvestmentService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n\nclass InvestmentService {\n    /**\n   * Create an investment transaction (buy/sell)\n   * This also creates a transfer from a funding account for buy transactions\n   */ static async createInvestmentTransaction(investmentData, fundingAccountId// Required for buy transactions\n    ) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Validate investment account exists and is an investment account\n        const { data: investmentAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', investmentData.account_id).eq('user_id', user.id).single();\n        if (!investmentAccount || investmentAccount.account_type !== 'investment') {\n            throw new Error('Invalid investment account specified');\n        }\n        // For buy transactions, validate funding account and check balance\n        if (investmentData.transaction_type === 'investment_buy') {\n            if (!fundingAccountId) {\n                throw new Error('Funding account required for investment purchases');\n            }\n            const { data: fundingAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', fundingAccountId).eq('user_id', user.id).single();\n            if (!fundingAccount) {\n                throw new Error('Invalid funding account specified');\n            }\n            const totalCost = investmentData.amount + (investmentData.fees || 0);\n            if (fundingAccount.current_balance < totalCost) {\n                throw new Error('Insufficient balance in funding account');\n            }\n        }\n        // Get investment category\n        const categoryName = investmentData.transaction_type === 'investment_buy' ? 'Investment Purchase' : 'Investment Sale';\n        const { data: category } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', categoryName).eq('is_system', true).single();\n        if (!category) {\n            throw new Error(\"\".concat(categoryName, \" category not found\"));\n        }\n        // Create the investment transaction\n        const transactionData = {\n            amount: investmentData.amount,\n            description: investmentData.description || \"\".concat(investmentData.transaction_type === 'investment_buy' ? 'Buy' : 'Sell', \" \").concat(investmentData.investment_quantity, \" shares of \").concat(investmentData.investment_symbol),\n            category_id: category.id,\n            account_id: investmentData.account_id,\n            transaction_type: investmentData.transaction_type,\n            transaction_date: investmentData.transaction_date,\n            transaction_status: 'completed',\n            fees: investmentData.fees || 0,\n            investment_symbol: investmentData.investment_symbol,\n            investment_quantity: investmentData.investment_quantity,\n            investment_price: investmentData.investment_price,\n            user_id: user.id\n        };\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \").single();\n        if (transactionError) {\n            throw new Error(\"Failed to create investment transaction: \".concat(transactionError.message));\n        }\n        // For buy transactions, create a transfer from funding account to investment account\n        if (investmentData.transaction_type === 'investment_buy' && fundingAccountId) {\n            const { TransferService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\"));\n            try {\n                await TransferService.createTransfer({\n                    amount: investmentData.amount + (investmentData.fees || 0),\n                    description: \"Investment purchase: \".concat(investmentData.investment_symbol),\n                    from_account_id: fundingAccountId,\n                    to_account_id: investmentData.account_id,\n                    transaction_date: investmentData.transaction_date,\n                    fees: 0\n                });\n            } catch (error) {\n                // If transfer fails, rollback the investment transaction\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', transaction.id);\n                throw new Error(\"Failed to create funding transfer: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n            }\n        }\n        return transaction;\n    }\n    /**\n   * Get investment transactions for a user or specific account\n   */ static async getInvestmentTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \", {\n            count: 'exact'\n        }).eq('user_id', user.id).in('transaction_type', [\n            'investment_buy',\n            'investment_sell'\n        ]).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            query = query.eq('account_id', options.account_id);\n        }\n        if (options === null || options === void 0 ? void 0 : options.symbol) {\n            query = query.eq('investment_symbol', options.symbol);\n        }\n        if (options === null || options === void 0 ? void 0 : options.transaction_type) {\n            query = query.eq('transaction_type', options.transaction_type);\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 20) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch investment transactions: \".concat(error.message));\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    /**\n   * Get investment holdings for a user or specific account\n   */ static async getInvestmentHoldings(accountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_holdings').select(\"\\n        *,\\n        account:accounts(*)\\n      \").order('symbol');\n        if (accountId) {\n            query = query.eq('account_id', accountId);\n        } else {\n            // Filter by user's accounts\n            const { data: userAccounts } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('id').eq('user_id', user.id).eq('account_type', 'investment');\n            if (userAccounts && userAccounts.length > 0) {\n                const accountIds = userAccounts.map((acc)=>acc.id);\n                query = query.in('account_id', accountIds);\n            } else {\n                return [];\n            }\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch investment holdings: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get portfolio summary for all investment accounts\n   */ static async getPortfolioSummary() {\n        const holdings = await this.getInvestmentHoldings();\n        let totalValue = 0;\n        let totalCost = 0;\n        const holdingsBySymbol = {};\n        holdings.forEach((holding)=>{\n            const symbol = holding.symbol;\n            const quantity = holding.quantity;\n            const avgCost = holding.average_cost;\n            const currentPrice = holding.current_price || avgCost;\n            const marketValue = quantity * currentPrice;\n            const costBasis = quantity * avgCost;\n            const gainLoss = marketValue - costBasis;\n            const gainLossPercent = costBasis > 0 ? gainLoss / costBasis * 100 : 0;\n            if (!holdingsBySymbol[symbol]) {\n                holdingsBySymbol[symbol] = {\n                    symbol,\n                    totalQuantity: 0,\n                    averageCost: 0,\n                    currentPrice,\n                    marketValue: 0,\n                    gainLoss: 0,\n                    gainLossPercent: 0\n                };\n            }\n            // Aggregate holdings for the same symbol across accounts\n            const existing = holdingsBySymbol[symbol];\n            const newTotalQuantity = existing.totalQuantity + quantity;\n            const newTotalCost = existing.totalQuantity * existing.averageCost + quantity * avgCost;\n            holdingsBySymbol[symbol] = {\n                ...existing,\n                totalQuantity: newTotalQuantity,\n                averageCost: newTotalQuantity > 0 ? newTotalCost / newTotalQuantity : 0,\n                marketValue: existing.marketValue + marketValue,\n                gainLoss: existing.gainLoss + gainLoss\n            };\n            // Recalculate percentage\n            const totalCostBasis = holdingsBySymbol[symbol].totalQuantity * holdingsBySymbol[symbol].averageCost;\n            holdingsBySymbol[symbol].gainLossPercent = totalCostBasis > 0 ? holdingsBySymbol[symbol].gainLoss / totalCostBasis * 100 : 0;\n            totalValue += marketValue;\n            totalCost += costBasis;\n        });\n        const totalGainLoss = totalValue - totalCost;\n        const totalGainLossPercent = totalCost > 0 ? totalGainLoss / totalCost * 100 : 0;\n        return {\n            totalValue,\n            totalCost,\n            totalGainLoss,\n            totalGainLossPercent,\n            holdingsBySymbol\n        };\n    }\n    /**\n   * Update current prices for holdings (would typically be called by a background job)\n   */ static async updateHoldingPrices(priceUpdates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        for (const update of priceUpdates){\n            const marketValue = update.price // Will be calculated by trigger\n            ;\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_holdings').update({\n                current_price: update.price,\n                market_value: marketValue,\n                last_updated: new Date().toISOString()\n            }).eq('symbol', update.symbol);\n            if (error) {\n                console.error(\"Failed to update price for \".concat(update.symbol, \":\"), error);\n            }\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/investments.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/transfers.ts":
/*!**************************************************!*\
  !*** ../../packages/shared/src/lib/transfers.ts ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransferService: () => (/* binding */ TransferService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/v4.js\");\n\n\nclass TransferService {\n    /**\n   * Create a transfer between two accounts\n   * This creates two linked transactions: one debit and one credit\n   */ static async createTransfer(transferData) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Validate accounts exist and belong to user\n        const { data: fromAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', transferData.from_account_id).eq('user_id', user.id).single();\n        const { data: toAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', transferData.to_account_id).eq('user_id', user.id).single();\n        if (!fromAccount || !toAccount) {\n            throw new Error('Invalid account(s) specified');\n        }\n        if (fromAccount.id === toAccount.id) {\n            throw new Error('Cannot transfer to the same account');\n        }\n        // Check if source account has sufficient balance (except for credit cards)\n        if (fromAccount.account_type !== 'credit_card' && fromAccount.current_balance < transferData.amount) {\n            throw new Error('Insufficient balance in source account');\n        }\n        // Generate a unique transfer ID to link the transactions\n        const transferId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        // Get system transfer categories\n        const { data: transferOutCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', 'Transfer Out').eq('is_system', true).single();\n        const { data: transferInCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', 'Transfer In').eq('is_system', true).single();\n        if (!transferOutCategory || !transferInCategory) {\n            throw new Error('Transfer categories not found');\n        }\n        // Create the outgoing transaction (debit from source account)\n        const outgoingTransaction = {\n            amount: transferData.amount,\n            description: transferData.description || \"Transfer to \".concat(toAccount.name),\n            category_id: transferOutCategory.id,\n            account_id: transferData.from_account_id,\n            to_account_id: transferData.to_account_id,\n            transfer_id: transferId,\n            transaction_type: 'transfer',\n            transaction_date: transferData.transaction_date,\n            transaction_status: 'completed',\n            fees: transferData.fees || 0,\n            user_id: user.id\n        };\n        // Create the incoming transaction (credit to destination account)\n        const incomingTransaction = {\n            amount: transferData.amount,\n            description: transferData.description || \"Transfer from \".concat(fromAccount.name),\n            category_id: transferInCategory.id,\n            account_id: transferData.to_account_id,\n            to_account_id: transferData.from_account_id,\n            transfer_id: transferId,\n            transaction_type: 'transfer',\n            transaction_date: transferData.transaction_date,\n            transaction_status: 'completed',\n            fees: 0,\n            user_id: user.id\n        };\n        // Insert both transactions in a transaction\n        const { data: outgoingData, error: outgoingError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(outgoingTransaction).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").single();\n        if (outgoingError) {\n            throw new Error(\"Failed to create outgoing transaction: \".concat(outgoingError.message));\n        }\n        const { data: incomingData, error: incomingError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(incomingTransaction).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").single();\n        if (incomingError) {\n            // If incoming transaction fails, we should rollback the outgoing transaction\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', outgoingData.id);\n            throw new Error(\"Failed to create incoming transaction: \".concat(incomingError.message));\n        }\n        // Return a combined transfer object\n        return {\n            id: transferId,\n            amount: transferData.amount,\n            description: transferData.description,\n            from_account_id: transferData.from_account_id,\n            to_account_id: transferData.to_account_id,\n            transfer_id: transferId,\n            transaction_date: transferData.transaction_date,\n            fees: transferData.fees,\n            user_id: user.id,\n            from_account: fromAccount,\n            to_account: toAccount\n        };\n    }\n    /**\n   * Get all transfers for the current user\n   */ static async getTransfers(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Get unique transfer IDs first\n        let transferQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transfer_id, transaction_date', {\n            count: 'exact'\n        }).eq('user_id', user.id).eq('transaction_type', 'transfer').not('transfer_id', 'is', null).order('transaction_date', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            transferQuery = transferQuery.or(\"account_id.eq.\".concat(options.account_id, \",to_account_id.eq.\").concat(options.account_id));\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            transferQuery = transferQuery.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            transferQuery = transferQuery.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            transferQuery = transferQuery.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            transferQuery = transferQuery.range(options.offset, options.offset + (options.limit || 20) - 1);\n        }\n        const { data: transferIds, error: transferError, count } = await transferQuery;\n        if (transferError) {\n            throw new Error(\"Failed to fetch transfers: \".concat(transferError.message));\n        }\n        if (!transferIds || transferIds.length === 0) {\n            return {\n                data: [],\n                count: count || 0\n            };\n        }\n        // Get the actual transfer transactions\n        const uniqueTransferIds = [\n            ...new Set(transferIds.map((t)=>t.transfer_id))\n        ];\n        const { data: transactions, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").in('transfer_id', uniqueTransferIds).eq('user_id', user.id).eq('transaction_type', 'transfer');\n        if (transactionError) {\n            throw new Error(\"Failed to fetch transfer transactions: \".concat(transactionError.message));\n        }\n        // Group transactions by transfer_id and create transfer objects\n        const transferMap = new Map();\n        transactions === null || transactions === void 0 ? void 0 : transactions.forEach((transaction)=>{\n            if (transaction.transfer_id) {\n                if (!transferMap.has(transaction.transfer_id)) {\n                    transferMap.set(transaction.transfer_id, []);\n                }\n                transferMap.get(transaction.transfer_id).push(transaction);\n            }\n        });\n        const transfers = [];\n        transferMap.forEach((transactionPair, transferId)=>{\n            if (transactionPair.length === 2) {\n                // Find the outgoing transaction (the one with fees or the one from the source account)\n                const outgoing = transactionPair.find((t)=>t.fees && t.fees > 0) || transactionPair[0];\n                const incoming = transactionPair.find((t)=>t.id !== outgoing.id);\n                transfers.push({\n                    id: transferId,\n                    amount: outgoing.amount,\n                    description: outgoing.description,\n                    from_account_id: outgoing.account_id,\n                    to_account_id: outgoing.to_account_id,\n                    transfer_id: transferId,\n                    transaction_date: outgoing.transaction_date,\n                    fees: outgoing.fees,\n                    user_id: user.id,\n                    from_account: outgoing.account,\n                    to_account: outgoing.to_account\n                });\n            }\n        });\n        // Sort by transaction date\n        transfers.sort((a, b)=>new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime());\n        return {\n            data: transfers,\n            count: count || 0\n        };\n    }\n    /**\n   * Get a specific transfer by transfer ID\n   */ static async getTransfer(transferId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data: transactions, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").eq('transfer_id', transferId).eq('user_id', user.id).eq('transaction_type', 'transfer');\n        if (error) {\n            throw new Error(\"Failed to fetch transfer: \".concat(error.message));\n        }\n        if (!transactions || transactions.length !== 2) {\n            throw new Error('Transfer not found or incomplete');\n        }\n        // Find the outgoing transaction\n        const outgoing = transactions.find((t)=>t.fees && t.fees > 0) || transactions[0];\n        return {\n            id: transferId,\n            amount: outgoing.amount,\n            description: outgoing.description,\n            from_account_id: outgoing.account_id,\n            to_account_id: outgoing.to_account_id,\n            transfer_id: transferId,\n            transaction_date: outgoing.transaction_date,\n            fees: outgoing.fees,\n            user_id: user.id,\n            from_account: outgoing.account,\n            to_account: outgoing.to_account\n        };\n    }\n    /**\n   * Cancel a transfer (mark both transactions as cancelled)\n   */ static async cancelTransfer(transferId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').update({\n            transaction_status: 'cancelled',\n            updated_at: new Date().toISOString()\n        }).eq('transfer_id', transferId).eq('user_id', user.id).eq('transaction_type', 'transfer');\n        if (error) {\n            throw new Error(\"Failed to cancel transfer: \".concat(error.message));\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\n"));

/***/ })

});