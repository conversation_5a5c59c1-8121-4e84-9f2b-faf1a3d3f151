"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/../../packages/shared/src/index.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/index.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccountService: () => (/* reexport safe */ _lib_accounts__WEBPACK_IMPORTED_MODULE_9__.AccountService),\n/* harmony export */   AnalyticsService: () => (/* reexport safe */ _lib_analytics__WEBPACK_IMPORTED_MODULE_7__.AnalyticsService),\n/* harmony export */   BiometricService: () => (/* reexport safe */ _lib_biometric_web__WEBPACK_IMPORTED_MODULE_13__.BiometricService),\n/* harmony export */   BudgetService: () => (/* reexport safe */ _lib_budget__WEBPACK_IMPORTED_MODULE_6__.BudgetService),\n/* harmony export */   CategoryService: () => (/* reexport safe */ _lib_categories__WEBPACK_IMPORTED_MODULE_12__.CategoryService),\n/* harmony export */   Constants: () => (/* reexport safe */ _database_types__WEBPACK_IMPORTED_MODULE_3__.Constants),\n/* harmony export */   ExpenseService: () => (/* reexport safe */ _lib_expenses__WEBPACK_IMPORTED_MODULE_5__.ExpenseService),\n/* harmony export */   InvestmentService: () => (/* reexport safe */ _lib_investments__WEBPACK_IMPORTED_MODULE_11__.InvestmentService),\n/* harmony export */   RecurringTransactionService: () => (/* reexport safe */ _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__.RecurringTransactionService),\n/* harmony export */   TransferService: () => (/* reexport safe */ _lib_transfers__WEBPACK_IMPORTED_MODULE_10__.TransferService),\n/* harmony export */   accountSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.accountSchema),\n/* harmony export */   budgetFormInputSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_16__.budgetFormInputSchema),\n/* harmony export */   budgetFormSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_16__.budgetFormSchema),\n/* harmony export */   budgetSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_16__.budgetSchema),\n/* harmony export */   calculateBudgetProgress: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.calculateBudgetProgress),\n/* harmony export */   categorySchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.categorySchema),\n/* harmony export */   expenseFormInputSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_15__.expenseFormInputSchema),\n/* harmony export */   expenseFormSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_15__.expenseFormSchema),\n/* harmony export */   expenseSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_15__.expenseSchema),\n/* harmony export */   formatCurrency: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency),\n/* harmony export */   formatDate: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatDate),\n/* harmony export */   investmentSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.investmentSchema),\n/* harmony export */   resetPasswordSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_14__.resetPasswordSchema),\n/* harmony export */   signInSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_14__.signInSchema),\n/* harmony export */   signUpSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_14__.signUpSchema),\n/* harmony export */   supabase: () => (/* reexport safe */ _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase),\n/* harmony export */   supabaseMobile: () => (/* reexport safe */ _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_18__.supabase),\n/* harmony export */   transactionSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.transactionSchema),\n/* harmony export */   transferSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.transferSchema),\n/* harmony export */   useCurrencyStore: () => (/* reexport safe */ _stores_currencyStore__WEBPACK_IMPORTED_MODULE_17__.useCurrencyStore)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/../../packages/shared/src/types.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/../../packages/shared/src/utils.ts\");\n/* harmony import */ var _validators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./validators */ \"(app-pages-browser)/../../packages/shared/src/validators.ts\");\n/* harmony import */ var _database_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./database.types */ \"(app-pages-browser)/../../packages/shared/src/database.types.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _lib_expenses__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/expenses */ \"(app-pages-browser)/../../packages/shared/src/lib/expenses.ts\");\n/* harmony import */ var _lib_budget__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/budget */ \"(app-pages-browser)/../../packages/shared/src/lib/budget.ts\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/analytics */ \"(app-pages-browser)/../../packages/shared/src/lib/analytics.ts\");\n/* harmony import */ var _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/recurring-transactions */ \"(app-pages-browser)/../../packages/shared/src/lib/recurring-transactions.ts\");\n/* harmony import */ var _lib_accounts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/accounts */ \"(app-pages-browser)/../../packages/shared/src/lib/accounts.ts\");\n/* harmony import */ var _lib_transfers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./lib/transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\");\n/* harmony import */ var _lib_investments__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./lib/investments */ \"(app-pages-browser)/../../packages/shared/src/lib/investments.ts\");\n/* harmony import */ var _lib_categories__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./lib/categories */ \"(app-pages-browser)/../../packages/shared/src/lib/categories.ts\");\n/* harmony import */ var _lib_biometric_web__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./lib/biometric.web */ \"(app-pages-browser)/../../packages/shared/src/lib/biometric.web.ts\");\n/* harmony import */ var _schemas_auth__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./schemas/auth */ \"(app-pages-browser)/../../packages/shared/src/schemas/auth.ts\");\n/* harmony import */ var _schemas_expense__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./schemas/expense */ \"(app-pages-browser)/../../packages/shared/src/schemas/expense.ts\");\n/* harmony import */ var _schemas_budget__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./schemas/budget */ \"(app-pages-browser)/../../packages/shared/src/schemas/budget.ts\");\n/* harmony import */ var _stores_currencyStore__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./stores/currencyStore */ \"(app-pages-browser)/../../packages/shared/src/stores/currencyStore.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./lib/supabase.mobile */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.mobile.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(_lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_18__);\n// Shared business logic and utilities\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Platform-specific biometric exports\n\n\n\n\n\n// Platform-specific exports (explicit re-export to avoid naming conflicts)\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsc0NBQXNDO0FBQ2Q7QUFDQTtBQUNLO0FBQ0k7QUFDRjtBQUNBO0FBQ0Y7QUFDRztBQUNhO0FBQ2Q7QUFDQztBQUNFO0FBQ0Q7QUFDakMsc0NBQXNDO0FBQ0Y7QUFDTDtBQUNHO0FBQ0Q7QUFDTTtBQUV2QywyRUFBMkU7QUFDUiIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9wYWNrYWdlcy9zaGFyZWQvc3JjL2luZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNoYXJlZCBidXNpbmVzcyBsb2dpYyBhbmQgdXRpbGl0aWVzXG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJztcbmV4cG9ydCAqIGZyb20gJy4vdXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi92YWxpZGF0b3JzJztcbmV4cG9ydCAqIGZyb20gJy4vZGF0YWJhc2UudHlwZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvc3VwYWJhc2UnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvZXhwZW5zZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvYnVkZ2V0JztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2FuYWx5dGljcyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9yZWN1cnJpbmctdHJhbnNhY3Rpb25zJztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2FjY291bnRzJztcbmV4cG9ydCAqIGZyb20gJy4vbGliL3RyYW5zZmVycyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9pbnZlc3RtZW50cyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9jYXRlZ29yaWVzJztcbi8vIFBsYXRmb3JtLXNwZWNpZmljIGJpb21ldHJpYyBleHBvcnRzXG5leHBvcnQgKiBmcm9tICcuL2xpYi9iaW9tZXRyaWMud2ViJztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy9hdXRoJztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy9leHBlbnNlJztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy9idWRnZXQnO1xuZXhwb3J0ICogZnJvbSAnLi9zdG9yZXMvY3VycmVuY3lTdG9yZSc7XG5cbi8vIFBsYXRmb3JtLXNwZWNpZmljIGV4cG9ydHMgKGV4cGxpY2l0IHJlLWV4cG9ydCB0byBhdm9pZCBuYW1pbmcgY29uZmxpY3RzKVxuZXhwb3J0IHsgc3VwYWJhc2UgYXMgc3VwYWJhc2VNb2JpbGUgfSBmcm9tICcuL2xpYi9zdXBhYmFzZS5tb2JpbGUnOyJdLCJuYW1lcyI6WyJzdXBhYmFzZSIsInN1cGFiYXNlTW9iaWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/validators.ts":
/*!***********************************************!*\
  !*** ../../packages/shared/src/validators.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accountSchema: () => (/* binding */ accountSchema),\n/* harmony export */   categorySchema: () => (/* binding */ categorySchema),\n/* harmony export */   investmentSchema: () => (/* binding */ investmentSchema),\n/* harmony export */   transactionSchema: () => (/* binding */ transactionSchema),\n/* harmony export */   transferSchema: () => (/* binding */ transferSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/../../node_modules/zod/dist/esm/index.js\");\n\n// Validation schemas using Zod\nconst transactionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Amount must be positive'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    category_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Invalid category ID').optional(),\n    account_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Invalid account ID').optional(),\n    transaction_type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'income',\n        'expense',\n        'transfer',\n        'investment_buy',\n        'investment_sell',\n        'dividend'\n    ]),\n    transaction_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    fees: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0, 'Fees cannot be negative').optional()\n});\nconst categorySchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Category name is required'),\n    icon: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    color: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format').optional(),\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'income',\n        'expense'\n    ]).optional(),\n    parent_category_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Invalid parent category ID').optional(),\n    sort_order: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int().min(0).optional()\n});\nconst accountSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Account name is required'),\n    account_type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'bank',\n        'investment',\n        'savings',\n        'credit_card',\n        'cash'\n    ]),\n    account_number: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    institution_name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    currency: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().length(3, 'Currency must be 3 characters').optional(),\n    current_balance: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n    available_balance: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n    credit_limit: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0, 'Credit limit cannot be negative').optional(),\n    interest_rate: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0).max(100, 'Interest rate must be between 0 and 100').optional(),\n    is_primary: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional()\n});\nconst transferSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Amount must be positive'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    from_account_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Invalid source account ID'),\n    to_account_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Invalid destination account ID'),\n    transaction_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    fees: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0, 'Fees cannot be negative').optional()\n}).refine((data)=>data.from_account_id !== data.to_account_id, {\n    message: 'Source and destination accounts must be different',\n    path: [\n        'to_account_id'\n    ]\n});\nconst investmentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Amount must be positive'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    account_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Invalid account ID'),\n    investment_symbol: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Investment symbol is required').max(10, 'Symbol too long'),\n    investment_quantity: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Quantity must be positive'),\n    investment_price: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Price must be positive'),\n    transaction_type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'investment_buy',\n        'investment_sell'\n    ]),\n    transaction_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    fees: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0, 'Fees cannot be negative').optional()\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/validators.ts\n"));

/***/ })

});