"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/../../packages/shared/src/utils.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/utils.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateBudgetProgress: () => (/* binding */ calculateBudgetProgress),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate)\n/* harmony export */ });\n// Shared utility functions\nconst formatCurrency = (amount, currency)=>{\n    // If no currency is provided, we should use the currency from the store\n    // This function is kept for backward compatibility but components should use useCurrencyStore\n    const currencyCode = currency || 'USD' // Fallback to USD only if no currency provided\n    ;\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: currencyCode\n    }).format(amount);\n};\nconst formatDate = (date)=>{\n    const d = typeof date === 'string' ? new Date(date) : date;\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\nconst calculateBudgetProgress = (spent, budget)=>{\n    return Math.min(spent / budget * 100, 100);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/utils.ts\n"));

/***/ })

});