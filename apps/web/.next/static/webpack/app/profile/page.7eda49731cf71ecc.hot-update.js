"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/native.js":
/*!**********************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/native.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({ randomUUID });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL25hdGl2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxpRUFBZSxFQUFFLFlBQVksRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL25hdGl2ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCByYW5kb21VVUlEID0gdHlwZW9mIGNyeXB0byAhPT0gJ3VuZGVmaW5lZCcgJiYgY3J5cHRvLnJhbmRvbVVVSUQgJiYgY3J5cHRvLnJhbmRvbVVVSUQuYmluZChjcnlwdG8pO1xuZXhwb3J0IGRlZmF1bHQgeyByYW5kb21VVUlEIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/native.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/regex.js":
/*!*********************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/regex.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3JlZ2V4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjLEVBQUUsVUFBVSxFQUFFLGVBQWUsRUFBRSxnQkFBZ0IsRUFBRSxVQUFVLEdBQUcsOEVBQThFLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tYnJvd3Nlci9yZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAvXig/OlswLTlhLWZdezh9LVswLTlhLWZdezR9LVsxLThdWzAtOWEtZl17M30tWzg5YWJdWzAtOWEtZl17M30tWzAtOWEtZl17MTJ9fDAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMHxmZmZmZmZmZi1mZmZmLWZmZmYtZmZmZi1mZmZmZmZmZmZmZmYpJC9pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/regex.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/rng.js":
/*!*******************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/rng.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rng)\n/* harmony export */ });\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nfunction rng() {\n    if (!getRandomValues) {\n        if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\n            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n        }\n        getRandomValues = crypto.getRandomValues.bind(crypto);\n    }\n    return getRandomValues(rnds8);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3JuZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3JuZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgZ2V0UmFuZG9tVmFsdWVzO1xuY29uc3Qgcm5kczggPSBuZXcgVWludDhBcnJheSgxNik7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBybmcoKSB7XG4gICAgaWYgKCFnZXRSYW5kb21WYWx1ZXMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBjcnlwdG8gPT09ICd1bmRlZmluZWQnIHx8ICFjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2NyeXB0by5nZXRSYW5kb21WYWx1ZXMoKSBub3Qgc3VwcG9ydGVkLiBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3V1aWRqcy91dWlkI2dldHJhbmRvbXZhbHVlcy1ub3Qtc3VwcG9ydGVkJyk7XG4gICAgICAgIH1cbiAgICAgICAgZ2V0UmFuZG9tVmFsdWVzID0gY3J5cHRvLmdldFJhbmRvbVZhbHVlcy5iaW5kKGNyeXB0byk7XG4gICAgfVxuICAgIHJldHVybiBnZXRSYW5kb21WYWx1ZXMocm5kczgpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/rng.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/stringify.js":
/*!*************************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/stringify.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   unsafeStringify: () => (/* binding */ unsafeStringify)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/validate.js\");\n\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nfunction unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stringify);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/stringify.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/v4.js":
/*!******************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/v4.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _native_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./native.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/native.js\");\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rng.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stringify.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/stringify.js\");\n\n\n\nfunction v4(options, buf, offset) {\n    if (_native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID && !buf && !options) {\n        return _native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? (0,_rng_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.unsafeStringify)(rnds);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v4);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3Y0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUM7QUFDTjtBQUNzQjtBQUNqRDtBQUNBLFFBQVEsa0RBQU07QUFDZCxlQUFlLGtEQUFNO0FBQ3JCO0FBQ0E7QUFDQSxzREFBc0QsbURBQUc7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9EQUFvRCxPQUFPLEdBQUcsYUFBYTtBQUMzRTtBQUNBLHdCQUF3QixRQUFRO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyw4REFBZTtBQUMxQjtBQUNBLGlFQUFlLEVBQUUsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3Y0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBuYXRpdmUgZnJvbSAnLi9uYXRpdmUuanMnO1xuaW1wb3J0IHJuZyBmcm9tICcuL3JuZy5qcyc7XG5pbXBvcnQgeyB1bnNhZmVTdHJpbmdpZnkgfSBmcm9tICcuL3N0cmluZ2lmeS5qcyc7XG5mdW5jdGlvbiB2NChvcHRpb25zLCBidWYsIG9mZnNldCkge1xuICAgIGlmIChuYXRpdmUucmFuZG9tVVVJRCAmJiAhYnVmICYmICFvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiBuYXRpdmUucmFuZG9tVVVJRCgpO1xuICAgIH1cbiAgICBvcHRpb25zID0gb3B0aW9ucyB8fCB7fTtcbiAgICBjb25zdCBybmRzID0gb3B0aW9ucy5yYW5kb20gPz8gb3B0aW9ucy5ybmc/LigpID8/IHJuZygpO1xuICAgIGlmIChybmRzLmxlbmd0aCA8IDE2KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignUmFuZG9tIGJ5dGVzIGxlbmd0aCBtdXN0IGJlID49IDE2Jyk7XG4gICAgfVxuICAgIHJuZHNbNl0gPSAocm5kc1s2XSAmIDB4MGYpIHwgMHg0MDtcbiAgICBybmRzWzhdID0gKHJuZHNbOF0gJiAweDNmKSB8IDB4ODA7XG4gICAgaWYgKGJ1Zikge1xuICAgICAgICBvZmZzZXQgPSBvZmZzZXQgfHwgMDtcbiAgICAgICAgaWYgKG9mZnNldCA8IDAgfHwgb2Zmc2V0ICsgMTYgPiBidWYubGVuZ3RoKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgUmFuZ2VFcnJvcihgVVVJRCBieXRlIHJhbmdlICR7b2Zmc2V0fToke29mZnNldCArIDE1fSBpcyBvdXQgb2YgYnVmZmVyIGJvdW5kc2ApO1xuICAgICAgICB9XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgMTY7ICsraSkge1xuICAgICAgICAgICAgYnVmW29mZnNldCArIGldID0gcm5kc1tpXTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYnVmO1xuICAgIH1cbiAgICByZXR1cm4gdW5zYWZlU3RyaW5naWZ5KHJuZHMpO1xufVxuZXhwb3J0IGRlZmF1bHQgdjQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/v4.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/validate.js":
/*!************************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/validate.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/regex.js\");\n\nfunction validate(uuid) {\n    return typeof uuid === 'string' && _regex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].test(uuid);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validate);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3ZhbGlkYXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStCO0FBQy9CO0FBQ0EsdUNBQXVDLGlEQUFLO0FBQzVDO0FBQ0EsaUVBQWUsUUFBUSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdmFsaWRhdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJFR0VYIGZyb20gJy4vcmVnZXguanMnO1xuZnVuY3Rpb24gdmFsaWRhdGUodXVpZCkge1xuICAgIHJldHVybiB0eXBlb2YgdXVpZCA9PT0gJ3N0cmluZycgJiYgUkVHRVgudGVzdCh1dWlkKTtcbn1cbmV4cG9ydCBkZWZhdWx0IHZhbGlkYXRlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/validate.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/index.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/index.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: () => (/* reexport safe */ _lib_analytics__WEBPACK_IMPORTED_MODULE_7__.AnalyticsService),\n/* harmony export */   BiometricService: () => (/* reexport safe */ _lib_biometric_web__WEBPACK_IMPORTED_MODULE_10__.BiometricService),\n/* harmony export */   BudgetService: () => (/* reexport safe */ _lib_budget__WEBPACK_IMPORTED_MODULE_6__.BudgetService),\n/* harmony export */   Constants: () => (/* reexport safe */ _database_types__WEBPACK_IMPORTED_MODULE_3__.Constants),\n/* harmony export */   ExpenseService: () => (/* reexport safe */ _lib_expenses__WEBPACK_IMPORTED_MODULE_5__.ExpenseService),\n/* harmony export */   RecurringTransactionService: () => (/* reexport safe */ _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_9__.RecurringTransactionService),\n/* harmony export */   TransactionService: () => (/* reexport safe */ _lib_transactions__WEBPACK_IMPORTED_MODULE_8__.TransactionService),\n/* harmony export */   accountSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.accountSchema),\n/* harmony export */   budgetFormInputSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_13__.budgetFormInputSchema),\n/* harmony export */   budgetFormSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_13__.budgetFormSchema),\n/* harmony export */   budgetSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_13__.budgetSchema),\n/* harmony export */   calculateBudgetProgress: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.calculateBudgetProgress),\n/* harmony export */   categorySchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.categorySchema),\n/* harmony export */   expenseFormInputSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_12__.expenseFormInputSchema),\n/* harmony export */   expenseFormSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_12__.expenseFormSchema),\n/* harmony export */   expenseSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_12__.expenseSchema),\n/* harmony export */   formatCurrency: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency),\n/* harmony export */   formatDate: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatDate),\n/* harmony export */   investmentFormInputSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_14__.investmentFormInputSchema),\n/* harmony export */   investmentFormSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_14__.investmentFormSchema),\n/* harmony export */   investmentSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.investmentSchema),\n/* harmony export */   resetPasswordSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_11__.resetPasswordSchema),\n/* harmony export */   signInSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_11__.signInSchema),\n/* harmony export */   signUpSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_11__.signUpSchema),\n/* harmony export */   supabase: () => (/* reexport safe */ _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase),\n/* harmony export */   supabaseMobile: () => (/* reexport safe */ _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_16__.supabase),\n/* harmony export */   transactionFormInputSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_14__.transactionFormInputSchema),\n/* harmony export */   transactionFormSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_14__.transactionFormSchema),\n/* harmony export */   transactionSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.transactionSchema),\n/* harmony export */   transferFormInputSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_14__.transferFormInputSchema),\n/* harmony export */   transferFormSchema: () => (/* reexport safe */ _schemas_transaction__WEBPACK_IMPORTED_MODULE_14__.transferFormSchema),\n/* harmony export */   transferSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.transferSchema),\n/* harmony export */   useCurrencyStore: () => (/* reexport safe */ _stores_currencyStore__WEBPACK_IMPORTED_MODULE_15__.useCurrencyStore)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/../../packages/shared/src/types.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/../../packages/shared/src/utils.ts\");\n/* harmony import */ var _validators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./validators */ \"(app-pages-browser)/../../packages/shared/src/validators.ts\");\n/* harmony import */ var _database_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./database.types */ \"(app-pages-browser)/../../packages/shared/src/database.types.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _lib_expenses__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/expenses */ \"(app-pages-browser)/../../packages/shared/src/lib/expenses.ts\");\n/* harmony import */ var _lib_budget__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/budget */ \"(app-pages-browser)/../../packages/shared/src/lib/budget.ts\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/analytics */ \"(app-pages-browser)/../../packages/shared/src/lib/analytics.ts\");\n/* harmony import */ var _lib_transactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/transactions */ \"(app-pages-browser)/../../packages/shared/src/lib/transactions.ts\");\n/* harmony import */ var _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/recurring-transactions */ \"(app-pages-browser)/../../packages/shared/src/lib/recurring-transactions.ts\");\n/* harmony import */ var _lib_biometric_web__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./lib/biometric.web */ \"(app-pages-browser)/../../packages/shared/src/lib/biometric.web.ts\");\n/* harmony import */ var _schemas_auth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./schemas/auth */ \"(app-pages-browser)/../../packages/shared/src/schemas/auth.ts\");\n/* harmony import */ var _schemas_expense__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./schemas/expense */ \"(app-pages-browser)/../../packages/shared/src/schemas/expense.ts\");\n/* harmony import */ var _schemas_budget__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./schemas/budget */ \"(app-pages-browser)/../../packages/shared/src/schemas/budget.ts\");\n/* harmony import */ var _schemas_transaction__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./schemas/transaction */ \"(app-pages-browser)/../../packages/shared/src/schemas/transaction.ts\");\n/* harmony import */ var _stores_currencyStore__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./stores/currencyStore */ \"(app-pages-browser)/../../packages/shared/src/stores/currencyStore.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./lib/supabase.mobile */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.mobile.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(_lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_16__);\n// Shared business logic and utilities\n\n\n\n\n\n\n\n\n\n\n// Platform-specific biometric exports\n\n\n\n\n\n\n// Platform-specific exports (explicit re-export to avoid naming conflicts)\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHNDQUFzQztBQUNkO0FBQ0E7QUFDSztBQUNJO0FBQ0Y7QUFDQTtBQUNGO0FBQ0c7QUFDRztBQUNVO0FBQzdDLHNDQUFzQztBQUNGO0FBQ0w7QUFDRztBQUNEO0FBQ0s7QUFDQztBQUV2QywyRUFBMkU7QUFDUiIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9wYWNrYWdlcy9zaGFyZWQvc3JjL2luZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNoYXJlZCBidXNpbmVzcyBsb2dpYyBhbmQgdXRpbGl0aWVzXG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJztcbmV4cG9ydCAqIGZyb20gJy4vdXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi92YWxpZGF0b3JzJztcbmV4cG9ydCAqIGZyb20gJy4vZGF0YWJhc2UudHlwZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvc3VwYWJhc2UnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvZXhwZW5zZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvYnVkZ2V0JztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2FuYWx5dGljcyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi90cmFuc2FjdGlvbnMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvcmVjdXJyaW5nLXRyYW5zYWN0aW9ucyc7XG4vLyBQbGF0Zm9ybS1zcGVjaWZpYyBiaW9tZXRyaWMgZXhwb3J0c1xuZXhwb3J0ICogZnJvbSAnLi9saWIvYmlvbWV0cmljLndlYic7XG5leHBvcnQgKiBmcm9tICcuL3NjaGVtYXMvYXV0aCc7XG5leHBvcnQgKiBmcm9tICcuL3NjaGVtYXMvZXhwZW5zZSc7XG5leHBvcnQgKiBmcm9tICcuL3NjaGVtYXMvYnVkZ2V0JztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy90cmFuc2FjdGlvbic7XG5leHBvcnQgKiBmcm9tICcuL3N0b3Jlcy9jdXJyZW5jeVN0b3JlJztcblxuLy8gUGxhdGZvcm0tc3BlY2lmaWMgZXhwb3J0cyAoZXhwbGljaXQgcmUtZXhwb3J0IHRvIGF2b2lkIG5hbWluZyBjb25mbGljdHMpXG5leHBvcnQgeyBzdXBhYmFzZSBhcyBzdXBhYmFzZU1vYmlsZSB9IGZyb20gJy4vbGliL3N1cGFiYXNlLm1vYmlsZSc7Il0sIm5hbWVzIjpbInN1cGFiYXNlIiwic3VwYWJhc2VNb2JpbGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/investments.ts":
/*!****************************************************!*\
  !*** ../../packages/shared/src/lib/investments.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvestmentService: () => (/* binding */ InvestmentService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n\nclass InvestmentService {\n    /**\n   * Create an investment transaction (buy/sell)\n   * This also creates a transfer from a funding account for buy transactions\n   */ static async createInvestmentTransaction(investmentData, fundingAccountId// Required for buy transactions\n    ) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Validate investment account exists and is an investment account\n        const { data: investmentAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', investmentData.account_id).eq('user_id', user.id).single();\n        if (!investmentAccount || investmentAccount.account_type !== 'investment') {\n            throw new Error('Invalid investment account specified');\n        }\n        // For buy transactions, validate funding account and check balance\n        if (investmentData.transaction_type === 'investment_buy') {\n            if (!fundingAccountId) {\n                throw new Error('Funding account required for investment purchases');\n            }\n            const { data: fundingAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', fundingAccountId).eq('user_id', user.id).single();\n            if (!fundingAccount) {\n                throw new Error('Invalid funding account specified');\n            }\n            const totalCost = investmentData.amount + (investmentData.fees || 0);\n            if (fundingAccount.current_balance < totalCost) {\n                throw new Error('Insufficient balance in funding account');\n            }\n        }\n        // Get investment category\n        const categoryName = investmentData.transaction_type === 'investment_buy' ? 'Investment Purchase' : 'Investment Sale';\n        const { data: category } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', categoryName).eq('is_system', true).single();\n        if (!category) {\n            throw new Error(\"\".concat(categoryName, \" category not found\"));\n        }\n        // Create the investment transaction\n        const transactionData = {\n            amount: investmentData.amount,\n            description: investmentData.description || \"\".concat(investmentData.transaction_type === 'investment_buy' ? 'Buy' : 'Sell', \" \").concat(investmentData.investment_quantity, \" shares of \").concat(investmentData.investment_symbol),\n            category_id: category.id,\n            account_id: investmentData.account_id,\n            transaction_type: investmentData.transaction_type,\n            transaction_date: investmentData.transaction_date,\n            transaction_status: 'completed',\n            fees: investmentData.fees || 0,\n            investment_symbol: investmentData.investment_symbol,\n            investment_quantity: investmentData.investment_quantity,\n            investment_price: investmentData.investment_price,\n            user_id: user.id\n        };\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \").single();\n        if (transactionError) {\n            throw new Error(\"Failed to create investment transaction: \".concat(transactionError.message));\n        }\n        // For buy transactions, create a transfer from funding account to investment account\n        if (investmentData.transaction_type === 'investment_buy' && fundingAccountId) {\n            const { TransferService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\"));\n            try {\n                await TransferService.createTransfer({\n                    amount: investmentData.amount + (investmentData.fees || 0),\n                    description: \"Investment purchase: \".concat(investmentData.investment_symbol),\n                    from_account_id: fundingAccountId,\n                    to_account_id: investmentData.account_id,\n                    transaction_date: investmentData.transaction_date,\n                    fees: 0\n                });\n            } catch (error) {\n                // If transfer fails, rollback the investment transaction\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', transaction.id);\n                throw new Error(\"Failed to create funding transfer: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n            }\n        }\n        return transaction;\n    }\n    /**\n   * Get investment transactions for a user or specific account\n   */ static async getInvestmentTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \", {\n            count: 'exact'\n        }).eq('user_id', user.id).in('transaction_type', [\n            'investment_buy',\n            'investment_sell'\n        ]).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            query = query.eq('account_id', options.account_id);\n        }\n        if (options === null || options === void 0 ? void 0 : options.symbol) {\n            query = query.eq('investment_symbol', options.symbol);\n        }\n        if (options === null || options === void 0 ? void 0 : options.transaction_type) {\n            query = query.eq('transaction_type', options.transaction_type);\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 20) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch investment transactions: \".concat(error.message));\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    /**\n   * Get investment holdings for a user or specific account\n   */ static async getInvestmentHoldings(accountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_holdings').select(\"\\n        *,\\n        account:accounts(*)\\n      \").order('symbol');\n        if (accountId) {\n            query = query.eq('account_id', accountId);\n        } else {\n            // Filter by user's accounts\n            const { data: userAccounts } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('id').eq('user_id', user.id).eq('account_type', 'investment');\n            if (userAccounts && userAccounts.length > 0) {\n                const accountIds = userAccounts.map((acc)=>acc.id);\n                query = query.in('account_id', accountIds);\n            } else {\n                return [];\n            }\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch investment holdings: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get portfolio summary for all investment accounts\n   */ static async getPortfolioSummary() {\n        const holdings = await this.getInvestmentHoldings();\n        let totalValue = 0;\n        let totalCost = 0;\n        const holdingsBySymbol = {};\n        holdings.forEach((holding)=>{\n            const symbol = holding.symbol;\n            const quantity = holding.quantity;\n            const avgCost = holding.average_cost;\n            const currentPrice = holding.current_price || avgCost;\n            const marketValue = quantity * currentPrice;\n            const costBasis = quantity * avgCost;\n            const gainLoss = marketValue - costBasis;\n            const gainLossPercent = costBasis > 0 ? gainLoss / costBasis * 100 : 0;\n            if (!holdingsBySymbol[symbol]) {\n                holdingsBySymbol[symbol] = {\n                    symbol,\n                    totalQuantity: 0,\n                    averageCost: 0,\n                    currentPrice,\n                    marketValue: 0,\n                    gainLoss: 0,\n                    gainLossPercent: 0\n                };\n            }\n            // Aggregate holdings for the same symbol across accounts\n            const existing = holdingsBySymbol[symbol];\n            const newTotalQuantity = existing.totalQuantity + quantity;\n            const newTotalCost = existing.totalQuantity * existing.averageCost + quantity * avgCost;\n            holdingsBySymbol[symbol] = {\n                ...existing,\n                totalQuantity: newTotalQuantity,\n                averageCost: newTotalQuantity > 0 ? newTotalCost / newTotalQuantity : 0,\n                marketValue: existing.marketValue + marketValue,\n                gainLoss: existing.gainLoss + gainLoss\n            };\n            // Recalculate percentage\n            const totalCostBasis = holdingsBySymbol[symbol].totalQuantity * holdingsBySymbol[symbol].averageCost;\n            holdingsBySymbol[symbol].gainLossPercent = totalCostBasis > 0 ? holdingsBySymbol[symbol].gainLoss / totalCostBasis * 100 : 0;\n            totalValue += marketValue;\n            totalCost += costBasis;\n        });\n        const totalGainLoss = totalValue - totalCost;\n        const totalGainLossPercent = totalCost > 0 ? totalGainLoss / totalCost * 100 : 0;\n        return {\n            totalValue,\n            totalCost,\n            totalGainLoss,\n            totalGainLossPercent,\n            holdingsBySymbol\n        };\n    }\n    /**\n   * Update current prices for holdings (would typically be called by a background job)\n   */ static async updateHoldingPrices(priceUpdates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        for (const update of priceUpdates){\n            const marketValue = update.price // Will be calculated by trigger\n            ;\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_holdings').update({\n                current_price: update.price,\n                market_value: marketValue,\n                last_updated: new Date().toISOString()\n            }).eq('symbol', update.symbol);\n            if (error) {\n                console.error(\"Failed to update price for \".concat(update.symbol, \":\"), error);\n            }\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/investments.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/transactions.ts":
/*!*****************************************************!*\
  !*** ../../packages/shared/src/lib/transactions.ts ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionService: () => (/* binding */ TransactionService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _transfers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\");\n/* harmony import */ var _investments__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./investments */ \"(app-pages-browser)/../../packages/shared/src/lib/investments.ts\");\n\n\n\nclass TransactionService {\n    /**\n   * Create a transaction of any type\n   */ static async createTransaction(data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Handle different transaction types\n        switch(data.transaction_type){\n            case 'transfer':\n                if (!data.account_id || !data.to_account_id) {\n                    throw new Error('Source and destination accounts are required for transfers');\n                }\n                const transfer = await _transfers__WEBPACK_IMPORTED_MODULE_1__.TransferService.createTransfer({\n                    amount: data.amount,\n                    description: data.description,\n                    from_account_id: data.account_id,\n                    to_account_id: data.to_account_id,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees\n                });\n                // Return the outgoing transaction as the primary transaction\n                const { data: transferTransactions } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n            *,\\n            category:categories(*),\\n            account:accounts(*),\\n            to_account:accounts!transactions_to_account_id_fkey(*)\\n          \").eq('transfer_id', transfer.transfer_id).eq('account_id', data.account_id).single();\n                return transferTransactions;\n            case 'investment_buy':\n            case 'investment_sell':\n                if (!data.account_id || !data.investment_symbol || !data.investment_quantity || !data.investment_price) {\n                    throw new Error('Investment account, symbol, quantity, and price are required for investment transactions');\n                }\n                const investment = await _investments__WEBPACK_IMPORTED_MODULE_2__.InvestmentService.createInvestmentTransaction({\n                    amount: data.amount,\n                    description: data.description,\n                    account_id: data.account_id,\n                    investment_symbol: data.investment_symbol,\n                    investment_quantity: data.investment_quantity,\n                    investment_price: data.investment_price,\n                    transaction_type: data.transaction_type,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees\n                }, data.funding_account_id);\n                return investment;\n            case 'income':\n            case 'expense':\n            case 'dividend':\n                if (!data.category_id || !data.account_id) {\n                    throw new Error('Category and account are required for income/expense transactions');\n                }\n                const transactionData = {\n                    amount: data.amount,\n                    description: data.description || null,\n                    category_id: data.category_id,\n                    account_id: data.account_id,\n                    transaction_type: data.transaction_type,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    transaction_status: 'completed',\n                    fees: data.fees || 0,\n                    investment_symbol: data.investment_symbol || null,\n                    user_id: user.id\n                };\n                const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(\"\\n            *,\\n            category:categories(*),\\n            account:accounts(*)\\n          \").single();\n                if (error) {\n                    throw new Error(\"Failed to create transaction: \".concat(error.message));\n                }\n                return transaction;\n            default:\n                throw new Error(\"Unsupported transaction type: \".concat(data.transaction_type));\n        }\n    }\n    /**\n   * Get all transactions for the current user with enhanced filtering\n   */ static async getTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \", {\n            count: 'exact'\n        }).eq('user_id', user.id).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        // Apply filters\n        if (options === null || options === void 0 ? void 0 : options.categoryId) {\n            query = query.eq('category_id', options.categoryId);\n        }\n        if (options === null || options === void 0 ? void 0 : options.accountId) {\n            query = query.or(\"account_id.eq.\".concat(options.accountId, \",to_account_id.eq.\").concat(options.accountId));\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.transactionType) {\n            if (Array.isArray(options.transactionType)) {\n                query = query.in('transaction_type', options.transactionType);\n            } else {\n                query = query.eq('transaction_type', options.transactionType);\n            }\n        }\n        if (options === null || options === void 0 ? void 0 : options.transactionStatus) {\n            query = query.eq('transaction_status', options.transactionStatus);\n        }\n        // Filter out transfers and investments if not explicitly requested\n        if (!(options === null || options === void 0 ? void 0 : options.includeTransfers) && !(options === null || options === void 0 ? void 0 : options.includeInvestments)) {\n            query = query.in('transaction_type', [\n                'income',\n                'expense',\n                'dividend'\n            ]);\n        } else if (!(options === null || options === void 0 ? void 0 : options.includeTransfers)) {\n            query = query.neq('transaction_type', 'transfer');\n        } else if (!(options === null || options === void 0 ? void 0 : options.includeInvestments)) {\n            query = query.not('transaction_type', 'in', '(investment_buy,investment_sell)');\n        }\n        if (options === null || options === void 0 ? void 0 : options.searchQuery) {\n            // Search in description, category name, and investment symbol\n            query = query.or(\"description.ilike.%\".concat(options.searchQuery, \"%,\") + \"investment_symbol.ilike.%\".concat(options.searchQuery, \"%\"));\n        }\n        // Apply pagination\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch transactions: \".concat(error.message));\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    /**\n   * Get a specific transaction by ID\n   */ static async getTransaction(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            throw new Error(\"Failed to fetch transaction: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update a transaction (limited to basic transactions, not transfers or investments)\n   */ static async updateTransaction(id, updates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // First check if this is a basic transaction (not transfer or investment)\n        const { data: existingTransaction } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, transfer_id').eq('id', id).eq('user_id', user.id).single();\n        if (!existingTransaction) {\n            throw new Error('Transaction not found');\n        }\n        if (existingTransaction.transaction_type === 'transfer' || existingTransaction.transaction_type === 'investment_buy' || existingTransaction.transaction_type === 'investment_sell') {\n            throw new Error('Cannot update transfer or investment transactions through this method');\n        }\n        const updateData = {\n            updated_at: new Date().toISOString()\n        };\n        if (updates.amount !== undefined) updateData.amount = updates.amount;\n        if (updates.description !== undefined) updateData.description = updates.description;\n        if (updates.category_id !== undefined) updateData.category_id = updates.category_id;\n        if (updates.fees !== undefined) updateData.fees = updates.fees;\n        if (updates.transaction_date !== undefined) {\n            updateData.transaction_date = updates.transaction_date.toISOString().split('T')[0];\n        }\n        const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').update(updateData).eq('id', id).eq('user_id', user.id).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to update transaction: \".concat(error.message));\n        }\n        return transaction;\n    }\n    /**\n   * Delete a transaction\n   */ static async deleteTransaction(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if this is a transfer transaction\n        const { data: transaction } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, transfer_id').eq('id', id).eq('user_id', user.id).single();\n        if (!transaction) {\n            throw new Error('Transaction not found');\n        }\n        if (transaction.transaction_type === 'transfer' && transaction.transfer_id) {\n            // Delete all transactions with the same transfer_id\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('transfer_id', transaction.transfer_id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete transfer: \".concat(error.message));\n            }\n        } else {\n            // Delete single transaction\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete transaction: \".concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get transaction summary for a date range\n   */ static async getTransactionSummary(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, amount').eq('user_id', user.id).eq('transaction_status', 'completed');\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.accountId) {\n            query = query.or(\"account_id.eq.\".concat(options.accountId, \",to_account_id.eq.\").concat(options.accountId));\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch transaction summary: \".concat(error.message));\n        }\n        const summary = {\n            totalIncome: 0,\n            totalExpenses: 0,\n            totalTransfers: 0,\n            totalInvestments: 0,\n            netFlow: 0,\n            transactionCount: (data === null || data === void 0 ? void 0 : data.length) || 0\n        };\n        data === null || data === void 0 ? void 0 : data.forEach((transaction)=>{\n            switch(transaction.transaction_type){\n                case 'income':\n                case 'dividend':\n                    summary.totalIncome += transaction.amount;\n                    break;\n                case 'expense':\n                    summary.totalExpenses += transaction.amount;\n                    break;\n                case 'transfer':\n                    summary.totalTransfers += transaction.amount;\n                    break;\n                case 'investment_buy':\n                case 'investment_sell':\n                    summary.totalInvestments += transaction.amount;\n                    break;\n            }\n        });\n        summary.netFlow = summary.totalIncome - summary.totalExpenses;\n        return summary;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/transactions.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/transfers.ts":
/*!**************************************************!*\
  !*** ../../packages/shared/src/lib/transfers.ts ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransferService: () => (/* binding */ TransferService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/v4.js\");\n\n\nclass TransferService {\n    /**\n   * Create a transfer between two accounts\n   * This creates two linked transactions: one debit and one credit\n   */ static async createTransfer(transferData) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Validate accounts exist and belong to user\n        const { data: fromAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', transferData.from_account_id).eq('user_id', user.id).single();\n        const { data: toAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', transferData.to_account_id).eq('user_id', user.id).single();\n        if (!fromAccount || !toAccount) {\n            throw new Error('Invalid account(s) specified');\n        }\n        if (fromAccount.id === toAccount.id) {\n            throw new Error('Cannot transfer to the same account');\n        }\n        // Check if source account has sufficient balance (except for credit cards)\n        if (fromAccount.account_type !== 'credit_card' && fromAccount.current_balance < transferData.amount) {\n            throw new Error('Insufficient balance in source account');\n        }\n        // Generate a unique transfer ID to link the transactions\n        const transferId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        // Get system transfer categories\n        const { data: transferOutCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', 'Transfer Out').eq('is_system', true).single();\n        const { data: transferInCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', 'Transfer In').eq('is_system', true).single();\n        if (!transferOutCategory || !transferInCategory) {\n            throw new Error('Transfer categories not found');\n        }\n        // Create the outgoing transaction (debit from source account)\n        const outgoingTransaction = {\n            amount: transferData.amount,\n            description: transferData.description || \"Transfer to \".concat(toAccount.name),\n            category_id: transferOutCategory.id,\n            account_id: transferData.from_account_id,\n            to_account_id: transferData.to_account_id,\n            transfer_id: transferId,\n            transaction_type: 'transfer',\n            transaction_date: transferData.transaction_date,\n            transaction_status: 'completed',\n            fees: transferData.fees || 0,\n            user_id: user.id\n        };\n        // Create the incoming transaction (credit to destination account)\n        const incomingTransaction = {\n            amount: transferData.amount,\n            description: transferData.description || \"Transfer from \".concat(fromAccount.name),\n            category_id: transferInCategory.id,\n            account_id: transferData.to_account_id,\n            to_account_id: transferData.from_account_id,\n            transfer_id: transferId,\n            transaction_type: 'transfer',\n            transaction_date: transferData.transaction_date,\n            transaction_status: 'completed',\n            fees: 0,\n            user_id: user.id\n        };\n        // Insert both transactions in a transaction\n        const { data: outgoingData, error: outgoingError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(outgoingTransaction).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").single();\n        if (outgoingError) {\n            throw new Error(\"Failed to create outgoing transaction: \".concat(outgoingError.message));\n        }\n        const { data: incomingData, error: incomingError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(incomingTransaction).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").single();\n        if (incomingError) {\n            // If incoming transaction fails, we should rollback the outgoing transaction\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', outgoingData.id);\n            throw new Error(\"Failed to create incoming transaction: \".concat(incomingError.message));\n        }\n        // Return a combined transfer object\n        return {\n            id: transferId,\n            amount: transferData.amount,\n            description: transferData.description,\n            from_account_id: transferData.from_account_id,\n            to_account_id: transferData.to_account_id,\n            transfer_id: transferId,\n            transaction_date: transferData.transaction_date,\n            fees: transferData.fees,\n            user_id: user.id,\n            from_account: fromAccount,\n            to_account: toAccount\n        };\n    }\n    /**\n   * Get all transfers for the current user\n   */ static async getTransfers(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Get unique transfer IDs first\n        let transferQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transfer_id, transaction_date', {\n            count: 'exact'\n        }).eq('user_id', user.id).eq('transaction_type', 'transfer').not('transfer_id', 'is', null).order('transaction_date', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            transferQuery = transferQuery.or(\"account_id.eq.\".concat(options.account_id, \",to_account_id.eq.\").concat(options.account_id));\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            transferQuery = transferQuery.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            transferQuery = transferQuery.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            transferQuery = transferQuery.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            transferQuery = transferQuery.range(options.offset, options.offset + (options.limit || 20) - 1);\n        }\n        const { data: transferIds, error: transferError, count } = await transferQuery;\n        if (transferError) {\n            throw new Error(\"Failed to fetch transfers: \".concat(transferError.message));\n        }\n        if (!transferIds || transferIds.length === 0) {\n            return {\n                data: [],\n                count: count || 0\n            };\n        }\n        // Get the actual transfer transactions\n        const uniqueTransferIds = [\n            ...new Set(transferIds.map((t)=>t.transfer_id))\n        ];\n        const { data: transactions, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").in('transfer_id', uniqueTransferIds).eq('user_id', user.id).eq('transaction_type', 'transfer');\n        if (transactionError) {\n            throw new Error(\"Failed to fetch transfer transactions: \".concat(transactionError.message));\n        }\n        // Group transactions by transfer_id and create transfer objects\n        const transferMap = new Map();\n        transactions === null || transactions === void 0 ? void 0 : transactions.forEach((transaction)=>{\n            if (transaction.transfer_id) {\n                if (!transferMap.has(transaction.transfer_id)) {\n                    transferMap.set(transaction.transfer_id, []);\n                }\n                transferMap.get(transaction.transfer_id).push(transaction);\n            }\n        });\n        const transfers = [];\n        transferMap.forEach((transactionPair, transferId)=>{\n            if (transactionPair.length === 2) {\n                // Find the outgoing transaction (the one with fees or the one from the source account)\n                const outgoing = transactionPair.find((t)=>t.fees && t.fees > 0) || transactionPair[0];\n                const incoming = transactionPair.find((t)=>t.id !== outgoing.id);\n                transfers.push({\n                    id: transferId,\n                    amount: outgoing.amount,\n                    description: outgoing.description,\n                    from_account_id: outgoing.account_id,\n                    to_account_id: outgoing.to_account_id,\n                    transfer_id: transferId,\n                    transaction_date: outgoing.transaction_date,\n                    fees: outgoing.fees,\n                    user_id: user.id,\n                    from_account: outgoing.account,\n                    to_account: outgoing.to_account\n                });\n            }\n        });\n        // Sort by transaction date\n        transfers.sort((a, b)=>new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime());\n        return {\n            data: transfers,\n            count: count || 0\n        };\n    }\n    /**\n   * Get a specific transfer by transfer ID\n   */ static async getTransfer(transferId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data: transactions, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").eq('transfer_id', transferId).eq('user_id', user.id).eq('transaction_type', 'transfer');\n        if (error) {\n            throw new Error(\"Failed to fetch transfer: \".concat(error.message));\n        }\n        if (!transactions || transactions.length !== 2) {\n            throw new Error('Transfer not found or incomplete');\n        }\n        // Find the outgoing transaction\n        const outgoing = transactions.find((t)=>t.fees && t.fees > 0) || transactions[0];\n        return {\n            id: transferId,\n            amount: outgoing.amount,\n            description: outgoing.description,\n            from_account_id: outgoing.account_id,\n            to_account_id: outgoing.to_account_id,\n            transfer_id: transferId,\n            transaction_date: outgoing.transaction_date,\n            fees: outgoing.fees,\n            user_id: user.id,\n            from_account: outgoing.account,\n            to_account: outgoing.to_account\n        };\n    }\n    /**\n   * Cancel a transfer (mark both transactions as cancelled)\n   */ static async cancelTransfer(transferId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').update({\n            transaction_status: 'cancelled',\n            updated_at: new Date().toISOString()\n        }).eq('transfer_id', transferId).eq('user_id', user.id).eq('transaction_type', 'transfer');\n        if (error) {\n            throw new Error(\"Failed to cancel transfer: \".concat(error.message));\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\n"));

/***/ })

});