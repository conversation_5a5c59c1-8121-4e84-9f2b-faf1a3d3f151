"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/../../packages/shared/src/lib/transfers.ts":
/*!**************************************************!*\
  !*** ../../packages/shared/src/lib/transfers.ts ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransferService: () => (/* binding */ TransferService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/v4.js\");\n\n\nclass TransferService {\n    /**\n   * Create a transfer between two accounts\n   * This creates two linked transactions: one debit and one credit\n   */ static async createTransfer(transferData) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Validate accounts exist and belong to user\n        const { data: fromAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', transferData.from_account_id).eq('user_id', user.id).single();\n        const { data: toAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', transferData.to_account_id).eq('user_id', user.id).single();\n        if (!fromAccount || !toAccount) {\n            throw new Error('Invalid account(s) specified');\n        }\n        if (fromAccount.id === toAccount.id) {\n            throw new Error('Cannot transfer to the same account');\n        }\n        // Check if source account has sufficient balance (except for credit cards)\n        if (fromAccount.account_type !== 'credit_card' && fromAccount.current_balance < transferData.amount) {\n            throw new Error('Insufficient balance in source account');\n        }\n        // Generate a unique transfer ID to link the transactions\n        const transferId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        // Get system transfer categories\n        const { data: transferOutCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', 'Transfer Out').eq('is_system', true).single();\n        const { data: transferInCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', 'Transfer In').eq('is_system', true).single();\n        if (!transferOutCategory || !transferInCategory) {\n            throw new Error('Transfer categories not found');\n        }\n        // Create the outgoing transaction (debit from source account)\n        const outgoingTransaction = {\n            amount: transferData.amount,\n            description: transferData.description || \"Transfer to \".concat(toAccount.name),\n            category_id: transferOutCategory.id,\n            account_id: transferData.from_account_id,\n            to_account_id: transferData.to_account_id,\n            transfer_id: transferId,\n            transaction_type: 'transfer',\n            transaction_date: transferData.transaction_date,\n            transaction_status: 'completed',\n            fees: transferData.fees || 0,\n            user_id: user.id\n        };\n        // Create the incoming transaction (credit to destination account)\n        const incomingTransaction = {\n            amount: transferData.amount,\n            description: transferData.description || \"Transfer from \".concat(fromAccount.name),\n            category_id: transferInCategory.id,\n            account_id: transferData.to_account_id,\n            to_account_id: transferData.from_account_id,\n            transfer_id: transferId,\n            transaction_type: 'transfer',\n            transaction_date: transferData.transaction_date,\n            transaction_status: 'completed',\n            fees: 0,\n            user_id: user.id\n        };\n        // Insert both transactions in a transaction\n        const { data: outgoingData, error: outgoingError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(outgoingTransaction).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").single();\n        if (outgoingError) {\n            throw new Error(\"Failed to create outgoing transaction: \".concat(outgoingError.message));\n        }\n        const { data: incomingData, error: incomingError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(incomingTransaction).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").single();\n        if (incomingError) {\n            // If incoming transaction fails, we should rollback the outgoing transaction\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', outgoingData.id);\n            throw new Error(\"Failed to create incoming transaction: \".concat(incomingError.message));\n        }\n        // Return a combined transfer object\n        return {\n            id: transferId,\n            amount: transferData.amount,\n            description: transferData.description,\n            from_account_id: transferData.from_account_id,\n            to_account_id: transferData.to_account_id,\n            transfer_id: transferId,\n            transaction_date: transferData.transaction_date,\n            fees: transferData.fees,\n            user_id: user.id,\n            from_account: fromAccount,\n            to_account: toAccount\n        };\n    }\n    /**\n   * Get all transfers for the current user\n   */ static async getTransfers(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Get unique transfer IDs first\n        let transferQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transfer_id, transaction_date', {\n            count: 'exact'\n        }).eq('user_id', user.id).eq('transaction_type', 'transfer').not('transfer_id', 'is', null).order('transaction_date', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            transferQuery = transferQuery.or(\"account_id.eq.\".concat(options.account_id, \",to_account_id.eq.\").concat(options.account_id));\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            transferQuery = transferQuery.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            transferQuery = transferQuery.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            transferQuery = transferQuery.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            transferQuery = transferQuery.range(options.offset, options.offset + (options.limit || 20) - 1);\n        }\n        const { data: transferIds, error: transferError, count } = await transferQuery;\n        if (transferError) {\n            throw new Error(\"Failed to fetch transfers: \".concat(transferError.message));\n        }\n        if (!transferIds || transferIds.length === 0) {\n            return {\n                data: [],\n                count: count || 0\n            };\n        }\n        // Get the actual transfer transactions\n        const uniqueTransferIds = [\n            ...new Set(transferIds.map((t)=>t.transfer_id))\n        ];\n        const { data: transactions, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").in('transfer_id', uniqueTransferIds).eq('user_id', user.id).eq('transaction_type', 'transfer');\n        if (transactionError) {\n            throw new Error(\"Failed to fetch transfer transactions: \".concat(transactionError.message));\n        }\n        // Group transactions by transfer_id and create transfer objects\n        const transferMap = new Map();\n        transactions === null || transactions === void 0 ? void 0 : transactions.forEach((transaction)=>{\n            if (transaction.transfer_id) {\n                if (!transferMap.has(transaction.transfer_id)) {\n                    transferMap.set(transaction.transfer_id, []);\n                }\n                transferMap.get(transaction.transfer_id).push(transaction);\n            }\n        });\n        const transfers = [];\n        transferMap.forEach((transactionPair, transferId)=>{\n            if (transactionPair.length === 2) {\n                // Find the outgoing transaction (the one with fees or the one from the source account)\n                const outgoing = transactionPair.find((t)=>t.fees && t.fees > 0) || transactionPair[0];\n                const incoming = transactionPair.find((t)=>t.id !== outgoing.id);\n                transfers.push({\n                    id: transferId,\n                    amount: outgoing.amount,\n                    description: outgoing.description,\n                    from_account_id: outgoing.account_id,\n                    to_account_id: outgoing.to_account_id,\n                    transfer_id: transferId,\n                    transaction_date: outgoing.transaction_date,\n                    fees: outgoing.fees,\n                    user_id: user.id,\n                    from_account: outgoing.account,\n                    to_account: outgoing.to_account\n                });\n            }\n        });\n        // Sort by transaction date\n        transfers.sort((a, b)=>new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime());\n        return {\n            data: transfers,\n            count: count || 0\n        };\n    }\n    /**\n   * Get a specific transfer by transfer ID\n   */ static async getTransfer(transferId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data: transactions, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").eq('transfer_id', transferId).eq('user_id', user.id).eq('transaction_type', 'transfer');\n        if (error) {\n            throw new Error(\"Failed to fetch transfer: \".concat(error.message));\n        }\n        if (!transactions || transactions.length !== 2) {\n            throw new Error('Transfer not found or incomplete');\n        }\n        // Find the outgoing transaction\n        const outgoing = transactions.find((t)=>t.fees && t.fees > 0) || transactions[0];\n        return {\n            id: transferId,\n            amount: outgoing.amount,\n            description: outgoing.description,\n            from_account_id: outgoing.account_id,\n            to_account_id: outgoing.to_account_id,\n            transfer_id: transferId,\n            transaction_date: outgoing.transaction_date,\n            fees: outgoing.fees,\n            user_id: user.id,\n            from_account: outgoing.account,\n            to_account: outgoing.to_account\n        };\n    }\n    /**\n   * Cancel a transfer (mark both transactions as cancelled)\n   */ static async cancelTransfer(transferId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').update({\n            transaction_status: 'cancelled',\n            updated_at: new Date().toISOString()\n        }).eq('transfer_id', transferId).eq('user_id', user.id).eq('transaction_type', 'transfer');\n        if (error) {\n            throw new Error(\"Failed to cancel transfer: \".concat(error.message));\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\n"));

/***/ })

});