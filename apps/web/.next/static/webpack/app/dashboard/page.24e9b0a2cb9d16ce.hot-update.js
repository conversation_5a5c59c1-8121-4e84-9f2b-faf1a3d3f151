"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/native.js":
/*!**********************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/native.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({ randomUUID });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL25hdGl2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxpRUFBZSxFQUFFLFlBQVksRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL25hdGl2ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCByYW5kb21VVUlEID0gdHlwZW9mIGNyeXB0byAhPT0gJ3VuZGVmaW5lZCcgJiYgY3J5cHRvLnJhbmRvbVVVSUQgJiYgY3J5cHRvLnJhbmRvbVVVSUQuYmluZChjcnlwdG8pO1xuZXhwb3J0IGRlZmF1bHQgeyByYW5kb21VVUlEIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/native.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/regex.js":
/*!*********************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/regex.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3JlZ2V4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjLEVBQUUsVUFBVSxFQUFFLGVBQWUsRUFBRSxnQkFBZ0IsRUFBRSxVQUFVLEdBQUcsOEVBQThFLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tYnJvd3Nlci9yZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAvXig/OlswLTlhLWZdezh9LVswLTlhLWZdezR9LVsxLThdWzAtOWEtZl17M30tWzg5YWJdWzAtOWEtZl17M30tWzAtOWEtZl17MTJ9fDAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMHxmZmZmZmZmZi1mZmZmLWZmZmYtZmZmZi1mZmZmZmZmZmZmZmYpJC9pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/regex.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/rng.js":
/*!*******************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/rng.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rng)\n/* harmony export */ });\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nfunction rng() {\n    if (!getRandomValues) {\n        if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\n            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n        }\n        getRandomValues = crypto.getRandomValues.bind(crypto);\n    }\n    return getRandomValues(rnds8);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3JuZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3JuZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgZ2V0UmFuZG9tVmFsdWVzO1xuY29uc3Qgcm5kczggPSBuZXcgVWludDhBcnJheSgxNik7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBybmcoKSB7XG4gICAgaWYgKCFnZXRSYW5kb21WYWx1ZXMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBjcnlwdG8gPT09ICd1bmRlZmluZWQnIHx8ICFjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2NyeXB0by5nZXRSYW5kb21WYWx1ZXMoKSBub3Qgc3VwcG9ydGVkLiBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3V1aWRqcy91dWlkI2dldHJhbmRvbXZhbHVlcy1ub3Qtc3VwcG9ydGVkJyk7XG4gICAgICAgIH1cbiAgICAgICAgZ2V0UmFuZG9tVmFsdWVzID0gY3J5cHRvLmdldFJhbmRvbVZhbHVlcy5iaW5kKGNyeXB0byk7XG4gICAgfVxuICAgIHJldHVybiBnZXRSYW5kb21WYWx1ZXMocm5kczgpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/rng.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/stringify.js":
/*!*************************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/stringify.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   unsafeStringify: () => (/* binding */ unsafeStringify)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/validate.js\");\n\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nfunction unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stringify);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/stringify.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/v4.js":
/*!******************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/v4.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _native_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./native.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/native.js\");\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rng.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stringify.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/stringify.js\");\n\n\n\nfunction v4(options, buf, offset) {\n    if (_native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID && !buf && !options) {\n        return _native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? (0,_rng_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.unsafeStringify)(rnds);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v4);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3Y0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUM7QUFDTjtBQUNzQjtBQUNqRDtBQUNBLFFBQVEsa0RBQU07QUFDZCxlQUFlLGtEQUFNO0FBQ3JCO0FBQ0E7QUFDQSxzREFBc0QsbURBQUc7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9EQUFvRCxPQUFPLEdBQUcsYUFBYTtBQUMzRTtBQUNBLHdCQUF3QixRQUFRO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyw4REFBZTtBQUMxQjtBQUNBLGlFQUFlLEVBQUUsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3Y0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBuYXRpdmUgZnJvbSAnLi9uYXRpdmUuanMnO1xuaW1wb3J0IHJuZyBmcm9tICcuL3JuZy5qcyc7XG5pbXBvcnQgeyB1bnNhZmVTdHJpbmdpZnkgfSBmcm9tICcuL3N0cmluZ2lmeS5qcyc7XG5mdW5jdGlvbiB2NChvcHRpb25zLCBidWYsIG9mZnNldCkge1xuICAgIGlmIChuYXRpdmUucmFuZG9tVVVJRCAmJiAhYnVmICYmICFvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiBuYXRpdmUucmFuZG9tVVVJRCgpO1xuICAgIH1cbiAgICBvcHRpb25zID0gb3B0aW9ucyB8fCB7fTtcbiAgICBjb25zdCBybmRzID0gb3B0aW9ucy5yYW5kb20gPz8gb3B0aW9ucy5ybmc/LigpID8/IHJuZygpO1xuICAgIGlmIChybmRzLmxlbmd0aCA8IDE2KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignUmFuZG9tIGJ5dGVzIGxlbmd0aCBtdXN0IGJlID49IDE2Jyk7XG4gICAgfVxuICAgIHJuZHNbNl0gPSAocm5kc1s2XSAmIDB4MGYpIHwgMHg0MDtcbiAgICBybmRzWzhdID0gKHJuZHNbOF0gJiAweDNmKSB8IDB4ODA7XG4gICAgaWYgKGJ1Zikge1xuICAgICAgICBvZmZzZXQgPSBvZmZzZXQgfHwgMDtcbiAgICAgICAgaWYgKG9mZnNldCA8IDAgfHwgb2Zmc2V0ICsgMTYgPiBidWYubGVuZ3RoKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgUmFuZ2VFcnJvcihgVVVJRCBieXRlIHJhbmdlICR7b2Zmc2V0fToke29mZnNldCArIDE1fSBpcyBvdXQgb2YgYnVmZmVyIGJvdW5kc2ApO1xuICAgICAgICB9XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgMTY7ICsraSkge1xuICAgICAgICAgICAgYnVmW29mZnNldCArIGldID0gcm5kc1tpXTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYnVmO1xuICAgIH1cbiAgICByZXR1cm4gdW5zYWZlU3RyaW5naWZ5KHJuZHMpO1xufVxuZXhwb3J0IGRlZmF1bHQgdjQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/v4.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/validate.js":
/*!************************************************************!*\
  !*** ../../node_modules/uuid/dist/esm-browser/validate.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.js */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/regex.js\");\n\nfunction validate(uuid) {\n    return typeof uuid === 'string' && _regex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].test(uuid);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validate);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3ZhbGlkYXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStCO0FBQy9CO0FBQ0EsdUNBQXVDLGlEQUFLO0FBQzVDO0FBQ0EsaUVBQWUsUUFBUSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdmFsaWRhdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJFR0VYIGZyb20gJy4vcmVnZXguanMnO1xuZnVuY3Rpb24gdmFsaWRhdGUodXVpZCkge1xuICAgIHJldHVybiB0eXBlb2YgdXVpZCA9PT0gJ3N0cmluZycgJiYgUkVHRVgudGVzdCh1dWlkKTtcbn1cbmV4cG9ydCBkZWZhdWx0IHZhbGlkYXRlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/validate.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/index.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/index.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccountService: () => (/* reexport safe */ _lib_accounts__WEBPACK_IMPORTED_MODULE_10__.AccountService),\n/* harmony export */   AnalyticsService: () => (/* reexport safe */ _lib_analytics__WEBPACK_IMPORTED_MODULE_7__.AnalyticsService),\n/* harmony export */   AssetClassService: () => (/* reexport safe */ _lib_assets__WEBPACK_IMPORTED_MODULE_15__.AssetClassService),\n/* harmony export */   AssetService: () => (/* reexport safe */ _lib_assets__WEBPACK_IMPORTED_MODULE_15__.AssetService),\n/* harmony export */   BiometricService: () => (/* reexport safe */ _lib_biometric_web__WEBPACK_IMPORTED_MODULE_18__.BiometricService),\n/* harmony export */   BudgetService: () => (/* reexport safe */ _lib_budget__WEBPACK_IMPORTED_MODULE_6__.BudgetService),\n/* harmony export */   CSVImportService: () => (/* reexport safe */ _lib_csv_import__WEBPACK_IMPORTED_MODULE_9__.CSVImportService),\n/* harmony export */   CategoryService: () => (/* reexport safe */ _lib_categories__WEBPACK_IMPORTED_MODULE_12__.CategoryService),\n/* harmony export */   Constants: () => (/* reexport safe */ _database_types__WEBPACK_IMPORTED_MODULE_3__.Constants),\n/* harmony export */   ExpenseService: () => (/* reexport safe */ _lib_expenses__WEBPACK_IMPORTED_MODULE_5__.ExpenseService),\n/* harmony export */   HoldingService: () => (/* reexport safe */ _lib_assets__WEBPACK_IMPORTED_MODULE_15__.HoldingService),\n/* harmony export */   InvestmentService: () => (/* reexport safe */ _lib_investments__WEBPACK_IMPORTED_MODULE_13__.InvestmentService),\n/* harmony export */   ProfitLossService: () => (/* reexport safe */ _lib_profit_loss__WEBPACK_IMPORTED_MODULE_16__.ProfitLossService),\n/* harmony export */   RecurringTransactionService: () => (/* reexport safe */ _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__.RecurringTransactionService),\n/* harmony export */   TaxCalculatorService: () => (/* reexport safe */ _lib_tax_calculator__WEBPACK_IMPORTED_MODULE_17__.TaxCalculatorService),\n/* harmony export */   TransactionService: () => (/* reexport safe */ _lib_transactions__WEBPACK_IMPORTED_MODULE_11__.TransactionService),\n/* harmony export */   TransferService: () => (/* reexport safe */ _lib_transfers__WEBPACK_IMPORTED_MODULE_14__.TransferService),\n/* harmony export */   accountSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.accountSchema),\n/* harmony export */   budgetFormInputSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_21__.budgetFormInputSchema),\n/* harmony export */   budgetFormSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_21__.budgetFormSchema),\n/* harmony export */   budgetSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_21__.budgetSchema),\n/* harmony export */   calculateBudgetProgress: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.calculateBudgetProgress),\n/* harmony export */   categorySchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.categorySchema),\n/* harmony export */   expenseFormInputSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_20__.expenseFormInputSchema),\n/* harmony export */   expenseFormSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_20__.expenseFormSchema),\n/* harmony export */   expenseSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_20__.expenseSchema),\n/* harmony export */   formatCurrency: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency),\n/* harmony export */   formatDate: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatDate),\n/* harmony export */   investmentSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.investmentSchema),\n/* harmony export */   legacyTransactionSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.legacyTransactionSchema),\n/* harmony export */   resetPasswordSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_19__.resetPasswordSchema),\n/* harmony export */   signInSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_19__.signInSchema),\n/* harmony export */   signUpSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_19__.signUpSchema),\n/* harmony export */   supabase: () => (/* reexport safe */ _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase),\n/* harmony export */   supabaseMobile: () => (/* reexport safe */ _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_23__.supabase),\n/* harmony export */   transferSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.transferSchema),\n/* harmony export */   useCurrencyStore: () => (/* reexport safe */ _stores_currencyStore__WEBPACK_IMPORTED_MODULE_22__.useCurrencyStore)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/../../packages/shared/src/types.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/../../packages/shared/src/utils.ts\");\n/* harmony import */ var _validators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./validators */ \"(app-pages-browser)/../../packages/shared/src/validators.ts\");\n/* harmony import */ var _database_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./database.types */ \"(app-pages-browser)/../../packages/shared/src/database.types.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _lib_expenses__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/expenses */ \"(app-pages-browser)/../../packages/shared/src/lib/expenses.ts\");\n/* harmony import */ var _lib_budget__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/budget */ \"(app-pages-browser)/../../packages/shared/src/lib/budget.ts\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/analytics */ \"(app-pages-browser)/../../packages/shared/src/lib/analytics.ts\");\n/* harmony import */ var _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/recurring-transactions */ \"(app-pages-browser)/../../packages/shared/src/lib/recurring-transactions.ts\");\n/* harmony import */ var _lib_csv_import__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/csv-import */ \"(app-pages-browser)/../../packages/shared/src/lib/csv-import.ts\");\n/* harmony import */ var _lib_accounts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./lib/accounts */ \"(app-pages-browser)/../../packages/shared/src/lib/accounts.ts\");\n/* harmony import */ var _lib_transactions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./lib/transactions */ \"(app-pages-browser)/../../packages/shared/src/lib/transactions.ts\");\n/* harmony import */ var _lib_categories__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./lib/categories */ \"(app-pages-browser)/../../packages/shared/src/lib/categories.ts\");\n/* harmony import */ var _lib_investments__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./lib/investments */ \"(app-pages-browser)/../../packages/shared/src/lib/investments.ts\");\n/* harmony import */ var _lib_transfers__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./lib/transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\");\n/* harmony import */ var _lib_assets__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./lib/assets */ \"(app-pages-browser)/../../packages/shared/src/lib/assets.ts\");\n/* harmony import */ var _lib_profit_loss__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./lib/profit-loss */ \"(app-pages-browser)/../../packages/shared/src/lib/profit-loss.ts\");\n/* harmony import */ var _lib_tax_calculator__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./lib/tax-calculator */ \"(app-pages-browser)/../../packages/shared/src/lib/tax-calculator.ts\");\n/* harmony import */ var _lib_biometric_web__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./lib/biometric.web */ \"(app-pages-browser)/../../packages/shared/src/lib/biometric.web.ts\");\n/* harmony import */ var _schemas_auth__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./schemas/auth */ \"(app-pages-browser)/../../packages/shared/src/schemas/auth.ts\");\n/* harmony import */ var _schemas_expense__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./schemas/expense */ \"(app-pages-browser)/../../packages/shared/src/schemas/expense.ts\");\n/* harmony import */ var _schemas_budget__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./schemas/budget */ \"(app-pages-browser)/../../packages/shared/src/schemas/budget.ts\");\n/* harmony import */ var _stores_currencyStore__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./stores/currencyStore */ \"(app-pages-browser)/../../packages/shared/src/stores/currencyStore.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./lib/supabase.mobile */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.mobile.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(_lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_23__);\n// Shared business logic and utilities\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Platform-specific biometric exports\n\n\n\n\n\n// Platform-specific exports (explicit re-export to avoid naming conflicts)\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsc0NBQXNDO0FBQ2Q7QUFDQTtBQUNLO0FBQ0k7QUFDRjtBQUNBO0FBQ0Y7QUFDRztBQUNhO0FBQ1o7QUFDRjtBQUNJO0FBQ0Y7QUFDQztBQUNGO0FBQ0g7QUFDSztBQUNHO0FBQ3JDLHNDQUFzQztBQUNGO0FBQ0w7QUFDRztBQUNEO0FBQ007QUFFdkMsMkVBQTJFO0FBQ1IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvcGFja2FnZXMvc2hhcmVkL3NyYy9pbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTaGFyZWQgYnVzaW5lc3MgbG9naWMgYW5kIHV0aWxpdGllc1xuZXhwb3J0ICogZnJvbSAnLi90eXBlcyc7XG5leHBvcnQgKiBmcm9tICcuL3V0aWxzJztcbmV4cG9ydCAqIGZyb20gJy4vdmFsaWRhdG9ycyc7XG5leHBvcnQgKiBmcm9tICcuL2RhdGFiYXNlLnR5cGVzJztcbmV4cG9ydCAqIGZyb20gJy4vbGliL3N1cGFiYXNlJztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2V4cGVuc2VzJztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2J1ZGdldCc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9hbmFseXRpY3MnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvcmVjdXJyaW5nLXRyYW5zYWN0aW9ucyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9jc3YtaW1wb3J0JztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2FjY291bnRzJztcbmV4cG9ydCAqIGZyb20gJy4vbGliL3RyYW5zYWN0aW9ucyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9jYXRlZ29yaWVzJztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2ludmVzdG1lbnRzJztcbmV4cG9ydCAqIGZyb20gJy4vbGliL3RyYW5zZmVycyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9hc3NldHMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvcHJvZml0LWxvc3MnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvdGF4LWNhbGN1bGF0b3InO1xuLy8gUGxhdGZvcm0tc3BlY2lmaWMgYmlvbWV0cmljIGV4cG9ydHNcbmV4cG9ydCAqIGZyb20gJy4vbGliL2Jpb21ldHJpYy53ZWInO1xuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWFzL2F1dGgnO1xuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWFzL2V4cGVuc2UnO1xuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWFzL2J1ZGdldCc7XG5leHBvcnQgKiBmcm9tICcuL3N0b3Jlcy9jdXJyZW5jeVN0b3JlJztcblxuLy8gUGxhdGZvcm0tc3BlY2lmaWMgZXhwb3J0cyAoZXhwbGljaXQgcmUtZXhwb3J0IHRvIGF2b2lkIG5hbWluZyBjb25mbGljdHMpXG5leHBvcnQgeyBzdXBhYmFzZSBhcyBzdXBhYmFzZU1vYmlsZSB9IGZyb20gJy4vbGliL3N1cGFiYXNlLm1vYmlsZSc7Il0sIm5hbWVzIjpbInN1cGFiYXNlIiwic3VwYWJhc2VNb2JpbGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/accounts.ts":
/*!*************************************************!*\
  !*** ../../packages/shared/src/lib/accounts.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccountService: () => (/* binding */ AccountService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n\nclass AccountService {\n    /**\n   * Get all accounts for the current user\n   */ static async getAccounts(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('user_id', user.id).order('is_primary', {\n            ascending: false\n        }).order('account_type').order('name');\n        if (options === null || options === void 0 ? void 0 : options.account_type) {\n            query = query.eq('account_type', options.account_type);\n        }\n        if ((options === null || options === void 0 ? void 0 : options.is_active) !== undefined) {\n            query = query.eq('is_active', options.is_active);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch accounts: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get a specific account by ID\n   */ static async getAccount(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            throw new Error(\"Failed to fetch account: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Create a new account\n   */ static async createAccount(accountData) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if account name already exists for this user\n        const { data: existingAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('id').eq('user_id', user.id).eq('name', accountData.name).single();\n        if (existingAccount) {\n            throw new Error('Account name already exists');\n        }\n        // If this is set as primary, unset other primary accounts of the same type\n        if (accountData.is_primary) {\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').update({\n                is_primary: false\n            }).eq('user_id', user.id).eq('account_type', accountData.account_type);\n        }\n        const insertData = {\n            ...accountData,\n            user_id: user.id,\n            currency: accountData.currency || 'USD',\n            current_balance: accountData.current_balance || 0,\n            is_active: true,\n            is_primary: accountData.is_primary || false\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').insert(insertData).select('*').single();\n        if (error) {\n            throw new Error(\"Failed to create account: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update an existing account\n   */ static async updateAccount(id, updates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // If updating to primary, unset other primary accounts of the same type\n        if (updates.is_primary) {\n            const account = await this.getAccount(id);\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').update({\n                is_primary: false\n            }).eq('user_id', user.id).eq('account_type', account.account_type).neq('id', id);\n        }\n        const updateData = {\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').update(updateData).eq('id', id).eq('user_id', user.id).select('*').single();\n        if (error) {\n            throw new Error(\"Failed to update account: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Delete an account (soft delete by setting is_active to false)\n   */ static async deleteAccount(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if account has transactions\n        const { data: transactions } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('id').or(\"account_id.eq.\".concat(id, \",to_account_id.eq.\").concat(id)).limit(1);\n        if (transactions && transactions.length > 0) {\n            // Soft delete if account has transactions\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').update({\n                is_active: false,\n                updated_at: new Date().toISOString()\n            }).eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to deactivate account: \".concat(error.message));\n            }\n        } else {\n            // Hard delete if no transactions\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').delete().eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete account: \".concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get account balance history\n   */ static async getAccountBalanceHistory(accountId, options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('account_balance_history').select(\"\\n        *,\\n        transaction:transactions(*)\\n      \").eq('account_id', accountId).order('balance_date', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('balance_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('balance_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch balance history: \".concat(error.message));\n        }\n        return data || [];\n    }\n    /**\n   * Create default accounts for a new user\n   */ static async createDefaultAccounts() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const defaultAccounts = [\n            {\n                name: 'Cash',\n                account_type: 'cash',\n                currency: 'USD',\n                current_balance: 0,\n                is_primary: true\n            }\n        ];\n        const createdAccounts = [];\n        for (const accountData of defaultAccounts){\n            try {\n                const account = await this.createAccount(accountData);\n                createdAccounts.push(account);\n            } catch (error) {\n                console.error('Failed to create default account:', error);\n            }\n        }\n        return createdAccounts;\n    }\n    /**\n   * Get account summary with balances by type\n   */ static async getAccountSummary() {\n        const accounts = await this.getAccounts({\n            is_active: true\n        });\n        let totalAssets = 0;\n        let totalLiabilities = 0;\n        const accountsByType = {\n            bank: {\n                count: 0,\n                balance: 0\n            },\n            investment: {\n                count: 0,\n                balance: 0\n            },\n            savings: {\n                count: 0,\n                balance: 0\n            },\n            credit_card: {\n                count: 0,\n                balance: 0\n            },\n            cash: {\n                count: 0,\n                balance: 0\n            }\n        };\n        accounts.forEach((account)=>{\n            const balance = account.current_balance || 0;\n            accountsByType[account.account_type].count++;\n            accountsByType[account.account_type].balance += balance;\n            if (account.account_type === 'credit_card') {\n                // Credit card balances are liabilities (negative is debt)\n                totalLiabilities += Math.abs(balance);\n            } else {\n                totalAssets += balance;\n            }\n        });\n        return {\n            totalAssets,\n            totalLiabilities,\n            netWorth: totalAssets - totalLiabilities,\n            accountsByType\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/accounts.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/categories.ts":
/*!***************************************************!*\
  !*** ../../packages/shared/src/lib/categories.ts ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoryService: () => (/* binding */ CategoryService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n\nclass CategoryService {\n    /**\n   * Get all categories for the current user (including system categories)\n   */ static async getCategories(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select(\"\\n        *,\\n        parent_category:categories!categories_parent_category_id_fkey(*),\\n        subcategories:categories!categories_parent_category_id_fkey(*)\\n      \").or(\"user_id.eq.\".concat(user.id, \",and(is_system.eq.true,user_id.is.null)\")).order('sort_order').order('name');\n        if (options === null || options === void 0 ? void 0 : options.type) {\n            query = query.eq('type', options.type);\n        }\n        if ((options === null || options === void 0 ? void 0 : options.is_active) !== undefined) {\n            query = query.eq('is_active', options.is_active);\n        }\n        if ((options === null || options === void 0 ? void 0 : options.include_system) === false) {\n            query = query.eq('is_system', false);\n        }\n        if ((options === null || options === void 0 ? void 0 : options.parent_id) !== undefined) {\n            if (options.parent_id === null) {\n                query = query.is('parent_category_id', null);\n            } else {\n                query = query.eq('parent_category_id', options.parent_id);\n            }\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch categories: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get a specific category by ID\n   */ static async getCategory(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select(\"\\n        *,\\n        parent_category:categories!categories_parent_category_id_fkey(*),\\n        subcategories:categories!categories_parent_category_id_fkey(*)\\n      \").eq('id', id).or(\"user_id.eq.\".concat(user.id, \",and(is_system.eq.true,user_id.is.null)\")).single();\n        if (error) {\n            throw new Error(\"Failed to fetch category: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Create a new category\n   */ static async createCategory(categoryData) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if category name already exists for this user\n        const { data: existingCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('user_id', user.id).eq('name', categoryData.name).single();\n        if (existingCategory) {\n            throw new Error('Category name already exists');\n        }\n        // Validate parent category if specified\n        if (categoryData.parent_category_id) {\n            const { data: parentCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id, user_id, is_system').eq('id', categoryData.parent_category_id).single();\n            if (!parentCategory || parentCategory.user_id !== user.id && !parentCategory.is_system) {\n                throw new Error('Invalid parent category specified');\n            }\n        }\n        const insertData = {\n            ...categoryData,\n            user_id: user.id,\n            is_default: false,\n            is_system: false,\n            is_active: true,\n            sort_order: categoryData.sort_order || 0\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').insert(insertData).select(\"\\n        *,\\n        parent_category:categories!categories_parent_category_id_fkey(*),\\n        subcategories:categories!categories_parent_category_id_fkey(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to create category: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update an existing category\n   */ static async updateCategory(id, updates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if category exists and belongs to user (can't update system categories)\n        const { data: existingCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('*').eq('id', id).eq('user_id', user.id).eq('is_system', false).single();\n        if (!existingCategory) {\n            throw new Error('Category not found or cannot be modified');\n        }\n        // Check for name conflicts if name is being updated\n        if (updates.name && updates.name !== existingCategory.name) {\n            const { data: nameConflict } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('user_id', user.id).eq('name', updates.name).neq('id', id).single();\n            if (nameConflict) {\n                throw new Error('Category name already exists');\n            }\n        }\n        // Validate parent category if being updated\n        if (updates.parent_category_id) {\n            const { data: parentCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id, user_id, is_system').eq('id', updates.parent_category_id).single();\n            if (!parentCategory || parentCategory.user_id !== user.id && !parentCategory.is_system) {\n                throw new Error('Invalid parent category specified');\n            }\n            // Prevent circular references\n            if (updates.parent_category_id === id) {\n                throw new Error('Category cannot be its own parent');\n            }\n        }\n        const updateData = {\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').update(updateData).eq('id', id).eq('user_id', user.id).select(\"\\n        *,\\n        parent_category:categories!categories_parent_category_id_fkey(*),\\n        subcategories:categories!categories_parent_category_id_fkey(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to update category: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Delete a category (soft delete by setting is_active to false)\n   */ static async deleteCategory(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if category exists and belongs to user (can't delete system categories)\n        const { data: category } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('*').eq('id', id).eq('user_id', user.id).eq('is_system', false).single();\n        if (!category) {\n            throw new Error('Category not found or cannot be deleted');\n        }\n        // Check if category has transactions\n        const { data: transactions } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('id').eq('category_id', id).limit(1);\n        // Check if category has subcategories\n        const { data: subcategories } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('parent_category_id', id).eq('is_active', true).limit(1);\n        if (transactions && transactions.length > 0) {\n            // Soft delete if category has transactions\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').update({\n                is_active: false,\n                updated_at: new Date().toISOString()\n            }).eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to deactivate category: \".concat(error.message));\n            }\n        } else if (subcategories && subcategories.length > 0) {\n            throw new Error('Cannot delete category with active subcategories');\n        } else {\n            // Hard delete if no transactions or subcategories\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').delete().eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete category: \".concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get category hierarchy (parent categories with their subcategories)\n   */ static async getCategoryHierarchy(type) {\n        const categories = await this.getCategories({\n            type,\n            is_active: true,\n            include_system: true\n        });\n        // Build hierarchy\n        const categoryMap = new Map();\n        const rootCategories = [];\n        // First pass: create map and identify root categories\n        categories.forEach((category)=>{\n            categoryMap.set(category.id, {\n                ...category,\n                subcategories: []\n            });\n            if (!category.parent_category_id) {\n                rootCategories.push(categoryMap.get(category.id));\n            }\n        });\n        // Second pass: build parent-child relationships\n        categories.forEach((category)=>{\n            if (category.parent_category_id) {\n                const parent = categoryMap.get(category.parent_category_id);\n                const child = categoryMap.get(category.id);\n                if (parent && child) {\n                    parent.subcategories = parent.subcategories || [];\n                    parent.subcategories.push(child);\n                }\n            }\n        });\n        return rootCategories;\n    }\n    /**\n   * Get category usage statistics\n   */ static async getCategoryStats(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        category_id,\\n        amount,\\n        category:categories(*)\\n      \").eq('user_id', user.id).not('category_id', 'is', null);\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch category statistics: \".concat(error.message));\n        }\n        // Group by category and calculate stats\n        const statsMap = new Map();\n        data === null || data === void 0 ? void 0 : data.forEach((transaction)=>{\n            if (transaction.category) {\n                const categoryId = transaction.category_id;\n                const existing = statsMap.get(categoryId) || {\n                    category: transaction.category,\n                    transactionCount: 0,\n                    totalAmount: 0\n                };\n                existing.transactionCount++;\n                existing.totalAmount += transaction.amount;\n                statsMap.set(categoryId, existing);\n            }\n        });\n        // Convert to array and add average\n        return Array.from(statsMap.values()).map((stat)=>({\n                ...stat,\n                averageAmount: stat.transactionCount > 0 ? stat.totalAmount / stat.transactionCount : 0\n            })).sort((a, b)=>b.totalAmount - a.totalAmount);\n    }\n    /**\n   * Reorder categories\n   */ static async reorderCategories(categoryOrders) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Update sort orders in batch\n        for (const { id, sort_order } of categoryOrders){\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').update({\n                sort_order,\n                updated_at: new Date().toISOString()\n            }).eq('id', id).eq('user_id', user.id).eq('is_system', false) // Only allow reordering user categories\n            ;\n            if (error) {\n                console.error(\"Failed to update sort order for category \".concat(id, \":\"), error);\n            }\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL2xpYi9jYXRlZ29yaWVzLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFDO0FBSTlCLE1BQU1DO0lBQ1g7O0dBRUMsR0FDRCxhQUFhQyxjQUFjQyxPQUsxQixFQUF3QjtRQUN2QixNQUFNLEVBQUVDLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUUsR0FBRyxNQUFNTCwrQ0FBUUEsQ0FBQ00sSUFBSSxDQUFDQyxPQUFPO1FBRXRELElBQUksQ0FBQ0YsTUFBTTtZQUNULE1BQU0sSUFBSUcsTUFBTTtRQUNsQjtRQUVBLElBQUlDLFFBQVFULCtDQUFRQSxDQUNqQlUsSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBRSwyS0FLUkMsRUFBRSxDQUFDLGNBQXNCLE9BQVJQLEtBQUtRLEVBQUUsRUFBQyw0Q0FDekJDLEtBQUssQ0FBQyxjQUNOQSxLQUFLLENBQUM7UUFFVCxJQUFJWCxvQkFBQUEsOEJBQUFBLFFBQVNZLElBQUksRUFBRTtZQUNqQk4sUUFBUUEsTUFBTU8sRUFBRSxDQUFDLFFBQVFiLFFBQVFZLElBQUk7UUFDdkM7UUFFQSxJQUFJWixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNjLFNBQVMsTUFBS0MsV0FBVztZQUNwQ1QsUUFBUUEsTUFBTU8sRUFBRSxDQUFDLGFBQWFiLFFBQVFjLFNBQVM7UUFDakQ7UUFFQSxJQUFJZCxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNnQixjQUFjLE1BQUssT0FBTztZQUNyQ1YsUUFBUUEsTUFBTU8sRUFBRSxDQUFDLGFBQWE7UUFDaEM7UUFFQSxJQUFJYixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNpQixTQUFTLE1BQUtGLFdBQVc7WUFDcEMsSUFBSWYsUUFBUWlCLFNBQVMsS0FBSyxNQUFNO2dCQUM5QlgsUUFBUUEsTUFBTVksRUFBRSxDQUFDLHNCQUFzQjtZQUN6QyxPQUFPO2dCQUNMWixRQUFRQSxNQUFNTyxFQUFFLENBQUMsc0JBQXNCYixRQUFRaUIsU0FBUztZQUMxRDtRQUNGO1FBRUEsTUFBTSxFQUFFaEIsSUFBSSxFQUFFa0IsS0FBSyxFQUFFLEdBQUcsTUFBTWI7UUFFOUIsSUFBSWEsT0FBTztZQUNULE1BQU0sSUFBSWQsTUFBTSwrQkFBNkMsT0FBZGMsTUFBTUMsT0FBTztRQUM5RDtRQUVBLE9BQU9uQjtJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhb0IsWUFBWVgsRUFBVSxFQUFzQjtRQUN2RCxNQUFNLEVBQUVULE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUUsR0FBRyxNQUFNTCwrQ0FBUUEsQ0FBQ00sSUFBSSxDQUFDQyxPQUFPO1FBRXRELElBQUksQ0FBQ0YsTUFBTTtZQUNULE1BQU0sSUFBSUcsTUFBTTtRQUNsQjtRQUVBLE1BQU0sRUFBRUosSUFBSSxFQUFFa0IsS0FBSyxFQUFFLEdBQUcsTUFBTXRCLCtDQUFRQSxDQUNuQ1UsSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBRSwyS0FLUkssRUFBRSxDQUFDLE1BQU1ILElBQ1RELEVBQUUsQ0FBQyxjQUFzQixPQUFSUCxLQUFLUSxFQUFFLEVBQUMsNENBQ3pCWSxNQUFNO1FBRVQsSUFBSUgsT0FBTztZQUNULE1BQU0sSUFBSWQsTUFBTSw2QkFBMkMsT0FBZGMsTUFBTUMsT0FBTztRQUM1RDtRQUVBLE9BQU9uQjtJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhc0IsZUFBZUMsWUFBMkIsRUFBc0I7UUFDM0UsTUFBTSxFQUFFdkIsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUFDTSxJQUFJLENBQUNDLE9BQU87UUFFdEQsSUFBSSxDQUFDRixNQUFNO1lBQ1QsTUFBTSxJQUFJRyxNQUFNO1FBQ2xCO1FBRUEsc0RBQXNEO1FBQ3RELE1BQU0sRUFBRUosTUFBTXdCLGdCQUFnQixFQUFFLEdBQUcsTUFBTTVCLCtDQUFRQSxDQUM5Q1UsSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBQyxNQUNQSyxFQUFFLENBQUMsV0FBV1gsS0FBS1EsRUFBRSxFQUNyQkcsRUFBRSxDQUFDLFFBQVFXLGFBQWFFLElBQUksRUFDNUJKLE1BQU07UUFFVCxJQUFJRyxrQkFBa0I7WUFDcEIsTUFBTSxJQUFJcEIsTUFBTTtRQUNsQjtRQUVBLHdDQUF3QztRQUN4QyxJQUFJbUIsYUFBYUcsa0JBQWtCLEVBQUU7WUFDbkMsTUFBTSxFQUFFMUIsTUFBTTJCLGNBQWMsRUFBRSxHQUFHLE1BQU0vQiwrQ0FBUUEsQ0FDNUNVLElBQUksQ0FBQyxjQUNMQyxNQUFNLENBQUMsMEJBQ1BLLEVBQUUsQ0FBQyxNQUFNVyxhQUFhRyxrQkFBa0IsRUFDeENMLE1BQU07WUFFVCxJQUFJLENBQUNNLGtCQUFtQkEsZUFBZUMsT0FBTyxLQUFLM0IsS0FBS1EsRUFBRSxJQUFJLENBQUNrQixlQUFlRSxTQUFTLEVBQUc7Z0JBQ3hGLE1BQU0sSUFBSXpCLE1BQU07WUFDbEI7UUFDRjtRQUVBLE1BQU0wQixhQUF5QztZQUM3QyxHQUFHUCxZQUFZO1lBQ2ZLLFNBQVMzQixLQUFLUSxFQUFFO1lBQ2hCc0IsWUFBWTtZQUNaRixXQUFXO1lBQ1hoQixXQUFXO1lBQ1htQixZQUFZVCxhQUFhUyxVQUFVLElBQUk7UUFDekM7UUFFQSxNQUFNLEVBQUVoQyxJQUFJLEVBQUVrQixLQUFLLEVBQUUsR0FBRyxNQUFNdEIsK0NBQVFBLENBQ25DVSxJQUFJLENBQUMsY0FDTDJCLE1BQU0sQ0FBQ0gsWUFDUHZCLE1BQU0sQ0FBRSwyS0FLUmMsTUFBTTtRQUVULElBQUlILE9BQU87WUFDVCxNQUFNLElBQUlkLE1BQU0sOEJBQTRDLE9BQWRjLE1BQU1DLE9BQU87UUFDN0Q7UUFFQSxPQUFPbkI7SUFDVDtJQUVBOztHQUVDLEdBQ0QsYUFBYWtDLGVBQWV6QixFQUFVLEVBQUUwQixPQUErQixFQUFzQjtRQUMzRixNQUFNLEVBQUVuQyxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTUwsK0NBQVFBLENBQUNNLElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVCxNQUFNLElBQUlHLE1BQU07UUFDbEI7UUFFQSxnRkFBZ0Y7UUFDaEYsTUFBTSxFQUFFSixNQUFNd0IsZ0JBQWdCLEVBQUUsR0FBRyxNQUFNNUIsK0NBQVFBLENBQzlDVSxJQUFJLENBQUMsY0FDTEMsTUFBTSxDQUFDLEtBQ1BLLEVBQUUsQ0FBQyxNQUFNSCxJQUNURyxFQUFFLENBQUMsV0FBV1gsS0FBS1EsRUFBRSxFQUNyQkcsRUFBRSxDQUFDLGFBQWEsT0FDaEJTLE1BQU07UUFFVCxJQUFJLENBQUNHLGtCQUFrQjtZQUNyQixNQUFNLElBQUlwQixNQUFNO1FBQ2xCO1FBRUEsb0RBQW9EO1FBQ3BELElBQUkrQixRQUFRVixJQUFJLElBQUlVLFFBQVFWLElBQUksS0FBS0QsaUJBQWlCQyxJQUFJLEVBQUU7WUFDMUQsTUFBTSxFQUFFekIsTUFBTW9DLFlBQVksRUFBRSxHQUFHLE1BQU14QywrQ0FBUUEsQ0FDMUNVLElBQUksQ0FBQyxjQUNMQyxNQUFNLENBQUMsTUFDUEssRUFBRSxDQUFDLFdBQVdYLEtBQUtRLEVBQUUsRUFDckJHLEVBQUUsQ0FBQyxRQUFRdUIsUUFBUVYsSUFBSSxFQUN2QlksR0FBRyxDQUFDLE1BQU01QixJQUNWWSxNQUFNO1lBRVQsSUFBSWUsY0FBYztnQkFDaEIsTUFBTSxJQUFJaEMsTUFBTTtZQUNsQjtRQUNGO1FBRUEsNENBQTRDO1FBQzVDLElBQUkrQixRQUFRVCxrQkFBa0IsRUFBRTtZQUM5QixNQUFNLEVBQUUxQixNQUFNMkIsY0FBYyxFQUFFLEdBQUcsTUFBTS9CLCtDQUFRQSxDQUM1Q1UsSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBQywwQkFDUEssRUFBRSxDQUFDLE1BQU11QixRQUFRVCxrQkFBa0IsRUFDbkNMLE1BQU07WUFFVCxJQUFJLENBQUNNLGtCQUFtQkEsZUFBZUMsT0FBTyxLQUFLM0IsS0FBS1EsRUFBRSxJQUFJLENBQUNrQixlQUFlRSxTQUFTLEVBQUc7Z0JBQ3hGLE1BQU0sSUFBSXpCLE1BQU07WUFDbEI7WUFFQSw4QkFBOEI7WUFDOUIsSUFBSStCLFFBQVFULGtCQUFrQixLQUFLakIsSUFBSTtnQkFDckMsTUFBTSxJQUFJTCxNQUFNO1lBQ2xCO1FBQ0Y7UUFFQSxNQUFNa0MsYUFBeUM7WUFDN0MsR0FBR0gsT0FBTztZQUNWSSxZQUFZLElBQUlDLE9BQU9DLFdBQVc7UUFDcEM7UUFFQSxNQUFNLEVBQUV6QyxJQUFJLEVBQUVrQixLQUFLLEVBQUUsR0FBRyxNQUFNdEIsK0NBQVFBLENBQ25DVSxJQUFJLENBQUMsY0FDTG9DLE1BQU0sQ0FBQ0osWUFDUDFCLEVBQUUsQ0FBQyxNQUFNSCxJQUNURyxFQUFFLENBQUMsV0FBV1gsS0FBS1EsRUFBRSxFQUNyQkYsTUFBTSxDQUFFLDJLQUtSYyxNQUFNO1FBRVQsSUFBSUgsT0FBTztZQUNULE1BQU0sSUFBSWQsTUFBTSw4QkFBNEMsT0FBZGMsTUFBTUMsT0FBTztRQUM3RDtRQUVBLE9BQU9uQjtJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhMkMsZUFBZWxDLEVBQVUsRUFBaUI7UUFDckQsTUFBTSxFQUFFVCxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTUwsK0NBQVFBLENBQUNNLElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVCxNQUFNLElBQUlHLE1BQU07UUFDbEI7UUFFQSxnRkFBZ0Y7UUFDaEYsTUFBTSxFQUFFSixNQUFNNEMsUUFBUSxFQUFFLEdBQUcsTUFBTWhELCtDQUFRQSxDQUN0Q1UsSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBQyxLQUNQSyxFQUFFLENBQUMsTUFBTUgsSUFDVEcsRUFBRSxDQUFDLFdBQVdYLEtBQUtRLEVBQUUsRUFDckJHLEVBQUUsQ0FBQyxhQUFhLE9BQ2hCUyxNQUFNO1FBRVQsSUFBSSxDQUFDdUIsVUFBVTtZQUNiLE1BQU0sSUFBSXhDLE1BQU07UUFDbEI7UUFFQSxxQ0FBcUM7UUFDckMsTUFBTSxFQUFFSixNQUFNNkMsWUFBWSxFQUFFLEdBQUcsTUFBTWpELCtDQUFRQSxDQUMxQ1UsSUFBSSxDQUFDLGdCQUNMQyxNQUFNLENBQUMsTUFDUEssRUFBRSxDQUFDLGVBQWVILElBQ2xCcUMsS0FBSyxDQUFDO1FBRVQsc0NBQXNDO1FBQ3RDLE1BQU0sRUFBRTlDLE1BQU0rQyxhQUFhLEVBQUUsR0FBRyxNQUFNbkQsK0NBQVFBLENBQzNDVSxJQUFJLENBQUMsY0FDTEMsTUFBTSxDQUFDLE1BQ1BLLEVBQUUsQ0FBQyxzQkFBc0JILElBQ3pCRyxFQUFFLENBQUMsYUFBYSxNQUNoQmtDLEtBQUssQ0FBQztRQUVULElBQUlELGdCQUFnQkEsYUFBYUcsTUFBTSxHQUFHLEdBQUc7WUFDM0MsMkNBQTJDO1lBQzNDLE1BQU0sRUFBRTlCLEtBQUssRUFBRSxHQUFHLE1BQU10QiwrQ0FBUUEsQ0FDN0JVLElBQUksQ0FBQyxjQUNMb0MsTUFBTSxDQUFDO2dCQUNON0IsV0FBVztnQkFDWDBCLFlBQVksSUFBSUMsT0FBT0MsV0FBVztZQUNwQyxHQUNDN0IsRUFBRSxDQUFDLE1BQU1ILElBQ1RHLEVBQUUsQ0FBQyxXQUFXWCxLQUFLUSxFQUFFO1lBRXhCLElBQUlTLE9BQU87Z0JBQ1QsTUFBTSxJQUFJZCxNQUFNLGtDQUFnRCxPQUFkYyxNQUFNQyxPQUFPO1lBQ2pFO1FBQ0YsT0FBTyxJQUFJNEIsaUJBQWlCQSxjQUFjQyxNQUFNLEdBQUcsR0FBRztZQUNwRCxNQUFNLElBQUk1QyxNQUFNO1FBQ2xCLE9BQU87WUFDTCxrREFBa0Q7WUFDbEQsTUFBTSxFQUFFYyxLQUFLLEVBQUUsR0FBRyxNQUFNdEIsK0NBQVFBLENBQzdCVSxJQUFJLENBQUMsY0FDTDJDLE1BQU0sR0FDTnJDLEVBQUUsQ0FBQyxNQUFNSCxJQUNURyxFQUFFLENBQUMsV0FBV1gsS0FBS1EsRUFBRTtZQUV4QixJQUFJUyxPQUFPO2dCQUNULE1BQU0sSUFBSWQsTUFBTSw4QkFBNEMsT0FBZGMsTUFBTUMsT0FBTztZQUM3RDtRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWErQixxQkFBcUJ2QyxJQUEyQixFQUF3QjtRQUNuRixNQUFNd0MsYUFBYSxNQUFNLElBQUksQ0FBQ3JELGFBQWEsQ0FBQztZQUMxQ2E7WUFDQUUsV0FBVztZQUNYRSxnQkFBZ0I7UUFDbEI7UUFFQSxrQkFBa0I7UUFDbEIsTUFBTXFDLGNBQWMsSUFBSUM7UUFDeEIsTUFBTUMsaUJBQThCLEVBQUU7UUFFdEMsc0RBQXNEO1FBQ3RESCxXQUFXSSxPQUFPLENBQUNYLENBQUFBO1lBQ2pCUSxZQUFZSSxHQUFHLENBQUNaLFNBQVNuQyxFQUFFLEVBQUU7Z0JBQUUsR0FBR21DLFFBQVE7Z0JBQUVHLGVBQWUsRUFBRTtZQUFDO1lBQzlELElBQUksQ0FBQ0gsU0FBU2xCLGtCQUFrQixFQUFFO2dCQUNoQzRCLGVBQWVHLElBQUksQ0FBQ0wsWUFBWU0sR0FBRyxDQUFDZCxTQUFTbkMsRUFBRTtZQUNqRDtRQUNGO1FBRUEsZ0RBQWdEO1FBQ2hEMEMsV0FBV0ksT0FBTyxDQUFDWCxDQUFBQTtZQUNqQixJQUFJQSxTQUFTbEIsa0JBQWtCLEVBQUU7Z0JBQy9CLE1BQU1pQyxTQUFTUCxZQUFZTSxHQUFHLENBQUNkLFNBQVNsQixrQkFBa0I7Z0JBQzFELE1BQU1rQyxRQUFRUixZQUFZTSxHQUFHLENBQUNkLFNBQVNuQyxFQUFFO2dCQUN6QyxJQUFJa0QsVUFBVUMsT0FBTztvQkFDbkJELE9BQU9aLGFBQWEsR0FBR1ksT0FBT1osYUFBYSxJQUFJLEVBQUU7b0JBQ2pEWSxPQUFPWixhQUFhLENBQUNVLElBQUksQ0FBQ0c7Z0JBQzVCO1lBQ0Y7UUFDRjtRQUVBLE9BQU9OO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELGFBQWFPLGlCQUFpQjlELE9BRzdCLEVBS0c7UUFDRixNQUFNLEVBQUVDLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUUsR0FBRyxNQUFNTCwrQ0FBUUEsQ0FBQ00sSUFBSSxDQUFDQyxPQUFPO1FBRXRELElBQUksQ0FBQ0YsTUFBTTtZQUNULE1BQU0sSUFBSUcsTUFBTTtRQUNsQjtRQUVBLElBQUlDLFFBQVFULCtDQUFRQSxDQUNqQlUsSUFBSSxDQUFDLGdCQUNMQyxNQUFNLENBQUUsbUZBS1JLLEVBQUUsQ0FBQyxXQUFXWCxLQUFLUSxFQUFFLEVBQ3JCcUQsR0FBRyxDQUFDLGVBQWUsTUFBTTtRQUU1QixJQUFJL0Qsb0JBQUFBLDhCQUFBQSxRQUFTZ0UsU0FBUyxFQUFFO1lBQ3RCMUQsUUFBUUEsTUFBTTJELEdBQUcsQ0FBQyxvQkFBb0JqRSxRQUFRZ0UsU0FBUztRQUN6RDtRQUVBLElBQUloRSxvQkFBQUEsOEJBQUFBLFFBQVNrRSxPQUFPLEVBQUU7WUFDcEI1RCxRQUFRQSxNQUFNNkQsR0FBRyxDQUFDLG9CQUFvQm5FLFFBQVFrRSxPQUFPO1FBQ3ZEO1FBRUEsTUFBTSxFQUFFakUsSUFBSSxFQUFFa0IsS0FBSyxFQUFFLEdBQUcsTUFBTWI7UUFFOUIsSUFBSWEsT0FBTztZQUNULE1BQU0sSUFBSWQsTUFBTSx3Q0FBc0QsT0FBZGMsTUFBTUMsT0FBTztRQUN2RTtRQUVBLHdDQUF3QztRQUN4QyxNQUFNZ0QsV0FBVyxJQUFJZDtRQU1yQnJELGlCQUFBQSwyQkFBQUEsS0FBTXVELE9BQU8sQ0FBQ2EsQ0FBQUE7WUFDWixJQUFJQSxZQUFZeEIsUUFBUSxFQUFFO2dCQUN4QixNQUFNeUIsYUFBYUQsWUFBWUUsV0FBVztnQkFDMUMsTUFBTUMsV0FBV0osU0FBU1QsR0FBRyxDQUFDVyxlQUFlO29CQUMzQ3pCLFVBQVV3QixZQUFZeEIsUUFBUTtvQkFDOUI0QixrQkFBa0I7b0JBQ2xCQyxhQUFhO2dCQUNmO2dCQUVBRixTQUFTQyxnQkFBZ0I7Z0JBQ3pCRCxTQUFTRSxXQUFXLElBQUlMLFlBQVlNLE1BQU07Z0JBRTFDUCxTQUFTWCxHQUFHLENBQUNhLFlBQVlFO1lBQzNCO1FBQ0Y7UUFFQSxtQ0FBbUM7UUFDbkMsT0FBT0ksTUFBTXJFLElBQUksQ0FBQzZELFNBQVNTLE1BQU0sSUFBSUMsR0FBRyxDQUFDQyxDQUFBQSxPQUFTO2dCQUNoRCxHQUFHQSxJQUFJO2dCQUNQQyxlQUFlRCxLQUFLTixnQkFBZ0IsR0FBRyxJQUFJTSxLQUFLTCxXQUFXLEdBQUdLLEtBQUtOLGdCQUFnQixHQUFHO1lBQ3hGLElBQUlRLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxFQUFFVCxXQUFXLEdBQUdRLEVBQUVSLFdBQVc7SUFDbEQ7SUFFQTs7R0FFQyxHQUNELGFBQWFVLGtCQUFrQkMsY0FBb0QsRUFBaUI7UUFDbEcsTUFBTSxFQUFFcEYsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUFDTSxJQUFJLENBQUNDLE9BQU87UUFFdEQsSUFBSSxDQUFDRixNQUFNO1lBQ1QsTUFBTSxJQUFJRyxNQUFNO1FBQ2xCO1FBRUEsOEJBQThCO1FBQzlCLEtBQUssTUFBTSxFQUFFSyxFQUFFLEVBQUV1QixVQUFVLEVBQUUsSUFBSW9ELGVBQWdCO1lBQy9DLE1BQU0sRUFBRWxFLEtBQUssRUFBRSxHQUFHLE1BQU10QiwrQ0FBUUEsQ0FDN0JVLElBQUksQ0FBQyxjQUNMb0MsTUFBTSxDQUFDO2dCQUNOVjtnQkFDQU8sWUFBWSxJQUFJQyxPQUFPQyxXQUFXO1lBQ3BDLEdBQ0M3QixFQUFFLENBQUMsTUFBTUgsSUFDVEcsRUFBRSxDQUFDLFdBQVdYLEtBQUtRLEVBQUUsRUFDckJHLEVBQUUsQ0FBQyxhQUFhLE9BQU8sd0NBQXdDOztZQUVsRSxJQUFJTSxPQUFPO2dCQUNUbUUsUUFBUW5FLEtBQUssQ0FBQyw0Q0FBK0MsT0FBSFQsSUFBRyxNQUFJUztZQUNuRTtRQUNGO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9wYWNrYWdlcy9zaGFyZWQvc3JjL2xpYi9jYXRlZ29yaWVzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN1cGFiYXNlIH0gZnJvbSAnLi9zdXBhYmFzZSdcbmltcG9ydCB0eXBlIHsgSUNhdGVnb3J5LCBJQ2F0ZWdvcnlGb3JtIH0gZnJvbSAnLi4vdHlwZXMnXG5pbXBvcnQgdHlwZSB7IFRhYmxlc0luc2VydCwgVGFibGVzVXBkYXRlIH0gZnJvbSAnLi4vZGF0YWJhc2UudHlwZXMnXG5cbmV4cG9ydCBjbGFzcyBDYXRlZ29yeVNlcnZpY2Uge1xuICAvKipcbiAgICogR2V0IGFsbCBjYXRlZ29yaWVzIGZvciB0aGUgY3VycmVudCB1c2VyIChpbmNsdWRpbmcgc3lzdGVtIGNhdGVnb3JpZXMpXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0Q2F0ZWdvcmllcyhvcHRpb25zPzoge1xuICAgIHR5cGU/OiAnaW5jb21lJyB8ICdleHBlbnNlJ1xuICAgIGlzX2FjdGl2ZT86IGJvb2xlYW5cbiAgICBpbmNsdWRlX3N5c3RlbT86IGJvb2xlYW5cbiAgICBwYXJlbnRfaWQ/OiBzdHJpbmcgfCBudWxsXG4gIH0pOiBQcm9taXNlPElDYXRlZ29yeVtdPiB7XG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKClcbiAgICBcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgYXV0aGVudGljYXRlZCcpXG4gICAgfVxuXG4gICAgbGV0IHF1ZXJ5ID0gc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjYXRlZ29yaWVzJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBwYXJlbnRfY2F0ZWdvcnk6Y2F0ZWdvcmllcyFjYXRlZ29yaWVzX3BhcmVudF9jYXRlZ29yeV9pZF9ma2V5KCopLFxuICAgICAgICBzdWJjYXRlZ29yaWVzOmNhdGVnb3JpZXMhY2F0ZWdvcmllc19wYXJlbnRfY2F0ZWdvcnlfaWRfZmtleSgqKVxuICAgICAgYClcbiAgICAgIC5vcihgdXNlcl9pZC5lcS4ke3VzZXIuaWR9LGFuZChpc19zeXN0ZW0uZXEudHJ1ZSx1c2VyX2lkLmlzLm51bGwpYClcbiAgICAgIC5vcmRlcignc29ydF9vcmRlcicpXG4gICAgICAub3JkZXIoJ25hbWUnKVxuXG4gICAgaWYgKG9wdGlvbnM/LnR5cGUpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuZXEoJ3R5cGUnLCBvcHRpb25zLnR5cGUpXG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnM/LmlzX2FjdGl2ZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdpc19hY3RpdmUnLCBvcHRpb25zLmlzX2FjdGl2ZSlcbiAgICB9XG5cbiAgICBpZiAob3B0aW9ucz8uaW5jbHVkZV9zeXN0ZW0gPT09IGZhbHNlKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdpc19zeXN0ZW0nLCBmYWxzZSlcbiAgICB9XG5cbiAgICBpZiAob3B0aW9ucz8ucGFyZW50X2lkICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGlmIChvcHRpb25zLnBhcmVudF9pZCA9PT0gbnVsbCkge1xuICAgICAgICBxdWVyeSA9IHF1ZXJ5LmlzKCdwYXJlbnRfY2F0ZWdvcnlfaWQnLCBudWxsKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcXVlcnkgPSBxdWVyeS5lcSgncGFyZW50X2NhdGVnb3J5X2lkJywgb3B0aW9ucy5wYXJlbnRfaWQpXG4gICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgcXVlcnlcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggY2F0ZWdvcmllczogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgSUNhdGVnb3J5W11cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgYSBzcGVjaWZpYyBjYXRlZ29yeSBieSBJRFxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldENhdGVnb3J5KGlkOiBzdHJpbmcpOiBQcm9taXNlPElDYXRlZ29yeT4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnY2F0ZWdvcmllcycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgcGFyZW50X2NhdGVnb3J5OmNhdGVnb3JpZXMhY2F0ZWdvcmllc19wYXJlbnRfY2F0ZWdvcnlfaWRfZmtleSgqKSxcbiAgICAgICAgc3ViY2F0ZWdvcmllczpjYXRlZ29yaWVzIWNhdGVnb3JpZXNfcGFyZW50X2NhdGVnb3J5X2lkX2ZrZXkoKilcbiAgICAgIGApXG4gICAgICAuZXEoJ2lkJywgaWQpXG4gICAgICAub3IoYHVzZXJfaWQuZXEuJHt1c2VyLmlkfSxhbmQoaXNfc3lzdGVtLmVxLnRydWUsdXNlcl9pZC5pcy5udWxsKWApXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggY2F0ZWdvcnk6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIHJldHVybiBkYXRhIGFzIElDYXRlZ29yeVxuICB9XG5cbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyBjYXRlZ29yeVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGNyZWF0ZUNhdGVnb3J5KGNhdGVnb3J5RGF0YTogSUNhdGVnb3J5Rm9ybSk6IFByb21pc2U8SUNhdGVnb3J5PiB7XG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKClcbiAgICBcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgYXV0aGVudGljYXRlZCcpXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgY2F0ZWdvcnkgbmFtZSBhbHJlYWR5IGV4aXN0cyBmb3IgdGhpcyB1c2VyXG4gICAgY29uc3QgeyBkYXRhOiBleGlzdGluZ0NhdGVnb3J5IH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NhdGVnb3JpZXMnKVxuICAgICAgLnNlbGVjdCgnaWQnKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5lcSgnbmFtZScsIGNhdGVnb3J5RGF0YS5uYW1lKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXhpc3RpbmdDYXRlZ29yeSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdDYXRlZ29yeSBuYW1lIGFscmVhZHkgZXhpc3RzJylcbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0ZSBwYXJlbnQgY2F0ZWdvcnkgaWYgc3BlY2lmaWVkXG4gICAgaWYgKGNhdGVnb3J5RGF0YS5wYXJlbnRfY2F0ZWdvcnlfaWQpIHtcbiAgICAgIGNvbnN0IHsgZGF0YTogcGFyZW50Q2F0ZWdvcnkgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKCdjYXRlZ29yaWVzJylcbiAgICAgICAgLnNlbGVjdCgnaWQsIHVzZXJfaWQsIGlzX3N5c3RlbScpXG4gICAgICAgIC5lcSgnaWQnLCBjYXRlZ29yeURhdGEucGFyZW50X2NhdGVnb3J5X2lkKVxuICAgICAgICAuc2luZ2xlKClcblxuICAgICAgaWYgKCFwYXJlbnRDYXRlZ29yeSB8fCAocGFyZW50Q2F0ZWdvcnkudXNlcl9pZCAhPT0gdXNlci5pZCAmJiAhcGFyZW50Q2F0ZWdvcnkuaXNfc3lzdGVtKSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgcGFyZW50IGNhdGVnb3J5IHNwZWNpZmllZCcpXG4gICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgaW5zZXJ0RGF0YTogVGFibGVzSW5zZXJ0PCdjYXRlZ29yaWVzJz4gPSB7XG4gICAgICAuLi5jYXRlZ29yeURhdGEsXG4gICAgICB1c2VyX2lkOiB1c2VyLmlkLFxuICAgICAgaXNfZGVmYXVsdDogZmFsc2UsXG4gICAgICBpc19zeXN0ZW06IGZhbHNlLFxuICAgICAgaXNfYWN0aXZlOiB0cnVlLFxuICAgICAgc29ydF9vcmRlcjogY2F0ZWdvcnlEYXRhLnNvcnRfb3JkZXIgfHwgMCxcbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NhdGVnb3JpZXMnKVxuICAgICAgLmluc2VydChpbnNlcnREYXRhKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIHBhcmVudF9jYXRlZ29yeTpjYXRlZ29yaWVzIWNhdGVnb3JpZXNfcGFyZW50X2NhdGVnb3J5X2lkX2ZrZXkoKiksXG4gICAgICAgIHN1YmNhdGVnb3JpZXM6Y2F0ZWdvcmllcyFjYXRlZ29yaWVzX3BhcmVudF9jYXRlZ29yeV9pZF9ma2V5KCopXG4gICAgICBgKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSBjYXRlZ29yeTogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgSUNhdGVnb3J5XG4gIH1cblxuICAvKipcbiAgICogVXBkYXRlIGFuIGV4aXN0aW5nIGNhdGVnb3J5XG4gICAqL1xuICBzdGF0aWMgYXN5bmMgdXBkYXRlQ2F0ZWdvcnkoaWQ6IHN0cmluZywgdXBkYXRlczogUGFydGlhbDxJQ2F0ZWdvcnlGb3JtPik6IFByb21pc2U8SUNhdGVnb3J5PiB7XG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKClcbiAgICBcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgYXV0aGVudGljYXRlZCcpXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgY2F0ZWdvcnkgZXhpc3RzIGFuZCBiZWxvbmdzIHRvIHVzZXIgKGNhbid0IHVwZGF0ZSBzeXN0ZW0gY2F0ZWdvcmllcylcbiAgICBjb25zdCB7IGRhdGE6IGV4aXN0aW5nQ2F0ZWdvcnkgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnY2F0ZWdvcmllcycpXG4gICAgICAuc2VsZWN0KCcqJylcbiAgICAgIC5lcSgnaWQnLCBpZClcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuZXEoJ2lzX3N5c3RlbScsIGZhbHNlKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoIWV4aXN0aW5nQ2F0ZWdvcnkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignQ2F0ZWdvcnkgbm90IGZvdW5kIG9yIGNhbm5vdCBiZSBtb2RpZmllZCcpXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgZm9yIG5hbWUgY29uZmxpY3RzIGlmIG5hbWUgaXMgYmVpbmcgdXBkYXRlZFxuICAgIGlmICh1cGRhdGVzLm5hbWUgJiYgdXBkYXRlcy5uYW1lICE9PSBleGlzdGluZ0NhdGVnb3J5Lm5hbWUpIHtcbiAgICAgIGNvbnN0IHsgZGF0YTogbmFtZUNvbmZsaWN0IH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnY2F0ZWdvcmllcycpXG4gICAgICAgIC5zZWxlY3QoJ2lkJylcbiAgICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgICAgLmVxKCduYW1lJywgdXBkYXRlcy5uYW1lKVxuICAgICAgICAubmVxKCdpZCcsIGlkKVxuICAgICAgICAuc2luZ2xlKClcblxuICAgICAgaWYgKG5hbWVDb25mbGljdCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NhdGVnb3J5IG5hbWUgYWxyZWFkeSBleGlzdHMnKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIHBhcmVudCBjYXRlZ29yeSBpZiBiZWluZyB1cGRhdGVkXG4gICAgaWYgKHVwZGF0ZXMucGFyZW50X2NhdGVnb3J5X2lkKSB7XG4gICAgICBjb25zdCB7IGRhdGE6IHBhcmVudENhdGVnb3J5IH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnY2F0ZWdvcmllcycpXG4gICAgICAgIC5zZWxlY3QoJ2lkLCB1c2VyX2lkLCBpc19zeXN0ZW0nKVxuICAgICAgICAuZXEoJ2lkJywgdXBkYXRlcy5wYXJlbnRfY2F0ZWdvcnlfaWQpXG4gICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICBpZiAoIXBhcmVudENhdGVnb3J5IHx8IChwYXJlbnRDYXRlZ29yeS51c2VyX2lkICE9PSB1c2VyLmlkICYmICFwYXJlbnRDYXRlZ29yeS5pc19zeXN0ZW0pKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBwYXJlbnQgY2F0ZWdvcnkgc3BlY2lmaWVkJylcbiAgICAgIH1cblxuICAgICAgLy8gUHJldmVudCBjaXJjdWxhciByZWZlcmVuY2VzXG4gICAgICBpZiAodXBkYXRlcy5wYXJlbnRfY2F0ZWdvcnlfaWQgPT09IGlkKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignQ2F0ZWdvcnkgY2Fubm90IGJlIGl0cyBvd24gcGFyZW50JylcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCB1cGRhdGVEYXRhOiBUYWJsZXNVcGRhdGU8J2NhdGVnb3JpZXMnPiA9IHtcbiAgICAgIC4uLnVwZGF0ZXMsXG4gICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjYXRlZ29yaWVzJylcbiAgICAgIC51cGRhdGUodXBkYXRlRGF0YSlcbiAgICAgIC5lcSgnaWQnLCBpZClcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgcGFyZW50X2NhdGVnb3J5OmNhdGVnb3JpZXMhY2F0ZWdvcmllc19wYXJlbnRfY2F0ZWdvcnlfaWRfZmtleSgqKSxcbiAgICAgICAgc3ViY2F0ZWdvcmllczpjYXRlZ29yaWVzIWNhdGVnb3JpZXNfcGFyZW50X2NhdGVnb3J5X2lkX2ZrZXkoKilcbiAgICAgIGApXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gdXBkYXRlIGNhdGVnb3J5OiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YSBhcyBJQ2F0ZWdvcnlcbiAgfVxuXG4gIC8qKlxuICAgKiBEZWxldGUgYSBjYXRlZ29yeSAoc29mdCBkZWxldGUgYnkgc2V0dGluZyBpc19hY3RpdmUgdG8gZmFsc2UpXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZGVsZXRlQ2F0ZWdvcnkoaWQ6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIGNhdGVnb3J5IGV4aXN0cyBhbmQgYmVsb25ncyB0byB1c2VyIChjYW4ndCBkZWxldGUgc3lzdGVtIGNhdGVnb3JpZXMpXG4gICAgY29uc3QgeyBkYXRhOiBjYXRlZ29yeSB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjYXRlZ29yaWVzJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmVxKCdpZCcsIGlkKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5lcSgnaXNfc3lzdGVtJywgZmFsc2UpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmICghY2F0ZWdvcnkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignQ2F0ZWdvcnkgbm90IGZvdW5kIG9yIGNhbm5vdCBiZSBkZWxldGVkJylcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiBjYXRlZ29yeSBoYXMgdHJhbnNhY3Rpb25zXG4gICAgY29uc3QgeyBkYXRhOiB0cmFuc2FjdGlvbnMgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndHJhbnNhY3Rpb25zJylcbiAgICAgIC5zZWxlY3QoJ2lkJylcbiAgICAgIC5lcSgnY2F0ZWdvcnlfaWQnLCBpZClcbiAgICAgIC5saW1pdCgxKVxuXG4gICAgLy8gQ2hlY2sgaWYgY2F0ZWdvcnkgaGFzIHN1YmNhdGVnb3JpZXNcbiAgICBjb25zdCB7IGRhdGE6IHN1YmNhdGVnb3JpZXMgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnY2F0ZWdvcmllcycpXG4gICAgICAuc2VsZWN0KCdpZCcpXG4gICAgICAuZXEoJ3BhcmVudF9jYXRlZ29yeV9pZCcsIGlkKVxuICAgICAgLmVxKCdpc19hY3RpdmUnLCB0cnVlKVxuICAgICAgLmxpbWl0KDEpXG5cbiAgICBpZiAodHJhbnNhY3Rpb25zICYmIHRyYW5zYWN0aW9ucy5sZW5ndGggPiAwKSB7XG4gICAgICAvLyBTb2Z0IGRlbGV0ZSBpZiBjYXRlZ29yeSBoYXMgdHJhbnNhY3Rpb25zXG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnY2F0ZWdvcmllcycpXG4gICAgICAgIC51cGRhdGUoeyBcbiAgICAgICAgICBpc19hY3RpdmU6IGZhbHNlLFxuICAgICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICB9KVxuICAgICAgICAuZXEoJ2lkJywgaWQpXG4gICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBkZWFjdGl2YXRlIGNhdGVnb3J5OiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKHN1YmNhdGVnb3JpZXMgJiYgc3ViY2F0ZWdvcmllcy5sZW5ndGggPiAwKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0Nhbm5vdCBkZWxldGUgY2F0ZWdvcnkgd2l0aCBhY3RpdmUgc3ViY2F0ZWdvcmllcycpXG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIEhhcmQgZGVsZXRlIGlmIG5vIHRyYW5zYWN0aW9ucyBvciBzdWJjYXRlZ29yaWVzXG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnY2F0ZWdvcmllcycpXG4gICAgICAgIC5kZWxldGUoKVxuICAgICAgICAuZXEoJ2lkJywgaWQpXG4gICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBkZWxldGUgY2F0ZWdvcnk6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgY2F0ZWdvcnkgaGllcmFyY2h5IChwYXJlbnQgY2F0ZWdvcmllcyB3aXRoIHRoZWlyIHN1YmNhdGVnb3JpZXMpXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0Q2F0ZWdvcnlIaWVyYXJjaHkodHlwZT86ICdpbmNvbWUnIHwgJ2V4cGVuc2UnKTogUHJvbWlzZTxJQ2F0ZWdvcnlbXT4ge1xuICAgIGNvbnN0IGNhdGVnb3JpZXMgPSBhd2FpdCB0aGlzLmdldENhdGVnb3JpZXMoeyBcbiAgICAgIHR5cGUsIFxuICAgICAgaXNfYWN0aXZlOiB0cnVlLFxuICAgICAgaW5jbHVkZV9zeXN0ZW06IHRydWUgXG4gICAgfSlcblxuICAgIC8vIEJ1aWxkIGhpZXJhcmNoeVxuICAgIGNvbnN0IGNhdGVnb3J5TWFwID0gbmV3IE1hcDxzdHJpbmcsIElDYXRlZ29yeT4oKVxuICAgIGNvbnN0IHJvb3RDYXRlZ29yaWVzOiBJQ2F0ZWdvcnlbXSA9IFtdXG5cbiAgICAvLyBGaXJzdCBwYXNzOiBjcmVhdGUgbWFwIGFuZCBpZGVudGlmeSByb290IGNhdGVnb3JpZXNcbiAgICBjYXRlZ29yaWVzLmZvckVhY2goY2F0ZWdvcnkgPT4ge1xuICAgICAgY2F0ZWdvcnlNYXAuc2V0KGNhdGVnb3J5LmlkLCB7IC4uLmNhdGVnb3J5LCBzdWJjYXRlZ29yaWVzOiBbXSB9KVxuICAgICAgaWYgKCFjYXRlZ29yeS5wYXJlbnRfY2F0ZWdvcnlfaWQpIHtcbiAgICAgICAgcm9vdENhdGVnb3JpZXMucHVzaChjYXRlZ29yeU1hcC5nZXQoY2F0ZWdvcnkuaWQpISlcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgLy8gU2Vjb25kIHBhc3M6IGJ1aWxkIHBhcmVudC1jaGlsZCByZWxhdGlvbnNoaXBzXG4gICAgY2F0ZWdvcmllcy5mb3JFYWNoKGNhdGVnb3J5ID0+IHtcbiAgICAgIGlmIChjYXRlZ29yeS5wYXJlbnRfY2F0ZWdvcnlfaWQpIHtcbiAgICAgICAgY29uc3QgcGFyZW50ID0gY2F0ZWdvcnlNYXAuZ2V0KGNhdGVnb3J5LnBhcmVudF9jYXRlZ29yeV9pZClcbiAgICAgICAgY29uc3QgY2hpbGQgPSBjYXRlZ29yeU1hcC5nZXQoY2F0ZWdvcnkuaWQpXG4gICAgICAgIGlmIChwYXJlbnQgJiYgY2hpbGQpIHtcbiAgICAgICAgICBwYXJlbnQuc3ViY2F0ZWdvcmllcyA9IHBhcmVudC5zdWJjYXRlZ29yaWVzIHx8IFtdXG4gICAgICAgICAgcGFyZW50LnN1YmNhdGVnb3JpZXMucHVzaChjaGlsZClcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pXG5cbiAgICByZXR1cm4gcm9vdENhdGVnb3JpZXNcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgY2F0ZWdvcnkgdXNhZ2Ugc3RhdGlzdGljc1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldENhdGVnb3J5U3RhdHMob3B0aW9ucz86IHtcbiAgICBzdGFydERhdGU/OiBzdHJpbmdcbiAgICBlbmREYXRlPzogc3RyaW5nXG4gIH0pOiBQcm9taXNlPEFycmF5PHtcbiAgICBjYXRlZ29yeTogSUNhdGVnb3J5XG4gICAgdHJhbnNhY3Rpb25Db3VudDogbnVtYmVyXG4gICAgdG90YWxBbW91bnQ6IG51bWJlclxuICAgIGF2ZXJhZ2VBbW91bnQ6IG51bWJlclxuICB9Pj4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIGxldCBxdWVyeSA9IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndHJhbnNhY3Rpb25zJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICBjYXRlZ29yeV9pZCxcbiAgICAgICAgYW1vdW50LFxuICAgICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopXG4gICAgICBgKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5ub3QoJ2NhdGVnb3J5X2lkJywgJ2lzJywgbnVsbClcblxuICAgIGlmIChvcHRpb25zPy5zdGFydERhdGUpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuZ3RlKCd0cmFuc2FjdGlvbl9kYXRlJywgb3B0aW9ucy5zdGFydERhdGUpXG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnM/LmVuZERhdGUpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkubHRlKCd0cmFuc2FjdGlvbl9kYXRlJywgb3B0aW9ucy5lbmREYXRlKVxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHF1ZXJ5XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIGNhdGVnb3J5IHN0YXRpc3RpY3M6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIC8vIEdyb3VwIGJ5IGNhdGVnb3J5IGFuZCBjYWxjdWxhdGUgc3RhdHNcbiAgICBjb25zdCBzdGF0c01hcCA9IG5ldyBNYXA8c3RyaW5nLCB7XG4gICAgICBjYXRlZ29yeTogSUNhdGVnb3J5XG4gICAgICB0cmFuc2FjdGlvbkNvdW50OiBudW1iZXJcbiAgICAgIHRvdGFsQW1vdW50OiBudW1iZXJcbiAgICB9PigpXG5cbiAgICBkYXRhPy5mb3JFYWNoKHRyYW5zYWN0aW9uID0+IHtcbiAgICAgIGlmICh0cmFuc2FjdGlvbi5jYXRlZ29yeSkge1xuICAgICAgICBjb25zdCBjYXRlZ29yeUlkID0gdHJhbnNhY3Rpb24uY2F0ZWdvcnlfaWQhXG4gICAgICAgIGNvbnN0IGV4aXN0aW5nID0gc3RhdHNNYXAuZ2V0KGNhdGVnb3J5SWQpIHx8IHtcbiAgICAgICAgICBjYXRlZ29yeTogdHJhbnNhY3Rpb24uY2F0ZWdvcnkgYXMgSUNhdGVnb3J5LFxuICAgICAgICAgIHRyYW5zYWN0aW9uQ291bnQ6IDAsXG4gICAgICAgICAgdG90YWxBbW91bnQ6IDAsXG4gICAgICAgIH1cblxuICAgICAgICBleGlzdGluZy50cmFuc2FjdGlvbkNvdW50KytcbiAgICAgICAgZXhpc3RpbmcudG90YWxBbW91bnQgKz0gdHJhbnNhY3Rpb24uYW1vdW50XG5cbiAgICAgICAgc3RhdHNNYXAuc2V0KGNhdGVnb3J5SWQsIGV4aXN0aW5nKVxuICAgICAgfVxuICAgIH0pXG5cbiAgICAvLyBDb252ZXJ0IHRvIGFycmF5IGFuZCBhZGQgYXZlcmFnZVxuICAgIHJldHVybiBBcnJheS5mcm9tKHN0YXRzTWFwLnZhbHVlcygpKS5tYXAoc3RhdCA9PiAoe1xuICAgICAgLi4uc3RhdCxcbiAgICAgIGF2ZXJhZ2VBbW91bnQ6IHN0YXQudHJhbnNhY3Rpb25Db3VudCA+IDAgPyBzdGF0LnRvdGFsQW1vdW50IC8gc3RhdC50cmFuc2FjdGlvbkNvdW50IDogMCxcbiAgICB9KSkuc29ydCgoYSwgYikgPT4gYi50b3RhbEFtb3VudCAtIGEudG90YWxBbW91bnQpXG4gIH1cblxuICAvKipcbiAgICogUmVvcmRlciBjYXRlZ29yaWVzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgcmVvcmRlckNhdGVnb3JpZXMoY2F0ZWdvcnlPcmRlcnM6IHsgaWQ6IHN0cmluZzsgc29ydF9vcmRlcjogbnVtYmVyIH1bXSk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIC8vIFVwZGF0ZSBzb3J0IG9yZGVycyBpbiBiYXRjaFxuICAgIGZvciAoY29uc3QgeyBpZCwgc29ydF9vcmRlciB9IG9mIGNhdGVnb3J5T3JkZXJzKSB7XG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnY2F0ZWdvcmllcycpXG4gICAgICAgIC51cGRhdGUoeyBcbiAgICAgICAgICBzb3J0X29yZGVyLFxuICAgICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICB9KVxuICAgICAgICAuZXEoJ2lkJywgaWQpXG4gICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAgIC5lcSgnaXNfc3lzdGVtJywgZmFsc2UpIC8vIE9ubHkgYWxsb3cgcmVvcmRlcmluZyB1c2VyIGNhdGVnb3JpZXNcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEZhaWxlZCB0byB1cGRhdGUgc29ydCBvcmRlciBmb3IgY2F0ZWdvcnkgJHtpZH06YCwgZXJyb3IpXG4gICAgICB9XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsic3VwYWJhc2UiLCJDYXRlZ29yeVNlcnZpY2UiLCJnZXRDYXRlZ29yaWVzIiwib3B0aW9ucyIsImRhdGEiLCJ1c2VyIiwiYXV0aCIsImdldFVzZXIiLCJFcnJvciIsInF1ZXJ5IiwiZnJvbSIsInNlbGVjdCIsIm9yIiwiaWQiLCJvcmRlciIsInR5cGUiLCJlcSIsImlzX2FjdGl2ZSIsInVuZGVmaW5lZCIsImluY2x1ZGVfc3lzdGVtIiwicGFyZW50X2lkIiwiaXMiLCJlcnJvciIsIm1lc3NhZ2UiLCJnZXRDYXRlZ29yeSIsInNpbmdsZSIsImNyZWF0ZUNhdGVnb3J5IiwiY2F0ZWdvcnlEYXRhIiwiZXhpc3RpbmdDYXRlZ29yeSIsIm5hbWUiLCJwYXJlbnRfY2F0ZWdvcnlfaWQiLCJwYXJlbnRDYXRlZ29yeSIsInVzZXJfaWQiLCJpc19zeXN0ZW0iLCJpbnNlcnREYXRhIiwiaXNfZGVmYXVsdCIsInNvcnRfb3JkZXIiLCJpbnNlcnQiLCJ1cGRhdGVDYXRlZ29yeSIsInVwZGF0ZXMiLCJuYW1lQ29uZmxpY3QiLCJuZXEiLCJ1cGRhdGVEYXRhIiwidXBkYXRlZF9hdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInVwZGF0ZSIsImRlbGV0ZUNhdGVnb3J5IiwiY2F0ZWdvcnkiLCJ0cmFuc2FjdGlvbnMiLCJsaW1pdCIsInN1YmNhdGVnb3JpZXMiLCJsZW5ndGgiLCJkZWxldGUiLCJnZXRDYXRlZ29yeUhpZXJhcmNoeSIsImNhdGVnb3JpZXMiLCJjYXRlZ29yeU1hcCIsIk1hcCIsInJvb3RDYXRlZ29yaWVzIiwiZm9yRWFjaCIsInNldCIsInB1c2giLCJnZXQiLCJwYXJlbnQiLCJjaGlsZCIsImdldENhdGVnb3J5U3RhdHMiLCJub3QiLCJzdGFydERhdGUiLCJndGUiLCJlbmREYXRlIiwibHRlIiwic3RhdHNNYXAiLCJ0cmFuc2FjdGlvbiIsImNhdGVnb3J5SWQiLCJjYXRlZ29yeV9pZCIsImV4aXN0aW5nIiwidHJhbnNhY3Rpb25Db3VudCIsInRvdGFsQW1vdW50IiwiYW1vdW50IiwiQXJyYXkiLCJ2YWx1ZXMiLCJtYXAiLCJzdGF0IiwiYXZlcmFnZUFtb3VudCIsInNvcnQiLCJhIiwiYiIsInJlb3JkZXJDYXRlZ29yaWVzIiwiY2F0ZWdvcnlPcmRlcnMiLCJjb25zb2xlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/categories.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/investments.ts":
/*!****************************************************!*\
  !*** ../../packages/shared/src/lib/investments.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvestmentService: () => (/* binding */ InvestmentService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _tax_calculator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tax-calculator */ \"(app-pages-browser)/../../packages/shared/src/lib/tax-calculator.ts\");\n/* harmony import */ var _assets__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./assets */ \"(app-pages-browser)/../../packages/shared/src/lib/assets.ts\");\n/* harmony import */ var _profit_loss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./profit-loss */ \"(app-pages-browser)/../../packages/shared/src/lib/profit-loss.ts\");\n\n\n\n\nclass InvestmentService {\n    /**\n   * Create an investment transaction (buy/sell)\n   * This also creates a transfer from a funding account for buy transactions\n   */ static async createInvestmentTransaction(investmentData, fundingAccountId// Required for buy transactions\n    ) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Validate investment account exists and is an investment account\n        const { data: investmentAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', investmentData.account_id).eq('user_id', user.id).single();\n        if (!investmentAccount || investmentAccount.account_type !== 'investment') {\n            throw new Error('Invalid investment account specified');\n        }\n        // For buy transactions, validate funding account and check balance\n        if (investmentData.transaction_type === 'investment_buy') {\n            if (!fundingAccountId) {\n                throw new Error('Funding account required for investment purchases');\n            }\n            const { data: fundingAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', fundingAccountId).eq('user_id', user.id).single();\n            if (!fundingAccount) {\n                throw new Error('Invalid funding account specified');\n            }\n            const totalCost = investmentData.amount + (investmentData.fees || 0);\n            if (fundingAccount.current_balance < totalCost) {\n                throw new Error('Insufficient balance in funding account');\n            }\n        }\n        // Get investment category\n        const categoryName = investmentData.transaction_type === 'investment_buy' ? 'Investment Purchase' : 'Investment Sale';\n        const { data: category } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', categoryName).eq('is_system', true).single();\n        if (!category) {\n            throw new Error(\"\".concat(categoryName, \" category not found\"));\n        }\n        // Create the investment transaction\n        const transactionData = {\n            amount: investmentData.amount,\n            description: investmentData.description || \"\".concat(investmentData.transaction_type === 'investment_buy' ? 'Buy' : 'Sell', \" \").concat(investmentData.investment_quantity, \" shares of \").concat(investmentData.investment_symbol),\n            category_id: category.id,\n            account_id: investmentData.account_id,\n            transaction_type: investmentData.transaction_type,\n            transaction_date: investmentData.transaction_date,\n            transaction_status: 'completed',\n            fees: investmentData.fees || 0,\n            investment_symbol: investmentData.investment_symbol,\n            investment_quantity: investmentData.investment_quantity,\n            investment_price: investmentData.investment_price,\n            user_id: user.id\n        };\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts(*)\\n      \").single();\n        if (transactionError) {\n            throw new Error(\"Failed to create investment transaction: \".concat(transactionError.message));\n        }\n        // For buy transactions, create a transfer from funding account to investment account\n        if (investmentData.transaction_type === 'investment_buy' && fundingAccountId) {\n            const { TransferService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\"));\n            try {\n                await TransferService.createTransfer({\n                    amount: investmentData.amount + (investmentData.fees || 0),\n                    description: \"Investment purchase: \".concat(investmentData.investment_symbol),\n                    from_account_id: fundingAccountId,\n                    to_account_id: investmentData.account_id,\n                    transaction_date: investmentData.transaction_date,\n                    fees: 0\n                });\n            } catch (error) {\n                // If transfer fails, rollback the investment transaction\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', transaction.id);\n                throw new Error(\"Failed to create funding transfer: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n            }\n        }\n        return transaction;\n    }\n    /**\n   * Create investment transaction with enhanced asset tracking and tax calculations\n   */ static async createInvestmentTransactionWithAssets(investmentData, fundingAccountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Get asset information\n        const asset = await _assets__WEBPACK_IMPORTED_MODULE_2__.AssetService.getAsset(investmentData.asset_id);\n        if (!asset) {\n            throw new Error('Asset not found');\n        }\n        // For sell transactions, calculate tax implications\n        let taxCalculation = null;\n        let realizedGainLoss = null;\n        if (investmentData.transaction_type === 'investment_sell') {\n            // Get the holding to determine purchase details\n            const holding = await _assets__WEBPACK_IMPORTED_MODULE_2__.HoldingService.getHolding(investmentData.account_id, investmentData.asset_id);\n            if (!holding) {\n                throw new Error('No holding found for this asset');\n            }\n            if (holding.quantity < investmentData.investment_quantity) {\n                throw new Error('Cannot sell more than current holding');\n            }\n            // Calculate tax implications\n            taxCalculation = await _tax_calculator__WEBPACK_IMPORTED_MODULE_1__.TaxCalculatorService.calculateCapitalGainsTax({\n                asset_class_id: asset.asset_class_id,\n                purchase_date: holding.created_at,\n                sale_date: investmentData.transaction_date,\n                purchase_price: holding.average_cost,\n                sale_price: investmentData.investment_price,\n                quantity: investmentData.investment_quantity,\n                fees: investmentData.fees\n            });\n            // Calculate realized gain/loss\n            realizedGainLoss = await _profit_loss__WEBPACK_IMPORTED_MODULE_3__.ProfitLossService.calculateRealizedGainLoss(investmentData.asset_id, investmentData.investment_quantity, investmentData.investment_price, investmentData.transaction_date, holding.average_cost, holding.created_at);\n        }\n        // Create the investment transaction using the existing method\n        const transaction = await this.createInvestmentTransaction(investmentData, fundingAccountId);\n        // Update holdings\n        const holding = await _assets__WEBPACK_IMPORTED_MODULE_2__.HoldingService.updateHolding(investmentData.account_id, investmentData.asset_id, investmentData.transaction_type === 'investment_buy' ? 'buy' : 'sell', investmentData.investment_quantity, investmentData.investment_price);\n        return {\n            transaction,\n            holding,\n            taxCalculation,\n            realizedGainLoss\n        };\n    }\n    /**\n   * Get investment transactions for a user or specific account\n   */ static async getInvestmentTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*)\\n      \", {\n            count: 'exact'\n        }).eq('user_id', user.id).in('transaction_type', [\n            'investment_buy',\n            'investment_sell'\n        ]).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            query = query.eq('account_id', options.account_id);\n        }\n        if (options === null || options === void 0 ? void 0 : options.symbol) {\n            query = query.eq('investment_symbol', options.symbol);\n        }\n        if (options === null || options === void 0 ? void 0 : options.transaction_type) {\n            query = query.eq('transaction_type', options.transaction_type);\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 20) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch investment transactions: \".concat(error.message));\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    /**\n   * Get investment holdings for a user or specific account\n   */ static async getInvestmentHoldings(accountId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_holdings').select(\"\\n        *,\\n        account:accounts(*)\\n      \").order('symbol');\n        if (accountId) {\n            query = query.eq('account_id', accountId);\n        } else {\n            // Filter by user's accounts\n            const { data: userAccounts } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('id').eq('user_id', user.id).eq('account_type', 'investment');\n            if (userAccounts && userAccounts.length > 0) {\n                const accountIds = userAccounts.map((acc)=>acc.id);\n                query = query.in('account_id', accountIds);\n            } else {\n                return [];\n            }\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch investment holdings: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get portfolio summary for all investment accounts\n   */ static async getPortfolioSummary() {\n        const holdings = await this.getInvestmentHoldings();\n        let totalValue = 0;\n        let totalCost = 0;\n        const holdingsBySymbol = {};\n        holdings.forEach((holding)=>{\n            const symbol = holding.symbol;\n            const quantity = holding.quantity;\n            const avgCost = holding.average_cost;\n            const currentPrice = holding.current_price || avgCost;\n            const marketValue = quantity * currentPrice;\n            const costBasis = quantity * avgCost;\n            const gainLoss = marketValue - costBasis;\n            const gainLossPercent = costBasis > 0 ? gainLoss / costBasis * 100 : 0;\n            if (!holdingsBySymbol[symbol]) {\n                holdingsBySymbol[symbol] = {\n                    symbol,\n                    totalQuantity: 0,\n                    averageCost: 0,\n                    currentPrice,\n                    marketValue: 0,\n                    gainLoss: 0,\n                    gainLossPercent: 0\n                };\n            }\n            // Aggregate holdings for the same symbol across accounts\n            const existing = holdingsBySymbol[symbol];\n            const newTotalQuantity = existing.totalQuantity + quantity;\n            const newTotalCost = existing.totalQuantity * existing.averageCost + quantity * avgCost;\n            holdingsBySymbol[symbol] = {\n                ...existing,\n                totalQuantity: newTotalQuantity,\n                averageCost: newTotalQuantity > 0 ? newTotalCost / newTotalQuantity : 0,\n                marketValue: existing.marketValue + marketValue,\n                gainLoss: existing.gainLoss + gainLoss\n            };\n            // Recalculate percentage\n            const totalCostBasis = holdingsBySymbol[symbol].totalQuantity * holdingsBySymbol[symbol].averageCost;\n            holdingsBySymbol[symbol].gainLossPercent = totalCostBasis > 0 ? holdingsBySymbol[symbol].gainLoss / totalCostBasis * 100 : 0;\n            totalValue += marketValue;\n            totalCost += costBasis;\n        });\n        const totalGainLoss = totalValue - totalCost;\n        const totalGainLossPercent = totalCost > 0 ? totalGainLoss / totalCost * 100 : 0;\n        return {\n            totalValue,\n            totalCost,\n            totalGainLoss,\n            totalGainLossPercent,\n            holdingsBySymbol\n        };\n    }\n    /**\n   * Update current prices for holdings (would typically be called by a background job)\n   */ static async updateHoldingPrices(priceUpdates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        for (const update of priceUpdates){\n            const marketValue = update.price // Will be calculated by trigger\n            ;\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('investment_holdings').update({\n                current_price: update.price,\n                market_value: marketValue,\n                last_updated: new Date().toISOString()\n            }).eq('symbol', update.symbol);\n            if (error) {\n                console.error(\"Failed to update price for \".concat(update.symbol, \":\"), error);\n            }\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/investments.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/transactions.ts":
/*!*****************************************************!*\
  !*** ../../packages/shared/src/lib/transactions.ts ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionService: () => (/* binding */ TransactionService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _investments__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./investments */ \"(app-pages-browser)/../../packages/shared/src/lib/investments.ts\");\n\n\nclass TransactionService {\n    /**\n   * Create a transaction of any type\n   */ static async createTransaction(data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { TransferService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./transfers */ \"(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\"));\n        // Handle different transaction types\n        switch(data.transaction_type){\n            case 'transfer':\n                if (!data.account_id || !data.to_account_id) {\n                    throw new Error('Source and destination accounts are required for transfers');\n                }\n                const transfer = await TransferService.createTransfer({\n                    amount: data.amount,\n                    description: data.description,\n                    from_account_id: data.account_id,\n                    to_account_id: data.to_account_id,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees\n                });\n                // Return the outgoing transaction as the primary transaction\n                const { data: transferTransactions } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n            *,\\n            category:categories(*),\\n            account:accounts!transactions_account_id_fkey(*),\\n            to_account:accounts!transactions_to_account_id_fkey(*)\\n          \").eq('transfer_id', transfer.transfer_id).eq('account_id', data.account_id).single();\n                return transferTransactions;\n            case 'investment_buy':\n            case 'investment_sell':\n                if (!data.account_id || !data.investment_symbol || !data.investment_quantity || !data.investment_price) {\n                    throw new Error('Investment account, symbol, quantity, and price are required for investment transactions');\n                }\n                const investment = await _investments__WEBPACK_IMPORTED_MODULE_1__.InvestmentService.createInvestmentTransaction({\n                    amount: data.amount,\n                    description: data.description,\n                    account_id: data.account_id,\n                    investment_symbol: data.investment_symbol,\n                    investment_quantity: data.investment_quantity,\n                    investment_price: data.investment_price,\n                    transaction_type: data.transaction_type,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees\n                }, data.funding_account_id);\n                return investment;\n            case 'dividend':\n                // Handle dividend as a transfer from investment account to bank account\n                if (!data.account_id || !data.to_account_id) {\n                    throw new Error('Investment account and receiving account are required for dividend transactions');\n                }\n                const dividendTransfer = await TransferService.createTransfer({\n                    amount: data.amount,\n                    description: data.description || \"Dividend payment: \".concat(data.investment_symbol || 'Investment'),\n                    from_account_id: data.account_id,\n                    to_account_id: data.to_account_id,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    fees: data.fees || 0\n                });\n                return dividendTransfer;\n            case 'income':\n            case 'expense':\n                if (!data.category_id || !data.account_id) {\n                    throw new Error('Category and account are required for income/expense transactions');\n                }\n                const transactionData = {\n                    amount: data.amount,\n                    description: data.description || null,\n                    category_id: data.category_id,\n                    account_id: data.account_id,\n                    transaction_type: data.transaction_type,\n                    transaction_date: data.transaction_date.toISOString().split('T')[0],\n                    transaction_status: 'completed',\n                    fees: data.fees || 0,\n                    user_id: user.id\n                };\n                const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(\"\\n            *,\\n            category:categories(*),\\n            account:accounts!transactions_account_id_fkey(*)\\n          \").single();\n                if (error) {\n                    throw new Error(\"Failed to create transaction: \".concat(error.message));\n                }\n                return transaction;\n            default:\n                throw new Error(\"Unsupported transaction type: \".concat(data.transaction_type));\n        }\n    }\n    /**\n   * Get all transactions for the current user with enhanced filtering\n   */ static async getTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \", {\n            count: 'exact'\n        }).eq('user_id', user.id).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        // Apply filters\n        if (options === null || options === void 0 ? void 0 : options.categoryId) {\n            query = query.eq('category_id', options.categoryId);\n        }\n        if (options === null || options === void 0 ? void 0 : options.accountId) {\n            query = query.or(\"account_id.eq.\".concat(options.accountId, \",to_account_id.eq.\").concat(options.accountId));\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.transactionType) {\n            if (Array.isArray(options.transactionType)) {\n                query = query.in('transaction_type', options.transactionType);\n            } else {\n                query = query.eq('transaction_type', options.transactionType);\n            }\n        }\n        if (options === null || options === void 0 ? void 0 : options.transactionStatus) {\n            query = query.eq('transaction_status', options.transactionStatus);\n        }\n        // Filter out transfers and investments if not explicitly requested\n        if (!(options === null || options === void 0 ? void 0 : options.includeTransfers) && !(options === null || options === void 0 ? void 0 : options.includeInvestments)) {\n            query = query.in('transaction_type', [\n                'income',\n                'expense',\n                'dividend'\n            ]);\n        } else if (!(options === null || options === void 0 ? void 0 : options.includeTransfers)) {\n            query = query.neq('transaction_type', 'transfer');\n        } else if (!(options === null || options === void 0 ? void 0 : options.includeInvestments)) {\n            query = query.not('transaction_type', 'in', '(investment_buy,investment_sell)');\n        }\n        if (options === null || options === void 0 ? void 0 : options.searchQuery) {\n            // Search in description, category name, and investment symbol\n            query = query.or(\"description.ilike.%\".concat(options.searchQuery, \"%,\") + \"investment_symbol.ilike.%\".concat(options.searchQuery, \"%\"));\n        }\n        // Apply pagination\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch transactions: \".concat(error.message));\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    /**\n   * Get a specific transaction by ID\n   */ static async getTransaction(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            throw new Error(\"Failed to fetch transaction: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Update a transaction (limited to basic transactions, not transfers or investments)\n   */ static async updateTransaction(id, updates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // First check if this is a basic transaction (not transfer or investment)\n        const { data: existingTransaction } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, transfer_id').eq('id', id).eq('user_id', user.id).single();\n        if (!existingTransaction) {\n            throw new Error('Transaction not found');\n        }\n        if (existingTransaction.transaction_type === 'transfer' || existingTransaction.transaction_type === 'investment_buy' || existingTransaction.transaction_type === 'investment_sell') {\n            throw new Error('Cannot update transfer or investment transactions through this method');\n        }\n        const updateData = {\n            updated_at: new Date().toISOString()\n        };\n        if (updates.amount !== undefined) updateData.amount = updates.amount;\n        if (updates.description !== undefined) updateData.description = updates.description;\n        if (updates.category_id !== undefined) updateData.category_id = updates.category_id;\n        if (updates.fees !== undefined) updateData.fees = updates.fees;\n        if (updates.transaction_date !== undefined) {\n            updateData.transaction_date = updates.transaction_date.toISOString().split('T')[0];\n        }\n        const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').update(updateData).eq('id', id).eq('user_id', user.id).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*)\\n      \").single();\n        if (error) {\n            throw new Error(\"Failed to update transaction: \".concat(error.message));\n        }\n        return transaction;\n    }\n    /**\n   * Delete a transaction\n   */ static async deleteTransaction(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if this is a transfer transaction\n        const { data: transaction } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, transfer_id').eq('id', id).eq('user_id', user.id).single();\n        if (!transaction) {\n            throw new Error('Transaction not found');\n        }\n        if (transaction.transaction_type === 'transfer' && transaction.transfer_id) {\n            // Delete all transactions with the same transfer_id\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('transfer_id', transaction.transfer_id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete transfer: \".concat(error.message));\n            }\n        } else {\n            // Delete single transaction\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', id).eq('user_id', user.id);\n            if (error) {\n                throw new Error(\"Failed to delete transaction: \".concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get transaction summary for a date range\n   */ static async getTransactionSummary(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transaction_type, amount').eq('user_id', user.id).eq('transaction_status', 'completed');\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.accountId) {\n            query = query.or(\"account_id.eq.\".concat(options.accountId, \",to_account_id.eq.\").concat(options.accountId));\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(\"Failed to fetch transaction summary: \".concat(error.message));\n        }\n        const summary = {\n            totalIncome: 0,\n            totalExpenses: 0,\n            totalTransfers: 0,\n            totalInvestments: 0,\n            netFlow: 0,\n            transactionCount: (data === null || data === void 0 ? void 0 : data.length) || 0\n        };\n        data === null || data === void 0 ? void 0 : data.forEach((transaction)=>{\n            switch(transaction.transaction_type){\n                case 'income':\n                case 'dividend':\n                    summary.totalIncome += transaction.amount;\n                    break;\n                case 'expense':\n                    summary.totalExpenses += transaction.amount;\n                    break;\n                case 'transfer':\n                    summary.totalTransfers += transaction.amount;\n                    break;\n                case 'investment_buy':\n                case 'investment_sell':\n                    summary.totalInvestments += transaction.amount;\n                    break;\n            }\n        });\n        summary.netFlow = summary.totalIncome - summary.totalExpenses;\n        return summary;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/transactions.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/shared/src/lib/transfers.ts":
/*!**************************************************!*\
  !*** ../../packages/shared/src/lib/transfers.ts ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransferService: () => (/* binding */ TransferService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/../../node_modules/uuid/dist/esm-browser/v4.js\");\n\n\nclass TransferService {\n    /**\n   * Create a transfer between two accounts\n   * This creates two linked transactions: one debit and one credit\n   */ static async createTransfer(transferData) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Validate accounts exist and belong to user\n        const { data: fromAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', transferData.from_account_id).eq('user_id', user.id).single();\n        const { data: toAccount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('accounts').select('*').eq('id', transferData.to_account_id).eq('user_id', user.id).single();\n        if (!fromAccount || !toAccount) {\n            throw new Error('Invalid account(s) specified');\n        }\n        if (fromAccount.id === toAccount.id) {\n            throw new Error('Cannot transfer to the same account');\n        }\n        // Check if source account has sufficient balance (except for credit cards)\n        if (fromAccount.account_type !== 'credit_card' && fromAccount.current_balance < transferData.amount) {\n            throw new Error('Insufficient balance in source account');\n        }\n        // Generate a unique transfer ID to link the transactions\n        const transferId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        // Get system transfer categories\n        const { data: transferOutCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', 'Transfer Out').eq('is_system', true).single();\n        const { data: transferInCategory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('name', 'Transfer In').eq('is_system', true).single();\n        if (!transferOutCategory || !transferInCategory) {\n            throw new Error('Transfer categories not found');\n        }\n        // Create the outgoing transaction (debit from source account)\n        const outgoingTransaction = {\n            amount: transferData.amount,\n            description: transferData.description || \"Transfer to \".concat(toAccount.name),\n            category_id: transferOutCategory.id,\n            account_id: transferData.from_account_id,\n            to_account_id: transferData.to_account_id,\n            transfer_id: transferId,\n            transaction_type: 'transfer',\n            transaction_date: transferData.transaction_date,\n            transaction_status: 'completed',\n            fees: transferData.fees || 0,\n            user_id: user.id\n        };\n        // Create the incoming transaction (credit to destination account)\n        const incomingTransaction = {\n            amount: transferData.amount,\n            description: transferData.description || \"Transfer from \".concat(fromAccount.name),\n            category_id: transferInCategory.id,\n            account_id: transferData.to_account_id,\n            to_account_id: transferData.from_account_id,\n            transfer_id: transferId,\n            transaction_type: 'transfer',\n            transaction_date: transferData.transaction_date,\n            transaction_status: 'completed',\n            fees: 0,\n            user_id: user.id\n        };\n        // Insert both transactions in a transaction\n        const { data: outgoingData, error: outgoingError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(outgoingTransaction).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").single();\n        if (outgoingError) {\n            throw new Error(\"Failed to create outgoing transaction: \".concat(outgoingError.message));\n        }\n        const { data: incomingData, error: incomingError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(incomingTransaction).select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").single();\n        if (incomingError) {\n            // If incoming transaction fails, we should rollback the outgoing transaction\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', outgoingData.id);\n            throw new Error(\"Failed to create incoming transaction: \".concat(incomingError.message));\n        }\n        // Return a combined transfer object\n        return {\n            id: transferId,\n            amount: transferData.amount,\n            description: transferData.description,\n            from_account_id: transferData.from_account_id,\n            to_account_id: transferData.to_account_id,\n            transfer_id: transferId,\n            transaction_date: transferData.transaction_date,\n            fees: transferData.fees,\n            user_id: user.id,\n            from_account: fromAccount,\n            to_account: toAccount\n        };\n    }\n    /**\n   * Get all transfers for the current user\n   */ static async getTransfers(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Get unique transfer IDs first\n        let transferQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('transfer_id, transaction_date', {\n            count: 'exact'\n        }).eq('user_id', user.id).eq('transaction_type', 'transfer').not('transfer_id', 'is', null).order('transaction_date', {\n            ascending: false\n        });\n        if (options === null || options === void 0 ? void 0 : options.account_id) {\n            transferQuery = transferQuery.or(\"account_id.eq.\".concat(options.account_id, \",to_account_id.eq.\").concat(options.account_id));\n        }\n        if (options === null || options === void 0 ? void 0 : options.startDate) {\n            transferQuery = transferQuery.gte('transaction_date', options.startDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.endDate) {\n            transferQuery = transferQuery.lte('transaction_date', options.endDate);\n        }\n        if (options === null || options === void 0 ? void 0 : options.limit) {\n            transferQuery = transferQuery.limit(options.limit);\n        }\n        if (options === null || options === void 0 ? void 0 : options.offset) {\n            transferQuery = transferQuery.range(options.offset, options.offset + (options.limit || 20) - 1);\n        }\n        const { data: transferIds, error: transferError, count } = await transferQuery;\n        if (transferError) {\n            throw new Error(\"Failed to fetch transfers: \".concat(transferError.message));\n        }\n        if (!transferIds || transferIds.length === 0) {\n            return {\n                data: [],\n                count: count || 0\n            };\n        }\n        // Get the actual transfer transactions\n        const uniqueTransferIds = [\n            ...new Set(transferIds.map((t)=>t.transfer_id))\n        ];\n        const { data: transactions, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").in('transfer_id', uniqueTransferIds).eq('user_id', user.id).eq('transaction_type', 'transfer');\n        if (transactionError) {\n            throw new Error(\"Failed to fetch transfer transactions: \".concat(transactionError.message));\n        }\n        // Group transactions by transfer_id and create transfer objects\n        const transferMap = new Map();\n        transactions === null || transactions === void 0 ? void 0 : transactions.forEach((transaction)=>{\n            if (transaction.transfer_id) {\n                if (!transferMap.has(transaction.transfer_id)) {\n                    transferMap.set(transaction.transfer_id, []);\n                }\n                transferMap.get(transaction.transfer_id).push(transaction);\n            }\n        });\n        const transfers = [];\n        transferMap.forEach((transactionPair, transferId)=>{\n            if (transactionPair.length === 2) {\n                // Find the outgoing transaction (the one with fees or the one from the source account)\n                const outgoing = transactionPair.find((t)=>t.fees && t.fees > 0) || transactionPair[0];\n                const incoming = transactionPair.find((t)=>t.id !== outgoing.id);\n                transfers.push({\n                    id: transferId,\n                    amount: outgoing.amount,\n                    description: outgoing.description,\n                    from_account_id: outgoing.account_id,\n                    to_account_id: outgoing.to_account_id,\n                    transfer_id: transferId,\n                    transaction_date: outgoing.transaction_date,\n                    fees: outgoing.fees,\n                    user_id: user.id,\n                    from_account: outgoing.account,\n                    to_account: outgoing.to_account\n                });\n            }\n        });\n        // Sort by transaction date\n        transfers.sort((a, b)=>new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime());\n        return {\n            data: transfers,\n            count: count || 0\n        };\n    }\n    /**\n   * Get a specific transfer by transfer ID\n   */ static async getTransfer(transferId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data: transactions, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(\"\\n        *,\\n        category:categories(*),\\n        account:accounts!transactions_account_id_fkey(*),\\n        to_account:accounts!transactions_to_account_id_fkey(*)\\n      \").eq('transfer_id', transferId).eq('user_id', user.id).eq('transaction_type', 'transfer');\n        if (error) {\n            throw new Error(\"Failed to fetch transfer: \".concat(error.message));\n        }\n        if (!transactions || transactions.length !== 2) {\n            throw new Error('Transfer not found or incomplete');\n        }\n        // Find the outgoing transaction\n        const outgoing = transactions.find((t)=>t.fees && t.fees > 0) || transactions[0];\n        return {\n            id: transferId,\n            amount: outgoing.amount,\n            description: outgoing.description,\n            from_account_id: outgoing.account_id,\n            to_account_id: outgoing.to_account_id,\n            transfer_id: transferId,\n            transaction_date: outgoing.transaction_date,\n            fees: outgoing.fees,\n            user_id: user.id,\n            from_account: outgoing.account,\n            to_account: outgoing.to_account\n        };\n    }\n    /**\n   * Cancel a transfer (mark both transactions as cancelled)\n   */ static async cancelTransfer(transferId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').update({\n            transaction_status: 'cancelled',\n            updated_at: new Date().toISOString()\n        }).eq('transfer_id', transferId).eq('user_id', user.id).eq('transaction_type', 'transfer');\n        if (error) {\n            throw new Error(\"Failed to cancel transfer: \".concat(error.message));\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/shared/src/lib/transfers.ts\n"));

/***/ })

});