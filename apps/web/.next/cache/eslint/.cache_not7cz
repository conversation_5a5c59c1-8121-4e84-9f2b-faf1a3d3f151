[{"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx": "1", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/page.tsx": "2", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx": "3", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/layout.tsx": "4", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/signin/page.tsx": "5", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/signup/page.tsx": "6", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/dashboard/page.tsx": "7", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/profile/page.tsx": "8", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ProtectedRoute.tsx": "9", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx": "10", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx": "11", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx": "12", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/dashboard/page-simple.tsx": "13", "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Modal.tsx": "14"}, {"size": 1657, "mtime": 1750270548176, "results": "15", "hashOfConfig": "16"}, {"size": 658, "mtime": 1750241206332, "results": "17", "hashOfConfig": "16"}, {"size": 3746, "mtime": 1750266793808, "results": "18", "hashOfConfig": "16"}, {"size": 827, "mtime": 1750270796216, "results": "19", "hashOfConfig": "16"}, {"size": 6232, "mtime": 1750290054923, "results": "20", "hashOfConfig": "16"}, {"size": 5617, "mtime": 1750266805696, "results": "21", "hashOfConfig": "16"}, {"size": 7732, "mtime": 1750290067892, "results": "22", "hashOfConfig": "16"}, {"size": 3000, "mtime": 1750246797642, "results": "23", "hashOfConfig": "16"}, {"size": 742, "mtime": 1750241147503, "results": "24", "hashOfConfig": "16"}, {"size": 7623, "mtime": 1750341633583, "results": "25", "hashOfConfig": "16"}, {"size": 307, "mtime": 1750249647424, "results": "26", "hashOfConfig": "16"}, {"size": 19993, "mtime": 1750290075077, "results": "27", "hashOfConfig": "16"}, {"size": 3966, "mtime": 1750267596677, "results": "28", "hashOfConfig": "16"}, {"size": 4408, "mtime": 1750355107346, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qoy2r2", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/page.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/layout.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/signin/page.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/signup/page.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/dashboard/page.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/profile/page.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/expenses/page.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/dashboard/page-simple.tsx", [], [], "/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Modal.tsx", [], []]