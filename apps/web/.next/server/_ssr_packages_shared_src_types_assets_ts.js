"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_shared_src_types_assets_ts";
exports.ids = ["_ssr_packages_shared_src_types_assets_ts"];
exports.modules = {

/***/ "(ssr)/../../packages/shared/src/types/assets.ts":
/*!*************************************************!*\
  !*** ../../packages/shared/src/types/assets.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ASSET_CLASSES: () => (/* binding */ DEFAULT_ASSET_CLASSES)\n/* harmony export */ });\n// Asset class types and interfaces\n// Predefined asset classes with Indian tax implications\nconst DEFAULT_ASSET_CLASSES = [\n    {\n        name: 'Equity Shares',\n        class: 'stocks',\n        sub_class: 'large_cap',\n        description: 'Listed equity shares on stock exchanges',\n        risk_level: 'high',\n        liquidity: 'high',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'equity',\n        ltcg_period_months: 12,\n        ltcg_tax_rate: 10,\n        stcg_tax_rate: 15,\n        dividend_tax_rate: 0 // Dividend income taxed as per slab\n    },\n    {\n        name: 'Equity Mutual Funds',\n        class: 'mutual_funds',\n        sub_class: 'equity_funds',\n        description: 'Mutual funds investing primarily in equity',\n        risk_level: 'high',\n        liquidity: 'high',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'equity',\n        ltcg_period_months: 12,\n        ltcg_tax_rate: 10,\n        stcg_tax_rate: 15\n    },\n    {\n        name: 'Debt Mutual Funds',\n        class: 'mutual_funds',\n        sub_class: 'debt_funds',\n        description: 'Mutual funds investing primarily in debt instruments',\n        risk_level: 'medium',\n        liquidity: 'high',\n        typical_holding_period: 'medium_term',\n        tax_treatment: 'debt',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30 // As per income tax slab\n    },\n    {\n        name: 'Government Bonds',\n        class: 'bonds',\n        sub_class: 'government_bonds',\n        description: 'Government issued bonds and securities',\n        risk_level: 'low',\n        liquidity: 'medium',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'debt',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30\n    },\n    {\n        name: 'Corporate Bonds',\n        class: 'bonds',\n        sub_class: 'corporate_bonds',\n        description: 'Corporate issued bonds and debentures',\n        risk_level: 'medium',\n        liquidity: 'medium',\n        typical_holding_period: 'medium_term',\n        tax_treatment: 'debt',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30\n    },\n    {\n        name: 'Exchange Traded Funds',\n        class: 'etfs',\n        sub_class: 'equity_etfs',\n        description: 'Exchange traded funds tracking various indices',\n        risk_level: 'medium',\n        liquidity: 'high',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'equity',\n        ltcg_period_months: 12,\n        ltcg_tax_rate: 10,\n        stcg_tax_rate: 15\n    },\n    {\n        name: 'Real Estate Investment Trusts',\n        class: 'real_estate',\n        sub_class: 'reits',\n        description: 'REITs investing in real estate properties',\n        risk_level: 'medium',\n        liquidity: 'medium',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'other',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30\n    },\n    {\n        name: 'Gold ETFs',\n        class: 'commodities',\n        sub_class: 'precious_metals',\n        description: 'Gold exchange traded funds',\n        risk_level: 'medium',\n        liquidity: 'high',\n        typical_holding_period: 'long_term',\n        tax_treatment: 'other',\n        ltcg_period_months: 36,\n        ltcg_tax_rate: 20,\n        stcg_tax_rate: 30\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/types/assets.ts\n");

/***/ })

};
;