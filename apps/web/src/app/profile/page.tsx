'use client'

import { useAuth } from '../../contexts/AuthContext'
import { ProfileProvider } from '../../contexts/ProfileContext'
import { ProfileForm } from '../../components/ProfileForm'
import { DataExport } from '../../components/DataExport'
import ProtectedRoute from '../../components/ProtectedRoute'
import { useState } from 'react'
import Navbar from '../../components/Navbar'

export default function ProfilePage() {
  const { user, signOut } = useAuth()
  const [activeTab, setActiveTab] = useState<'profile' | 'data'>('profile')

  const handleSignOut = async () => {
    await signOut()
  }

  return (
    <ProtectedRoute>
      <ProfileProvider>
        <div className="min-h-screen bg-background">
          <Navbar currentPage="profile" />

          <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="space-y-8">
              {/* <PERSON>er */}
              <div>
                <h1 className="text-4xl font-bold text-text-primary">Profile Settings</h1>
                <p className="text-text-secondary text-lg mt-2">Manage your account settings and preferences</p>
              </div>

              {/* Combined Tabs and Content */}
              <div className="bg-surface-elevated rounded-xl shadow-sm border border-border-light">
                {/* Tabs */}
                <div className="px-6 border-b border-border-light">
                  <nav className="flex space-x-8">
                    <button
                      onClick={() => setActiveTab('profile')}
                      className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === 'profile'
                          ? 'border-primary-blue text-primary-blue'
                          : 'border-transparent text-text-secondary hover:text-text-primary hover:border-border-light'
                      }`}
                    >
                      Profile Information
                    </button>
                    <button
                      onClick={() => setActiveTab('data')}
                      className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === 'data'
                          ? 'border-primary-blue text-primary-blue'
                          : 'border-transparent text-text-secondary hover:text-text-primary hover:border-border-light'
                      }`}
                    >
                      Data Export
                    </button>
                  </nav>
                </div>
                
                {/* Content */}
                <div className="px-6 py-8">
                  {activeTab === 'profile' && (
                    <ProfileForm />
                  )}
                  {activeTab === 'data' && (
                    <DataExport />
                  )}
                </div>
              </div>
            </div>
          </main>
        </div>
      </ProfileProvider>
    </ProtectedRoute>
  )
}