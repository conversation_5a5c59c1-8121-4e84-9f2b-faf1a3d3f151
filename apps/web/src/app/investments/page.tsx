'use client'

import { useState, useEffect } from 'react'
import {
  AccountService,
  InvestmentService,
  type IAccount,
  type IInvestmentTransaction
} from '@shared/index'
import { InvestmentForm } from '../../components/InvestmentForm'
import { Modal } from '@/components/Modal'
import { toast } from 'react-hot-toast'
import ProtectedRoute from '@/components/ProtectedRoute'
import Navbar from '../../components/Navbar'

export default function InvestmentsPage() {
  const [investmentAccounts, setInvestmentAccounts] = useState<IAccount[]>([])
  const [investmentTransactions, setInvestmentTransactions] = useState<IInvestmentTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [showInvestmentForm, setShowInvestmentForm] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  useEffect(() => {
    loadInvestmentData()
  }, [refreshKey])

  const loadInvestmentData = async () => {
    try {
      setLoading(true)
      const [accounts, transactions] = await Promise.all([
        AccountService.getAccounts({ account_type: 'investment' }),
        InvestmentService.getInvestmentTransactions()
      ])
      setInvestmentAccounts(accounts)
      setInvestmentTransactions(transactions.data)
    } catch (error) {
      console.error('Failed to load investment data:', error)
      toast.error('Failed to load investment data')
    } finally {
      setLoading(false)
    }
  }

  const handleInvestmentSubmit = async (data: any) => {
    try {
      setSubmitting(true)
      toast.success('Investment transaction created successfully!')
      setShowInvestmentForm(false)
      setRefreshKey(prev => prev + 1)
    } catch (error) {
      console.error('Error submitting investment transaction:', error)
      toast.error('Failed to submit investment transaction')
    } finally {
      setSubmitting(false)
    }
  }

  const calculatePortfolioValue = () => {
    return investmentAccounts.reduce((total, account) => total + (account.current_balance || 0), 0)
  }

  const calculateTotalInvested = () => {
    return investmentTransactions
      .filter(t => t.transaction_type === 'investment_buy')
      .reduce((total, t) => total + t.amount, 0)
  }

  const calculateTotalReturns = () => {
    const totalValue = calculatePortfolioValue()
    const totalInvested = calculateTotalInvested()
    return totalValue - totalInvested
  }

  const getReturnPercentage = () => {
    const totalInvested = calculateTotalInvested()
    if (totalInvested === 0) return 0
    return ((calculateTotalReturns() / totalInvested) * 100)
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background">
          <Navbar currentPage="investments" />
          <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-border border-t-primary-blue"></div>
            </div>
          </main>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar currentPage="investments" />

        <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-8">
            {/* Page Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
              <div>
                <h1 className="text-4xl font-bold text-text-primary">Investments</h1>
                <p className="text-text-secondary text-lg mt-2">Manage your investment portfolio and track performance</p>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setShowInvestmentForm(true)}
                  className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  Add Investment
                </button>
              </div>
            </div>

            {/* Portfolio Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-surface border border-border rounded-xl p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-text-secondary text-sm font-medium">Portfolio Value</p>
                    <p className="text-2xl font-bold text-text-primary">${calculatePortfolioValue().toFixed(2)}</p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-surface border border-border rounded-xl p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-text-secondary text-sm font-medium">Total Invested</p>
                    <p className="text-2xl font-bold text-text-primary">${calculateTotalInvested().toFixed(2)}</p>
                  </div>
                  <div className="p-3 bg-green-100 rounded-lg">
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-surface border border-border rounded-xl p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-text-secondary text-sm font-medium">Total Returns</p>
                    <p className={`text-2xl font-bold ${calculateTotalReturns() >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      ${calculateTotalReturns().toFixed(2)}
                    </p>
                  </div>
                  <div className={`p-3 rounded-lg ${calculateTotalReturns() >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                    <svg className={`w-6 h-6 ${calculateTotalReturns() >= 0 ? 'text-green-600' : 'text-red-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={calculateTotalReturns() >= 0 ? "M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" : "M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"} />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-surface border border-border rounded-xl p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-text-secondary text-sm font-medium">Return %</p>
                    <p className={`text-2xl font-bold ${getReturnPercentage() >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {getReturnPercentage().toFixed(2)}%
                    </p>
                  </div>
                  <div className={`p-3 rounded-lg ${getReturnPercentage() >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                    <svg className={`w-6 h-6 ${getReturnPercentage() >= 0 ? 'text-green-600' : 'text-red-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Investment Accounts */}
            <div className="bg-surface border border-border rounded-xl p-6">
              <h2 className="text-xl font-semibold text-text-primary mb-6">Investment Accounts</h2>
              {investmentAccounts.length === 0 ? (
                <div className="text-center py-8">
                  <svg className="w-12 h-12 text-text-secondary mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  <p className="text-text-secondary">No investment accounts found. Create one to get started.</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {investmentAccounts.map((account) => (
                    <div key={account.id} className="bg-background border border-border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium text-text-primary">{account.name}</h3>
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          {account.account_type}
                        </span>
                      </div>
                      <p className="text-2xl font-bold text-text-primary">${account.current_balance?.toFixed(2) || '0.00'}</p>
                      {account.institution_name && (
                        <p className="text-sm text-text-secondary mt-1">{account.institution_name}</p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Recent Investment Transactions */}
            <div className="bg-surface border border-border rounded-xl p-6">
              <h2 className="text-xl font-semibold text-text-primary mb-6">Recent Transactions</h2>
              {investmentTransactions.length === 0 ? (
                <div className="text-center py-8">
                  <svg className="w-12 h-12 text-text-secondary mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  <p className="text-text-secondary">No investment transactions found.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-border">
                        <th className="text-left py-3 px-4 font-medium text-text-secondary">Date</th>
                        <th className="text-left py-3 px-4 font-medium text-text-secondary">Type</th>
                        <th className="text-left py-3 px-4 font-medium text-text-secondary">Symbol</th>
                        <th className="text-left py-3 px-4 font-medium text-text-secondary">Quantity</th>
                        <th className="text-left py-3 px-4 font-medium text-text-secondary">Price</th>
                        <th className="text-left py-3 px-4 font-medium text-text-secondary">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {investmentTransactions.slice(0, 10).map((transaction) => (
                        <tr key={transaction.id} className="border-b border-border hover:bg-surface-elevated">
                          <td className="py-3 px-4 text-text-primary">
                            {new Date(transaction.transaction_date).toLocaleDateString()}
                          </td>
                          <td className="py-3 px-4">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              transaction.transaction_type === 'investment_buy' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {transaction.transaction_type === 'investment_buy' ? 'Buy' : 'Sell'}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-text-primary font-medium">
                            {transaction.investment_symbol}
                          </td>
                          <td className="py-3 px-4 text-text-primary">
                            {transaction.investment_quantity}
                          </td>
                          <td className="py-3 px-4 text-text-primary">
                            ${transaction.investment_price?.toFixed(2)}
                          </td>
                          <td className="py-3 px-4 text-text-primary font-medium">
                            ${transaction.amount.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* Investment Form Modal */}
            <Modal
              isOpen={showInvestmentForm}
              onClose={() => setShowInvestmentForm(false)}
              title="Investment Transaction"
              size="xl"
            >
              <InvestmentForm
                onSubmit={handleInvestmentSubmit}
                loading={submitting}
                compact={true}
              />
            </Modal>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}
