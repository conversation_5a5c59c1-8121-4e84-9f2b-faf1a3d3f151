'use client'

import { useState } from 'react'
import { BudgetDashboard } from '../../components/BudgetDashboard'
import ProtectedRoute from '@/components/ProtectedRoute'
import Navbar from '../../components/Navbar'

export default function BudgetsPage() {
  const [showForm, setShowForm] = useState(false)

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar currentPage="budgets" />

        <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-8">
            {/* Page Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
              <div>
                <h1 className="text-4xl font-bold text-text-primary">Budgets</h1>
                <p className="text-text-secondary text-lg mt-2">Set and track your spending limits by category</p>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setShowForm(true)}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Create Budget
                </button>
              </div>
            </div>

            <BudgetDashboard showForm={showForm} setShowForm={setShowForm} />
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}