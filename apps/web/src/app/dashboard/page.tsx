'use client'

import { useState, useEffect, useCallback } from 'react'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuth } from '../../contexts/AuthContext'
import { AnalyticsService, type IAnalyticsData } from '@repo/shared'
import { OnboardingFlow } from '../../components/OnboardingFlow'
import { DueTransactionsNotification } from '../../components/DueTransactionsNotification'
import Navbar from '../../components/Navbar'
import AnalyticsDashboard from '@/components/AnalyticsDashboard'

export default function DashboardPage() {
  const { user, signOut } = useAuth()
  const [analyticsData, setAnalyticsData] = useState<IAnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dateRange, setDateRange] = useState<{ startDate: string; endDate: string }>(() => {
    const endDate = new Date().toISOString().split('T')[0]
    const startDate = new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    return { startDate, endDate }
  })

  const handleSignOut = async () => {
    await signOut()
  }

  const fetchAnalyticsData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await AnalyticsService.getAnalyticsData(dateRange)
      setAnalyticsData(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics data')
    } finally {
      setLoading(false)
    }
  }, [dateRange])

  useEffect(() => {
    if (user) {
      fetchAnalyticsData()
    }
  }, [user, dateRange, fetchAnalyticsData])

  const handleDateRangeChange = (newDateRange: { startDate: string; endDate: string }) => {
    setDateRange(newDateRange)
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar currentPage="dashboard" />

        <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-8">
            {/* Page Header */}
            <div>
              <h1 className="text-4xl font-bold text-text-primary">Dashboard</h1>
              <p className="text-text-secondary text-lg mt-2">Overview of your spending patterns and financial insights</p>
            </div>

            {/* Due Transactions Notification */}
            <DueTransactionsNotification 
              onTransactionCreated={fetchAnalyticsData}
              className="mb-6"
            />
            
            {loading && (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-border border-t-primary-blue"></div>
              </div>
            )}
            
            {error && (
              <div className="bg-error-red/5 border border-error-red/20 rounded-xl p-6 mb-6">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-error-red/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-error-red" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-sm font-semibold text-error-red mb-1">Error loading analytics</h3>
                    <p className="text-sm text-text-secondary mb-3">{error}</p>
                    <button
                      onClick={fetchAnalyticsData}
                      className="bg-error-red/10 hover:bg-error-red/20 text-error-red px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              </div>
            )}
            
            {analyticsData && !loading && (
              <AnalyticsDashboard 
                data={analyticsData}
                dateRange={dateRange}
                onDateRangeChange={handleDateRangeChange}
              />
            )}
          </div>
        </main>
        
        <OnboardingFlow onComplete={() => {}} />
      </div>
    </ProtectedRoute>
  )
}