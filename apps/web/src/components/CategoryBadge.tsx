import React from 'react'

export interface CategoryBadgeProps {
  name: string
  color?: string
  icon?: React.ReactNode
  size?: 'small' | 'medium' | 'large'
  variant?: 'default' | 'outline' | 'solid'
  className?: string
}

export function CategoryBadge({
  name,
  color = '#3B82F6',
  icon,
  size = 'medium',
  variant = 'default',
  className = ''
}: CategoryBadgeProps) {
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'px-2 py-1 text-xs'
      case 'large':
        return 'px-4 py-2 text-base'
      default:
        return 'px-3 py-1.5 text-sm'
    }
  }

  const getVariantClasses = () => {
    switch (variant) {
      case 'outline':
        return 'border-2 bg-transparent'
      case 'solid':
        return 'text-white'
      default:
        return 'bg-opacity-10 border border-opacity-20'
    }
  }

  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 'w-3 h-3'
      case 'large':
        return 'w-5 h-5'
      default:
        return 'w-4 h-4'
    }
  }

  const styles = {
    backgroundColor: variant === 'solid' ? color : variant === 'default' ? `${color}10` : 'transparent',
    borderColor: color,
    color: variant === 'solid' ? 'white' : color
  }

  return (
    <div 
      className={`inline-flex items-center gap-2 rounded-full font-medium transition-all ${getSizeClasses()} ${getVariantClasses()} ${className}`}
      style={styles}
    >
      {icon && (
        <span className={getIconSize()}>
          {icon}
        </span>
      )}
      <span>{name}</span>
    </div>
  )
}

// Common category icons
export const CategoryIcons = {
  Food: (
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5.5M7 13l2.5-2.5m0 0L17 18m-7.5-7.5L17 18" />
    </svg>
  ),
  Transport: (
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
    </svg>
  ),
  Entertainment: (
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  Shopping: (
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z" />
    </svg>
  ),
  Healthcare: (
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
    </svg>
  ),
  Bills: (
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  ),
  Income: (
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
    </svg>
  )
}

export default CategoryBadge