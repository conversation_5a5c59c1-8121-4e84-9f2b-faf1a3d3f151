import React, { useState, useEffect } from 'react'
import { BudgetService, type IBudgetWithProgress, type IBudget, useCurrencyStore } from '@repo/shared'

interface BudgetListProps {
  onEdit?: (budget: IBudget) => void
  onDelete?: (budgetId: string) => void
  refreshTrigger?: number
}

export const BudgetList: React.FC<BudgetListProps> = ({
  onEdit,
  onDelete,
  refreshTrigger,
}) => {
  const [budgets, setBudgets] = useState<IBudgetWithProgress[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const { formatCurrency } = useCurrencyStore()

  useEffect(() => {
    loadBudgets()
  }, [refreshTrigger])

  const loadBudgets = async () => {
    try {
      setLoading(true)
      setError(null)
      const budgetsWithProgress = await BudgetService.getBudgetsWithProgress()
      setBudgets(budgetsWithProgress)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load budgets')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (budgetId: string) => {
    if (!confirm('Are you sure you want to delete this budget?')) {
      return
    }

    try {
      setDeletingId(budgetId)
      await BudgetService.deleteBudget(budgetId)
      setBudgets(budgets.filter(b => b.id !== budgetId))
      onDelete?.(budgetId)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete budget')
    } finally {
      setDeletingId(null)
    }
  }


  const formatPeriod = (period: string) => {
    return period.charAt(0).toUpperCase() + period.slice(1)
  }

  const getProgressColor = (progress: number, isOverBudget: boolean) => {
    if (isOverBudget) return 'bg-red-500'
    if (progress >= 80) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const getProgressTextColor = (progress: number, isOverBudget: boolean) => {
    if (isOverBudget) return 'text-red-600'
    if (progress >= 80) return 'text-yellow-600'
    return 'text-green-600'
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 text-red-700 rounded-lg">
        <p className="font-medium">{error}</p>
        <button
          onClick={loadBudgets}
          className="mt-3 text-sm text-blue-600 font-medium hover:underline"
        >
          Try again
        </button>
      </div>
    )
  }

  if (budgets.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-6">
          <svg className="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No budgets yet</h3>
        <p className="text-gray-600">Create your first budget to start tracking your spending.</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {budgets.map((budget) => (
        <div
          key={budget.id}
          className="bg-white rounded-xl border border-gray-100 p-6 shadow-sm hover:shadow-md transition-all duration-200"
        >
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">{budget.name}</h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span className="font-medium">{formatPeriod(budget.period)}</span>
                {budget.category && (
                  <span className="flex items-center bg-gray-100 px-2 py-1 rounded-full">
                    <span className="mr-1">{budget.category.icon}</span>
                    <span className="font-medium">{budget.category.name}</span>
                  </span>
                )}
              </div>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => onEdit?.(budget)}
                className="text-blue-600 hover:text-blue-700 text-sm font-semibold px-4 py-2 border border-blue-200 hover:border-blue-300 bg-blue-50 hover:bg-blue-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
              >
                Edit
              </button>
              <button
                onClick={() => handleDelete(budget.id)}
                disabled={deletingId === budget.id}
                className="text-red-600 hover:text-red-700 text-sm font-semibold px-4 py-2 border border-red-200 hover:border-red-300 bg-red-50 hover:bg-red-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {deletingId === budget.id ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>

          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span className="font-medium">Spent: {formatCurrency(budget.spent)}</span>
              <span className="font-medium">Budget: {formatCurrency(budget.amount)}</span>
            </div>
            <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
              <div
                className={`h-full rounded-full transition-all duration-300 ${getProgressColor(budget.progress, budget.isOverBudget)}`}
                style={{ width: `${Math.min(100, budget.progress)}%` }}
              ></div>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div className={`text-sm font-medium ${getProgressTextColor(budget.progress, budget.isOverBudget)}`}>
              {budget.isOverBudget ? (
                <span>Over budget by {formatCurrency(budget.spent - budget.amount)}</span>
              ) : (
                <span>{formatCurrency(budget.remaining)} remaining</span>
              )}
            </div>
            <div className="text-sm text-gray-600">
              {budget.progress.toFixed(1)}% used
            </div>
          </div>

          {budget.isOverBudget && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-700">
              <span className="font-semibold">⚠️ Over Budget:</span> You've exceeded your budget for this period
            </div>
          )}

          {!budget.isOverBudget && budget.progress >= 80 && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm text-yellow-700">
              <span className="font-semibold">⚠️ Warning:</span> You're approaching your budget limit
            </div>
          )}
        </div>
      ))}
    </div>
  )
}