import React from 'react'
import type { ImportResult } from '@repo/shared'

export interface ImportResultDialogProps {
  result: ImportResult
  onClose: () => void
  className?: string
}

export function ImportResultDialog({ result, onClose, className = '' }: ImportResultDialogProps) {
  const hasErrors = result.errors.length > 0

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`bg-white rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden ${className}`}>
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-900">
              Import Results
            </h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto">
          {/* Success Summary */}
          <div className="mb-6">
            <div className="flex items-center space-x-3 mb-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                hasErrors ? 'bg-yellow-100' : 'bg-green-100'
              }`}>
                {hasErrors ? (
                  <span className="text-yellow-600 text-lg">⚠</span>
                ) : (
                  <span className="text-green-600 text-lg">✓</span>
                )}
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {hasErrors ? 'Import Completed with Issues' : 'Import Successful'}
                </h3>
                <p className="text-gray-600">
                  {result.success} transaction{result.success !== 1 ? 's' : ''} imported successfully
                  {hasErrors && `, ${result.errors.length} error${result.errors.length !== 1 ? 's' : ''}`}
                </p>
              </div>
            </div>
          </div>

          {/* Success Details */}
          {result.success > 0 && (
            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3">Successfully Imported Transactions</h4>
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="space-y-2">
                  {result.transactions.slice(0, 5).map((transaction, index) => (
                    <div key={transaction.id} className="flex justify-between items-center text-sm">
                      <div>
                        <span className="font-medium">{transaction.investment_symbol}</span>
                        <span className="text-gray-600 ml-2">
                          {transaction.transaction_type === 'investment_buy' ? 'BUY' : 'SELL'} {transaction.investment_quantity} shares
                        </span>
                      </div>
                      <span className="text-gray-600">
                        ${transaction.amount.toFixed(2)}
                      </span>
                    </div>
                  ))}
                  {result.transactions.length > 5 && (
                    <div className="text-sm text-gray-500 text-center pt-2 border-t border-green-200">
                      ... and {result.transactions.length - 5} more transactions
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Error Details */}
          {hasErrors && (
            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3">Import Errors</h4>
              <div className="bg-red-50 border border-red-200 rounded-md p-4 max-h-60 overflow-y-auto">
                <div className="space-y-3">
                  {result.errors.map((error, index) => (
                    <div key={index} className="border-b border-red-200 last:border-b-0 pb-3 last:pb-0">
                      <div className="flex justify-between items-start mb-1">
                        <span className="text-sm font-medium text-red-800">
                          Row {error.row}
                        </span>
                        <span className="text-xs text-red-600">
                          Error
                        </span>
                      </div>
                      <p className="text-sm text-red-700 mb-2">
                        {error.error}
                      </p>
                      <div className="text-xs text-red-600 bg-red-100 rounded p-2">
                        <strong>Data:</strong> {JSON.stringify(error.data, null, 2)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Next Steps */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h4 className="font-medium text-blue-900 mb-2">Next Steps</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Review your imported transactions in the Investments tab</li>
              {hasErrors && (
                <li>• Fix the errors in your CSV file and re-import the failed rows</li>
              )}
              <li>• Check your portfolio holdings and performance metrics</li>
              <li>• Consider setting up recurring imports for regular updates</li>
            </ul>
          </div>
        </div>

        <div className="p-6 border-t bg-gray-50">
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ImportResultDialog
