import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { budgetFormInputSchema, type BudgetFormInputData, BudgetService, ExpenseService, type IBudget, type ICategory } from '@repo/shared'

interface BudgetFormProps {
  budget?: IBudget
  onSuccess?: (budget: IBudget) => void
  onCancel?: () => void
}

export const BudgetForm: React.FC<BudgetFormProps> = ({
  budget,
  onSuccess,
  onCancel,
}) => {
  const [categories, setCategories] = useState<ICategory[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<BudgetFormInputData>({
    resolver: zodResolver(budgetFormInputSchema),
    defaultValues: {
      name: budget?.name || '',
      amount: budget?.amount?.toString() || '',
      period: budget?.period || 'monthly',
      category_id: budget?.category_id || '',
      start_date: budget?.start_date ? new Date(budget.start_date) : new Date(),
      end_date: budget?.end_date ? new Date(budget.end_date) : null,
    },
  })

  const selectedPeriod = watch('period')

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    try {
      const fetchedCategories = await ExpenseService.getCategories()
      setCategories(fetchedCategories.filter(cat => cat.type === 'expense'))
    } catch (err) {
      console.error('Failed to load categories:', err)
    }
  }

  const onSubmit = async (data: BudgetFormInputData) => {
    setLoading(true)
    setError(null)

    try {
      // Transform the data
      const transformedData = {
        name: data.name,
        amount: parseFloat(data.amount.replace(/[^\d.-]/g, '')),
        period: data.period,
        category_id: data.category_id || undefined,
        start_date: data.start_date,
        end_date: data.end_date,
      }

      let savedBudget: IBudget
      
      if (budget) {
        savedBudget = await BudgetService.updateBudget(budget.id, transformedData)
      } else {
        savedBudget = await BudgetService.createBudget(transformedData)
      }

      reset()
      onSuccess?.(savedBudget)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save budget')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    reset()
    onCancel?.()
  }

  return (
    <div className="bg-white rounded-xl border border-gray-100 p-6 shadow-sm hover:shadow-md transition-all duration-200">
      <h2 className="text-xl font-semibold mb-6 text-gray-900">
        {budget ? 'Edit Budget' : 'Create New Budget'}
      </h2>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-lg">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-2">
            Budget Name
          </label>
          <input
            type="text"
            id="name"
            {...register('name')}
            className="w-full bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 text-base transition-all focus:border-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-600/10 focus:bg-white"
            placeholder="e.g., Monthly Groceries"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="amount" className="block text-sm font-semibold text-gray-700 mb-2">
            Budget Amount
          </label>
          <input
            type="text"
            id="amount"
            {...register('amount')}
            className="w-full bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 text-base transition-all focus:border-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-600/10 focus:bg-white"
            placeholder="0.00"
          />
          {errors.amount && (
            <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="period" className="block text-sm font-semibold text-gray-700 mb-2">
            Budget Period
          </label>
          <select
            id="period"
            {...register('period')}
            className="w-full bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 appearance-none cursor-pointer transition-all focus:border-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-600/10 focus:bg-white"
          >
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
          </select>
          {errors.period && (
            <p className="mt-1 text-sm text-red-600">{errors.period.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="category_id" className="block text-sm font-semibold text-gray-700 mb-2">
            Category (Optional)
          </label>
          <select
            id="category_id"
            {...register('category_id')}
            className="w-full bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 appearance-none cursor-pointer transition-all focus:border-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-600/10 focus:bg-white"
          >
            <option value="">All Categories</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.icon} {category.name}
              </option>
            ))}
          </select>
          {errors.category_id && (
            <p className="mt-1 text-sm text-red-600">{errors.category_id.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="start_date" className="block text-sm font-semibold text-gray-700 mb-2">
            Start Date
          </label>
          <input
            type="date"
            id="start_date"
            {...register('start_date', { 
              valueAsDate: true,
              setValueAs: (value) => value === '' ? new Date() : new Date(value)
            })}
            className="w-full bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 text-base transition-all focus:border-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-600/10 focus:bg-white"
          />
          {errors.start_date && (
            <p className="mt-1 text-sm text-red-600">{errors.start_date.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="end_date" className="block text-sm font-semibold text-gray-700 mb-2">
            End Date (Optional)
          </label>
          <input
            type="date"
            id="end_date"
            {...register('end_date', { 
              setValueAs: (value) => {
                if (value === '' || !value) return undefined
                const date = new Date(value)
                return isNaN(date.getTime()) ? undefined : date
              }
            })}
            className="w-full bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 text-base transition-all focus:border-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-600/10 focus:bg-white"
          />
          <p className="mt-1 text-sm text-gray-500">
            Leave empty for recurring {selectedPeriod} budget
          </p>
          {errors.end_date && (
            <p className="mt-1 text-sm text-red-600">{errors.end_date.message}</p>
          )}
        </div>

        <div className="flex justify-end space-x-3 pt-6">
          <button
            type="button"
            onClick={handleCancel}
            className="bg-gray-50 text-gray-700 border border-gray-200 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {loading ? 'Saving...' : budget ? 'Update Budget' : 'Create Budget'}
          </button>
        </div>
      </form>
    </div>
  )
}