import React, { useEffect, useState } from 'react'

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl'
  subtitle?: string
  icon?: React.ReactNode
}

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'lg',
  subtitle,
  icon
}) => {
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true)
    } else {
      const timer = setTimeout(() => setIsAnimating(false), 300)
      return () => clearTimeout(timer)
    }
  }, [isOpen])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  if (!isOpen && !isAnimating) return null

  const getSizeClasses = () => {
    const sizes = {
      sm: 'max-w-md',
      md: 'max-w-2xl',
      lg: 'max-w-4xl',
      xl: 'max-w-6xl'
    }
    return sizes[size] || sizes.lg
  }

  return (
    <>
      {/* Backdrop with animation */}
      <div
        className={`fixed inset-0 z-50 transition-all duration-300 ${
          isOpen ? 'bg-black/70 backdrop-blur-md' : 'bg-black/0 backdrop-blur-none'
        }`}
        onClick={onClose}
      />
      
      {/* Modal Container */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 pointer-events-none">
        <div
          className={`
            ${getSizeClasses()} w-full transform transition-all duration-300 pointer-events-auto
            ${isOpen ? 'scale-100 opacity-100 translate-y-0' : 'scale-95 opacity-0 translate-y-4'}
          `}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="bg-surface-elevated dark:bg-dark-surface-elevated rounded-2xl shadow-2xl overflow-hidden">
            {/* Gradient accent line */}
            <div className="h-1 bg-gradient-to-r from-primary-blue to-primary-purple" />
            
            {/* Header */}
            <div className="relative px-8 pt-8 pb-6">
              {/* Background pattern */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0 bg-gradient-to-br from-primary-blue to-primary-purple" />
                <svg className="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg">
                  <defs>
                    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                      <circle cx="20" cy="20" r="1" fill="currentColor" />
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#grid)" />
                </svg>
              </div>
              
              <div className="relative flex items-start justify-between">
                <div className="flex items-start gap-4">
                  {/* Icon */}
                  <div className="flex-shrink-0">
                    {icon || (
                      <div className="w-12 h-12 bg-gradient-to-br from-primary-blue to-primary-purple rounded-xl flex items-center justify-center shadow-lg transform rotate-3 hover:rotate-6 transition-transform">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                    )}
                  </div>
                  
                  {/* Title and subtitle */}
                  <div>
                    <h2 className="text-2xl font-bold text-text-primary dark:text-dark-text-primary leading-tight">
                      {title}
                    </h2>
                    {subtitle && (
                      <p className="mt-1 text-sm text-text-secondary dark:text-dark-text-secondary">
                        {subtitle}
                      </p>
                    )}
                  </div>
                </div>
                
                {/* Close button */}
                <button
                  onClick={onClose}
                  className="flex-shrink-0 -mt-2 -mr-2 p-2 rounded-xl text-text-tertiary hover:text-text-primary dark:hover:text-dark-text-primary hover:bg-surface dark:hover:bg-dark-surface transition-all duration-200 group"
                  aria-label="Close modal"
                >
                  <svg className="w-5 h-5 transform group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            
            {/* Separator with gradient */}
            <div className="px-8">
              <div className="h-px bg-gradient-to-r from-transparent via-border dark:via-dark-border to-transparent" />
            </div>
            
            {/* Content */}
            <div className="px-8 py-6 pb-8 max-h-[calc(100vh-12rem)] overflow-y-auto custom-scrollbar">
              <div className="text-text-secondary dark:text-dark-text-secondary">
                {children}
              </div>
            </div>
            
          </div>
        </div>
      </div>

      {/* Custom scrollbar styles */}
      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: var(--border);
          border-radius: 3px;
        }
        .dark .custom-scrollbar::-webkit-scrollbar-thumb {
          background: var(--dark-border);
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: var(--border-light);
        }
        .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: var(--dark-border-light);
        }
      `}</style>
    </>
  )
}