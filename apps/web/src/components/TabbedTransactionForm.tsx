import React, { useState, useEffect } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  mainTransactionFormInputSchema,
  mainTransactionFormSchema,
  type MainTransactionFormInputData,
  type ICategory,
  type IAccount,
  AccountService,
  CategoryService
} from '@shared/index'
import toast from 'react-hot-toast'

interface TabbedTransactionFormProps {
  onSubmit: (data: any) => Promise<void>
  loading?: boolean
  className?: string
  initialData?: any
  compact?: boolean
}

type TabType = 'expense' | 'income' | 'transfer'

export const TabbedTransactionForm: React.FC<TabbedTransactionFormProps> = ({
  onSubmit,
  loading = false,
  className = "",
  initialData,
  compact = false
}) => {
  const [activeTab, setActiveTab] = useState<TabType>(initialData?.transaction_type || 'expense')
  const [categories, setCategories] = useState<ICategory[]>([])
  const [accounts, setAccounts] = useState<IAccount[]>([])
  const [loadingData, setLoadingData] = useState(true)

  const form = useForm<MainTransactionFormInputData>({
    resolver: zodResolver(mainTransactionFormInputSchema),
    defaultValues: {
      amount: initialData?.amount?.toString() || '',
      category_id: initialData?.category_id || '',
      account_id: initialData?.account_id || '',
      to_account_id: initialData?.to_account_id || '',
      description: initialData?.description || '',
      transaction_date: initialData?.transaction_date || new Date(),
      transaction_type: initialData?.transaction_type || 'expense',
      fees: initialData?.fees?.toString() || '',
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = form

  const transactionType = watch('transaction_type')
  const accountId = watch('account_id')

  useEffect(() => {
    const loadData = async () => {
      try {
        const [categoriesData, accountsData] = await Promise.all([
          CategoryService.getCategories(),
          AccountService.getAccounts()
        ])
        setCategories(categoriesData)
        setAccounts(accountsData)
      } catch (error) {
        console.error('Error loading form data:', error)
        toast.error('Failed to load form data')
      } finally {
        setLoadingData(false)
      }
    }

    loadData()
  }, [])

  // Update transaction type when tab changes
  useEffect(() => {
    setValue('transaction_type', activeTab)
  }, [activeTab, setValue])

  // Filter accounts based on transaction type
  const getFilteredAccounts = (purpose: 'source' | 'destination') => {
    switch (purpose) {
      case 'destination':
        return accounts.filter(acc => acc.id !== accountId && acc.account_type !== 'investment')
      default:
        return accounts.filter(acc => acc.account_type !== 'investment')
    }
  }

  // Filter categories based on transaction type
  const getFilteredCategories = () => {
    return categories.filter(cat => {
      if (activeTab === 'expense') return cat.type === 'expense'
      if (activeTab === 'income') return cat.type === 'income'
      return true // For transfers, we might not need specific categories
    })
  }

  const handleFormSubmit = async (data: MainTransactionFormInputData) => {
    try {
      const validatedData = mainTransactionFormSchema.parse(data)
      await onSubmit(validatedData)
      if (!initialData) {
        reset()
      }
    } catch (error) {
      console.error('Error submitting transaction:', error)
      toast.error('Failed to submit transaction')
    }
  }

  if (loadingData) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-4 border-border border-t-primary-blue"></div>
      </div>
    )
  }

  const tabs = [
    { id: 'expense' as TabType, label: 'Expenses', icon: '💸' },
    { id: 'income' as TabType, label: 'Income', icon: '💰' },
    { id: 'transfer' as TabType, label: 'Transfer', icon: '🔄' }
  ]

  return (
    <div className={`${className}`}>
      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-surface-secondary rounded-lg p-1 mb-6">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            type="button"
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-md text-sm font-medium transition-all ${
              activeTab === tab.id
                ? 'bg-primary-blue text-white shadow-sm'
                : 'text-text-secondary hover:text-text-primary hover:bg-surface'
            }`}
          >
            <span>{tab.icon}</span>
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Form Content */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className={`${compact ? 'space-y-4' : 'space-y-6'}`}>
        {/* Amount */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Amount *
          </label>
          <Controller
            name="amount"
            control={control}
            render={({ field }) => (
              <input
                type="text"
                inputMode="decimal"
                {...field}
                placeholder="0.00"
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.amount ? 'border-error-red' : ''}`}
              />
            )}
          />
          {errors.amount && (
            <p className="text-sm text-error-red">{errors.amount.message}</p>
          )}
        </div>

        {/* Account Selection */}
        {(activeTab === 'expense' || activeTab === 'income') && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Account *
            </label>
            <Controller
              name="account_id"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.account_id ? 'border-error-red' : ''}`}
                >
                  <option value="">Select an account</option>
                  {getFilteredAccounts('source').map((account) => (
                    <option key={account.id} value={account.id}>
                      {account.name} ({account.account_type}) - ${account.current_balance?.toFixed(2)}
                    </option>
                  ))}
                </select>
              )}
            />
            {errors.account_id && (
              <p className="text-sm text-error-red">{errors.account_id.message}</p>
            )}
          </div>
        )}

        {/* Transfer-specific fields */}
        {activeTab === 'transfer' && (
          <>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-text-primary">
                From Account *
              </label>
              <Controller
                name="account_id"
                control={control}
                render={({ field }) => (
                  <select
                    {...field}
                    className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.account_id ? 'border-error-red' : ''}`}
                  >
                    <option value="">Select source account</option>
                    {getFilteredAccounts('source').map((account) => (
                      <option key={account.id} value={account.id}>
                        {account.name} ({account.account_type}) - ${account.current_balance?.toFixed(2)}
                      </option>
                    ))}
                  </select>
                )}
              />
              {errors.account_id && (
                <p className="text-sm text-error-red">{errors.account_id.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-text-primary">
                To Account *
              </label>
              <Controller
                name="to_account_id"
                control={control}
                render={({ field }) => (
                  <select
                    {...field}
                    className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.to_account_id ? 'border-error-red' : ''}`}
                  >
                    <option value="">Select destination account</option>
                    {getFilteredAccounts('destination').map((account) => (
                      <option key={account.id} value={account.id}>
                        {account.name} ({account.account_type}) - ${account.current_balance?.toFixed(2)}
                      </option>
                    ))}
                  </select>
                )}
              />
              {errors.to_account_id && (
                <p className="text-sm text-error-red">{errors.to_account_id.message}</p>
              )}
            </div>

            {/* Transfer Fees */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-text-primary">
                Transfer Fees
              </label>
              <Controller
                name="fees"
                control={control}
                render={({ field }) => (
                  <input
                    type="text"
                    inputMode="decimal"
                    {...field}
                    placeholder="0.00"
                    className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.fees ? 'border-error-red' : ''}`}
                  />
                )}
              />
              {errors.fees && (
                <p className="text-sm text-error-red">{errors.fees.message}</p>
              )}
            </div>
          </>
        )}

        {/* Category Selection (for expense and income) */}
        {(activeTab === 'expense' || activeTab === 'income') && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Category *
            </label>
            <Controller
              name="category_id"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.category_id ? 'border-error-red' : ''}`}
                >
                  <option value="">Select a category</option>
                  {getFilteredCategories().map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              )}
            />
            {errors.category_id && (
              <p className="text-sm text-error-red">{errors.category_id.message}</p>
            )}
          </div>
        )}

        {/* Description */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Description
          </label>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <textarea
                {...field}
                rows={3}
                placeholder="Add a note about this transaction..."
                className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary resize-none"
              />
            )}
          />
        </div>

        {/* Transaction Date */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Date *
          </label>
          <Controller
            name="transaction_date"
            control={control}
            render={({ field }) => (
              <input
                type="date"
                {...field}
                value={field.value instanceof Date ? field.value.toISOString().split('T')[0] : field.value}
                onChange={(e) => field.onChange(new Date(e.target.value))}
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.transaction_date ? 'border-error-red' : ''}`}
              />
            )}
          />
          {errors.transaction_date && (
            <p className="text-sm text-error-red">{errors.transaction_date.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            disabled={isSubmitting || loading}
            className="w-full bg-primary-blue hover:bg-primary-blue/90 disabled:bg-primary-blue/50 text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            {isSubmitting || loading ? 'Saving...' : initialData ? 'Update Transaction' : 'Add Transaction'}
          </button>
        </div>
      </form>
    </div>
  )
}
