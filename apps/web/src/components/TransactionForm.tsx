import React, { useState, useEffect } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { 
  transactionFormInputSchema, 
  transactionFormSchema, 
  type TransactionFormInputData,
  type ICategory,
  type IAccount,
  AccountService,
  CategoryService
} from '@shared/index'
import toast from 'react-hot-toast'

interface TransactionFormProps {
  onSubmit: (data: any) => Promise<void>
  loading?: boolean
  className?: string
  initialData?: any
  compact?: boolean
}

export const TransactionForm: React.FC<TransactionFormProps> = ({
  onSubmit,
  loading = false,
  className = "",
  initialData,
  compact = false
}) => {
  const [categories, setCategories] = useState<ICategory[]>([])
  const [accounts, setAccounts] = useState<IAccount[]>([])
  const [loadingData, setLoadingData] = useState(true)

  const form = useForm<TransactionFormInputData>({
    resolver: zodResolver(transactionFormInputSchema),
    defaultValues: {
      amount: initialData?.amount?.toString() || '',
      category_id: initialData?.category_id || '',
      account_id: initialData?.account_id || '',
      to_account_id: initialData?.to_account_id || '',
      description: initialData?.description || '',
      transaction_date: initialData?.transaction_date || new Date(),
      transaction_type: initialData?.transaction_type || 'expense',
      fees: initialData?.fees?.toString() || '',
      investment_symbol: initialData?.investment_symbol || '',
      investment_quantity: initialData?.investment_quantity?.toString() || '',
      investment_price: initialData?.investment_price?.toString() || '',
      funding_account_id: '',
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    watch
  } = form

  const transactionType = watch('transaction_type')
  const accountId = watch('account_id')

  // Load categories and accounts
  useEffect(() => {
    const loadData = async () => {
      try {
        const [categoriesData, accountsData] = await Promise.all([
          CategoryService.getCategories({ is_active: true }),
          AccountService.getAccounts({ is_active: true })
        ])
        setCategories(categoriesData)
        setAccounts(accountsData)
      } catch (error) {
        console.error('Failed to load data:', error)
        toast.error('Failed to load form data')
      } finally {
        setLoadingData(false)
      }
    }
    loadData()
  }, [])

  // Filter categories based on transaction type
  const filteredCategories = categories.filter(cat => {
    if (transactionType === 'income') return cat.type === 'income'
    if (transactionType === 'expense') return cat.type === 'expense'
    if (transactionType === 'transfer') return cat.is_system && (cat.name === 'Transfer In' || cat.name === 'Transfer Out')
    if (transactionType === 'investment_buy') return cat.is_system && cat.name === 'Investment Purchase'
    if (transactionType === 'investment_sell') return cat.is_system && cat.name === 'Investment Sale'
    if (transactionType === 'dividend') return cat.is_system && cat.name === 'Dividend Income'
    return false
  })

  // Filter accounts based on transaction type
  const getFilteredAccounts = (purpose: 'source' | 'destination' | 'investment' | 'funding') => {
    switch (purpose) {
      case 'investment':
        return accounts.filter(acc => acc.account_type === 'investment')
      case 'funding':
        return accounts.filter(acc => acc.account_type !== 'investment')
      case 'destination':
        return accounts.filter(acc => acc.id !== accountId)
      default:
        return accounts
    }
  }

  const handleFormSubmit = async (data: TransactionFormInputData) => {
    try {
      const validatedData = transactionFormSchema.parse(data)
      await onSubmit(validatedData)
      if (!initialData) {
        reset()
      }
    } catch (error) {
      console.error('Error submitting transaction:', error)
      toast.error('Failed to submit transaction')
    }
  }

  if (loadingData) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className={`${compact ? 'space-y-4' : 'space-y-6'} ${className}`}>
      {/* Transaction Type Selection */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-text-primary">
          Transaction Type *
        </label>
        <Controller
          name="transaction_type"
          control={control}
          render={({ field }) => (
            <select
              {...field}
              className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary"
            >
              <option value="expense">Expense</option>
              <option value="income">Income</option>
              <option value="transfer">Transfer</option>
              <option value="investment_buy">Investment Purchase</option>
              <option value="investment_sell">Investment Sale</option>
              <option value="dividend">Dividend</option>
            </select>
          )}
        />
      </div>

      {/* Amount */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-text-primary">
          Amount *
        </label>
        <Controller
          name="amount"
          control={control}
          render={({ field }) => (
            <input
              type="text"
              inputMode="decimal"
              {...field}
              placeholder="0.00"
              className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.amount ? 'border-error-red' : ''}`}
            />
          )}
        />
        {errors.amount && (
          <p className="text-sm text-error-red">{errors.amount.message}</p>
        )}
      </div>

      {/* Account Selection */}
      {(transactionType === 'income' || transactionType === 'expense') && (
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Account *
          </label>
          <Controller
            name="account_id"
            control={control}
            render={({ field }) => (
              <select
                {...field}
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.account_id ? 'border-error-red' : ''}`}
              >
                <option value="">Select an account</option>
                {getFilteredAccounts('source').map((account) => (
                  <option key={account.id} value={account.id}>
                    {account.name} ({account.account_type})
                  </option>
                ))}
              </select>
            )}
          />
          {errors.account_id && (
            <p className="text-sm text-error-red">{errors.account_id.message}</p>
          )}
        </div>
      )}

      {/* Transfer-specific fields */}
      {transactionType === 'transfer' && (
        <>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              From Account *
            </label>
            <Controller
              name="account_id"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.account_id ? 'border-error-red' : ''}`}
                >
                  <option value="">Select source account</option>
                  {getFilteredAccounts('source').map((account) => (
                    <option key={account.id} value={account.id}>
                      {account.name} ({account.account_type}) - ${account.current_balance?.toFixed(2)}
                    </option>
                  ))}
                </select>
              )}
            />
            {errors.account_id && (
              <p className="text-sm text-error-red">{errors.account_id.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              To Account *
            </label>
            <Controller
              name="to_account_id"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.to_account_id ? 'border-error-red' : ''}`}
                >
                  <option value="">Select destination account</option>
                  {getFilteredAccounts('destination').map((account) => (
                    <option key={account.id} value={account.id}>
                      {account.name} ({account.account_type}) - ${account.current_balance?.toFixed(2)}
                    </option>
                  ))}
                </select>
              )}
            />
            {errors.to_account_id && (
              <p className="text-sm text-error-red">{errors.to_account_id.message}</p>
            )}
          </div>
        </>
      )}

      {/* Investment-specific fields */}
      {(transactionType === 'investment_buy' || transactionType === 'investment_sell' || transactionType === 'dividend') && (
        <>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Investment Account *
            </label>
            <Controller
              name="account_id"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.account_id ? 'border-error-red' : ''}`}
                >
                  <option value="">Select investment account</option>
                  {getFilteredAccounts('investment').map((account) => (
                    <option key={account.id} value={account.id}>
                      {account.name} - ${account.current_balance?.toFixed(2)}
                    </option>
                  ))}
                </select>
              )}
            />
            {errors.account_id && (
              <p className="text-sm text-error-red">{errors.account_id.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Symbol *
            </label>
            <Controller
              name="investment_symbol"
              control={control}
              render={({ field }) => (
                <input
                  type="text"
                  {...field}
                  placeholder="e.g., AAPL, MSFT"
                  className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.investment_symbol ? 'border-error-red' : ''}`}
                />
              )}
            />
            {errors.investment_symbol && (
              <p className="text-sm text-error-red">{errors.investment_symbol.message}</p>
            )}
          </div>

          {(transactionType === 'investment_buy' || transactionType === 'investment_sell') && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-text-primary">
                    Quantity *
                  </label>
                  <Controller
                    name="investment_quantity"
                    control={control}
                    render={({ field }) => (
                      <input
                        type="text"
                        inputMode="decimal"
                        {...field}
                        placeholder="0"
                        className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.investment_quantity ? 'border-error-red' : ''}`}
                      />
                    )}
                  />
                  {errors.investment_quantity && (
                    <p className="text-sm text-error-red">{errors.investment_quantity.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-text-primary">
                    Price per Share *
                  </label>
                  <Controller
                    name="investment_price"
                    control={control}
                    render={({ field }) => (
                      <input
                        type="text"
                        inputMode="decimal"
                        {...field}
                        placeholder="0.00"
                        className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.investment_price ? 'border-error-red' : ''}`}
                      />
                    )}
                  />
                  {errors.investment_price && (
                    <p className="text-sm text-error-red">{errors.investment_price.message}</p>
                  )}
                </div>
              </div>

              {transactionType === 'investment_buy' && (
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-text-primary">
                    Funding Account *
                  </label>
                  <Controller
                    name="funding_account_id"
                    control={control}
                    render={({ field }) => (
                      <select
                        {...field}
                        className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.funding_account_id ? 'border-error-red' : ''}`}
                      >
                        <option value="">Select funding account</option>
                        {getFilteredAccounts('funding').map((account) => (
                          <option key={account.id} value={account.id}>
                            {account.name} ({account.account_type}) - ${account.current_balance?.toFixed(2)}
                          </option>
                        ))}
                      </select>
                    )}
                  />
                  {errors.funding_account_id && (
                    <p className="text-sm text-error-red">{errors.funding_account_id.message}</p>
                  )}
                </div>
              )}
            </>
          )}
        </>
      )}

      {/* Category (for income/expense only) */}
      {(transactionType === 'income' || transactionType === 'expense') && (
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Category *
          </label>
          <Controller
            name="category_id"
            control={control}
            render={({ field }) => (
              <select
                {...field}
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.category_id ? 'border-error-red' : ''}`}
              >
                <option value="">Select a category</option>
                {filteredCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.icon} {category.name}
                  </option>
                ))}
              </select>
            )}
          />
          {errors.category_id && (
            <p className="text-sm text-error-red">{errors.category_id.message}</p>
          )}
        </div>
      )}

      {/* Date */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-text-primary">
          Date *
        </label>
        <Controller
          name="transaction_date"
          control={control}
          render={({ field }) => (
            <input
              type="date"
              value={field.value.toISOString().split('T')[0]}
              onChange={(e) => field.onChange(new Date(e.target.value))}
              className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.transaction_date ? 'border-error-red' : ''}`}
            />
          )}
        />
        {errors.transaction_date && (
          <p className="text-sm text-error-red">{errors.transaction_date.message}</p>
        )}
      </div>

      {/* Fees (for transfers and investments) */}
      {(transactionType === 'transfer' || transactionType === 'investment_buy' || transactionType === 'investment_sell') && (
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Fees
          </label>
          <Controller
            name="fees"
            control={control}
            render={({ field }) => (
              <input
                type="text"
                inputMode="decimal"
                {...field}
                placeholder="0.00"
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.fees ? 'border-error-red' : ''}`}
              />
            )}
          />
          {errors.fees && (
            <p className="text-sm text-error-red">{errors.fees.message}</p>
          )}
        </div>
      )}

      {/* Description */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-text-primary">
          Description
        </label>
        <Controller
          name="description"
          control={control}
          render={({ field }) => (
            <textarea
              {...field}
              rows={compact ? 2 : 3}
              placeholder="Optional notes about this transaction..."
              className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 resize-none text-text-primary"
            />
          )}
        />
        {errors.description && (
          <p className="text-sm text-error-red">{errors.description.message}</p>
        )}
      </div>

      {/* Submit Button */}
      <div className="pt-4">
        <button
          type="submit"
          disabled={isSubmitting || loading}
          className="w-full bg-gradient-to-r from-primary-blue to-primary-purple text-white px-6 py-4 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-primary-blue/20"
        >
          {isSubmitting || loading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {initialData ? 'Updating' : 'Adding'} transaction...
            </span>
          ) : (
            `${initialData ? 'Update' : 'Add'} Transaction`
          )}
        </button>
      </div>
    </form>
  )
}
