import React, { useState } from 'react'
import { useProfile } from '../contexts/ProfileContext'

export interface DataExportProps {
  className?: string
}

export function DataExport({ className = '' }: DataExportProps) {
  const { exportData } = useProfile()
  const [exporting, setExporting] = useState(false)
  const [error, setError] = useState('')

  const downloadJSON = (data: any, filename: string) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json',
    })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const downloadCSV = (data: any[], filename: string) => {
    if (data.length === 0) return

    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        headers.map(header => {
          const value = row[header]
          // Escape commas and quotes in CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`
          }
          return value || ''
        }).join(',')
      )
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const handleExportJSON = async () => {
    setExporting(true)
    setError('')

    try {
      const result = await exportData()
      if (result.error) {
        setError(result.error)
        return
      }

      const timestamp = new Date().toISOString().split('T')[0]
      downloadJSON(result.data, `portfolio-tracker-data-${timestamp}.json`)
    } catch (err) {
      setError('Failed to export data')
      console.error('Export error:', err)
    } finally {
      setExporting(false)
    }
  }

  const handleExportTransactionsCSV = async () => {
    setExporting(true)
    setError('')

    try {
      const result = await exportData()
      if (result.error) {
        setError(result.error)
        return
      }

      const transactions = result.data.transactions
      if (transactions.length === 0) {
        setError('No transactions to export')
        return
      }

      const timestamp = new Date().toISOString().split('T')[0]
      downloadCSV(transactions, `transactions-${timestamp}.csv`)
    } catch (err) {
      setError('Failed to export transactions')
      console.error('Export error:', err)
    } finally {
      setExporting(false)
    }
  }

  const handleExportBudgetsCSV = async () => {
    setExporting(true)
    setError('')

    try {
      const result = await exportData()
      if (result.error) {
        setError(result.error)
        return
      }

      const budgets = result.data.budgets
      if (budgets.length === 0) {
        setError('No budgets to export')
        return
      }

      const timestamp = new Date().toISOString().split('T')[0]
      downloadCSV(budgets, `budgets-${timestamp}.csv`)
    } catch (err) {
      setError('Failed to export budgets')
      console.error('Export error:', err)
    } finally {
      setExporting(false)
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h3 className="text-2xl font-bold text-text-primary mb-2">Data Export</h3>
        <p className="text-text-secondary">
          Export your financial data for backup or analysis purposes.
        </p>
      </div>

      {error && (
        <div className="bg-error-red/5 border border-error-red/20 rounded-xl p-4">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-error-red/10 rounded-full flex items-center justify-center flex-shrink-0">
              <svg className="w-4 h-4 text-error-red" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <p className="text-error-red font-medium">{error}</p>
          </div>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Complete Data Export (JSON) */}
        <div className="bg-surface-elevated border border-border-light rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow flex flex-col">
          <div className="flex-1 mb-4">
            <h4 className="text-lg font-semibold text-text-primary mb-2">Complete Data</h4>
            <p className="text-text-secondary text-sm">
              Export all your data including profile, transactions, budgets, and categories in JSON format.
            </p>
          </div>
          <button
            onClick={handleExportJSON}
            disabled={exporting}
            className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-md flex items-center justify-center gap-2"
          >
            {exporting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Exporting...
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export JSON
              </>
            )}
          </button>
        </div>

        {/* Transactions CSV */}
        <div className="bg-surface-elevated border border-border-light rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow flex flex-col">
          <div className="flex-1 mb-4">
            <h4 className="text-lg font-semibold text-text-primary mb-2">Transactions</h4>
            <p className="text-text-secondary text-sm">
              Export all your transactions in CSV format for spreadsheet analysis.
            </p>
          </div>
          <button
            onClick={handleExportTransactionsCSV}
            disabled={exporting}
            className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-md flex items-center justify-center gap-2"
          >
            {exporting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Exporting...
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export CSV
              </>
            )}
          </button>
        </div>

        {/* Budgets CSV */}
        <div className="bg-surface-elevated border border-border-light rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow flex flex-col">
          <div className="flex-1 mb-4">
            <h4 className="text-lg font-semibold text-text-primary mb-2">Budgets</h4>
            <p className="text-text-secondary text-sm">
              Export all your budgets in CSV format for analysis.
            </p>
          </div>
          <button
            onClick={handleExportBudgetsCSV}
            disabled={exporting}
            className="w-full bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-3 rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-md flex items-center justify-center gap-2"
          >
            {exporting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Exporting...
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
                Export CSV
              </>
            )}
          </button>
        </div>
      </div>

      <div className="bg-blue-50/50 dark:bg-blue-900/20 border border-blue-200/50 dark:border-blue-700/50 rounded-xl p-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-6 w-6 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-1">
              Data Privacy Notice
            </h3>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Your exported data contains sensitive financial information. Please store it securely 
              and avoid sharing it with unauthorized parties.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}