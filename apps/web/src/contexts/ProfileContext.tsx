import React, { createContext, useContext, useEffect, useState } from 'react'
import { IUserProfile, supabase, useCurrencyStore } from '@repo/shared'
import { useAuth } from './AuthContext'

export interface IProfileContext {
  profile: IUserProfile | null
  loading: boolean
  updateProfile: (updates: Partial<IUserProfile>) => Promise<{ error?: string }>
  uploadAvatar: (file: File | Blob) => Promise<{ error?: string; url?: string }>
  exportData: () => Promise<{ error?: string; data?: any }>
}

const ProfileContext = createContext<IProfileContext | undefined>(undefined)

export interface ProfileProviderProps {
  children: React.ReactNode
}

export function ProfileProvider({ children }: ProfileProviderProps) {
  const { user } = useAuth()
  const [profile, setProfile] = useState<IUserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const { setCurrency } = useCurrencyStore()

  useEffect(() => {
    if (user) {
      fetchProfile()
    } else {
      setProfile(null)
      setLoading(false)
    }
  }, [user])

  const fetchProfile = async () => {
    if (!user) return
    
    try {
      setLoading(true)
      
      // First, try to get existing profile
      const { data: existingProfile, error: fetchError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('Error fetching profile:', fetchError)
        return
      }

      if (existingProfile) {
        setProfile({
          ...existingProfile,
          notification_preferences: (existingProfile.notification_preferences as any) || {},
        } as IUserProfile)
        // Sync currency preference with global store
        if (existingProfile.currency_preference) {
          setCurrency(existingProfile.currency_preference)
        }
      } else {
        // Create initial profile if it doesn't exist
        const initialProfile = {
          user_id: user.id,
          display_name: user.user_metadata?.name || user.email?.split('@')[0] || '',
          currency_preference: 'USD',
          notification_preferences: {
            email_notifications: true,
            push_notifications: true,
            budget_alerts: true,
            weekly_summary: true,
          },
        }

        const { data: newProfile, error: insertError } = await supabase
          .from('user_profiles')
          .insert(initialProfile)
          .select()
          .single()

        if (insertError) {
          console.error('Error creating profile:', insertError)
          return
        }

        setProfile({
          ...newProfile,
          notification_preferences: (newProfile.notification_preferences as any) || {},
        } as IUserProfile)
        // Sync currency preference with global store
        setCurrency(newProfile.currency_preference || 'USD')
      }
    } catch (error) {
      console.error('Error in fetchProfile:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateProfile = async (updates: Partial<IUserProfile>) => {
    if (!user || !profile) {
      return { error: 'No user or profile found' }
    }

    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update(updates)
        .eq('user_id', user.id)
        .select()
        .single()

      if (error) {
        console.error('Error updating profile:', error)
        return { error: error.message }
      }

      setProfile({
        ...data,
        notification_preferences: (data.notification_preferences as any) || {},
      } as IUserProfile)

      // Sync currency preference with global store
      if (data.currency_preference) {
        setCurrency(data.currency_preference)
      }

      return {}
    } catch (error) {
      console.error('Error in updateProfile:', error)
      return { error: 'Failed to update profile' }
    }
  }

  const uploadAvatar = async (file: File | Blob) => {
    if (!user) {
      return { error: 'No user found' }
    }

    try {
      const fileExt = file instanceof File ? file.name.split('.').pop() : 'jpg'
      const fileName = `avatar.${fileExt}`
      const filePath = `${user.id}/${fileName}`

      // Upload file to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file, { upsert: true })

      if (uploadError) {
        console.error('Error uploading avatar:', uploadError)
        return { error: uploadError.message }
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath)

      const avatarUrl = urlData.publicUrl

      // Update profile with new avatar URL
      const updateResult = await updateProfile({ avatar_url: avatarUrl })
      if (updateResult.error) {
        return { error: updateResult.error }
      }

      return { url: avatarUrl }
    } catch (error) {
      console.error('Error in uploadAvatar:', error)
      return { error: 'Failed to upload avatar' }
    }
  }

  const exportData = async () => {
    if (!user) {
      return { error: 'No user found' }
    }

    try {
      // Fetch all user data
      const [categoriesResult, transactionsResult, budgetsResult] = await Promise.all([
        supabase.from('categories').select('*').eq('user_id', user.id),
        supabase.from('transactions').select('*').eq('user_id', user.id),
        supabase.from('budgets').select('*').eq('user_id', user.id),
      ])

      const exportData = {
        profile,
        categories: categoriesResult.data || [],
        transactions: transactionsResult.data || [],
        budgets: budgetsResult.data || [],
        exported_at: new Date().toISOString(),
      }

      return { data: exportData }
    } catch (error) {
      console.error('Error in exportData:', error)
      return { error: 'Failed to export data' }
    }
  }

  const value: IProfileContext = {
    profile,
    loading,
    updateProfile,
    uploadAvatar,
    exportData,
  }

  return <ProfileContext.Provider value={value}>{children}</ProfileContext.Provider>
}

export function useProfile() {
  const context = useContext(ProfileContext)
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider')
  }
  return context
}