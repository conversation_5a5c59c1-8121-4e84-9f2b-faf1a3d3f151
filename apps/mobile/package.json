{"name": "@apps/mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "dev": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "tsc --noEmit", "lint": "eslint \"src/**/*.{ts,tsx}\"", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/shared": "*", "expo": "~53.0.11", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3", "eslint": "^8.57.0"}, "private": true}