import React from 'react'
import { useCurrencyStore } from '@repo/shared'

export interface ProgressBarProps {
  value: number
  max: number
  label?: string
  showPercentage?: boolean
  variant?: 'default' | 'success' | 'warning' | 'error'
  size?: 'small' | 'medium' | 'large'
  className?: string
}

export function ProgressBar({
  value,
  max,
  label,
  showPercentage = true,
  variant = 'default',
  size = 'medium',
  className = ''
}: ProgressBarProps) {
  const percentage = Math.min((value / max) * 100, 100)
  const { formatCurrency } = useCurrencyStore()
  
  const getVariantClasses = () => {
    switch (variant) {
      case 'success':
        return 'bg-gradient-to-r from-green-500 to-green-600'
      case 'warning':
        return 'bg-gradient-to-r from-orange-500 to-orange-600'
      case 'error':
        return 'bg-gradient-to-r from-error-red to-error-red'
      default:
        return 'bg-gradient-to-r from-blue-500 to-purple-600'
    }
  }

  const getHeightClasses = () => {
    switch (size) {
      case 'small':
        return 'h-1'
      case 'large':
        return 'h-4'
      default:
        return 'h-2'
    }
  }


  return (
    <div className={`space-y-2 ${className}`}>
      {(label || showPercentage) && (
        <div className="flex justify-between text-sm">
          {label && (
            <span className="text-text-secondary">{label}</span>
          )}
          {showPercentage && (
            <span className="font-medium text-text-primary">
              {formatCurrency(value)} / {formatCurrency(max)}
            </span>
          )}
        </div>
      )}
      
      <div className={`w-full bg-border-light rounded-full overflow-hidden ${getHeightClasses()}`}>
        <div 
          className={`${getHeightClasses()} ${getVariantClasses()} rounded-full transition-all duration-300 ease-out`}
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      {showPercentage && size !== 'small' && (
        <div className="text-xs text-text-tertiary text-right">
          {percentage.toFixed(1)}% used
        </div>
      )}
    </div>
  )
}

export default ProgressBar