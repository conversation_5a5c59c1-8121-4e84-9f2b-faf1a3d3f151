import React, { useState, useEffect } from 'react'
import { RecurringTransactionService, type DueRecurringTransaction } from '@repo/shared'
import { useAuth } from '../contexts/AuthContext'

export interface DueTransactionsNotificationProps {
  onTransactionCreated?: () => void
  className?: string
}

export function DueTransactionsNotification({ 
  onTransactionCreated, 
  className = '' 
}: DueTransactionsNotificationProps) {
  const { user } = useAuth()
  const [dueTransactions, setDueTransactions] = useState<DueRecurringTransaction[]>([])
  const [loading, setLoading] = useState(false)
  const [creating, setCreating] = useState<Set<string>>(new Set())
  const [error, setError] = useState('')
  const [expanded, setExpanded] = useState(false)

  useEffect(() => {
    if (user) {
      fetchDueTransactions()
      // Process auto-create transactions on component mount
      processAutoCreateTransactions()
    }
  }, [user])

  const fetchDueTransactions = async () => {
    try {
      setLoading(true)
      const due = await RecurringTransactionService.getDueRecurringTransactions()
      setDueTransactions(due)
    } catch (err) {
      console.error('Error fetching due transactions:', err)
      setError('Failed to load due transactions')
    } finally {
      setLoading(false)
    }
  }

  const processAutoCreateTransactions = async () => {
    try {
      const result = await RecurringTransactionService.processAutoCreateRecurringTransactions()
      if (result.created > 0) {
        // Refresh the due transactions list
        await fetchDueTransactions()
        onTransactionCreated?.()
      }
    } catch (err) {
      console.error('Error processing auto-create transactions:', err)
    }
  }

  const createTransaction = async (template: DueRecurringTransaction['template']) => {
    try {
      setCreating(prev => new Set(prev).add(template.id))
      setError('')
      
      await RecurringTransactionService.createTransactionFromTemplate(template)
      
      // Remove from due list and refresh
      setDueTransactions(prev => prev.filter(dt => dt.template.id !== template.id))
      onTransactionCreated?.()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create transaction')
    } finally {
      setCreating(prev => {
        const newSet = new Set(prev)
        newSet.delete(template.id)
        return newSet
      })
    }
  }

  const createAllTransactions = async () => {
    try {
      setError('')
      const templates = dueTransactions.map(dt => dt.template)
      
      const result = await RecurringTransactionService.createTransactionsFromTemplates(templates)
      
      if (result.errors.length > 0) {
        setError(`Created ${result.success.length} transactions, ${result.errors.length} failed`)
      }
      
      // Refresh the due transactions list
      await fetchDueTransactions()
      onTransactionCreated?.()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create transactions')
    }
  }

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    )
  }

  if (dueTransactions.length === 0) {
    return null
  }

  return (
    <div className={`bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 ${className}`}>
      {error && (
        <div className="mb-3 text-red-600 dark:text-red-400 text-sm">
          {error}
        </div>
      )}

      <div className="flex items-start justify-between">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
              {dueTransactions.length} Due Recurring Transaction{dueTransactions.length !== 1 ? 's' : ''}
            </h3>
            <div className="mt-1 text-sm text-blue-700 dark:text-blue-300">
              {dueTransactions.some(dt => dt.daysOverdue > 0) && (
                <span>
                  {dueTransactions.filter(dt => dt.daysOverdue > 0).length} overdue • 
                </span>
              )}
              <button
                onClick={() => setExpanded(!expanded)}
                className="underline hover:no-underline"
              >
                {expanded ? 'Hide details' : 'View details'}
              </button>
            </div>
          </div>
        </div>
        <div className="flex-shrink-0 flex space-x-2">
          <button
            onClick={createAllTransactions}
            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md"
          >
            Create All
          </button>
        </div>
      </div>

      {expanded && (
        <div className="mt-4 space-y-2">
          {dueTransactions.map((dueTransaction) => (
            <div
              key={dueTransaction.template.id}
              className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
            >
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {dueTransaction.template.name}
                  </div>
                  <div className={`text-lg font-bold ${
                    dueTransaction.template.transaction_type === 'income' 
                      ? 'text-green-600 dark:text-green-400' 
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {dueTransaction.template.transaction_type === 'income' ? '+' : '-'}
                    ${dueTransaction.template.amount.toFixed(2)}
                  </div>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Due: {dueTransaction.nextDueDate.toLocaleDateString()}
                  {dueTransaction.daysOverdue > 0 && (
                    <span className="ml-2 text-red-600 dark:text-red-400">
                      ({dueTransaction.daysOverdue} day{dueTransaction.daysOverdue !== 1 ? 's' : ''} overdue)
                    </span>
                  )}
                  {dueTransaction.category && (
                    <span className="ml-2">
                      • {dueTransaction.category.name}
                    </span>
                  )}
                </div>
                {dueTransaction.template.description && (
                  <div className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {dueTransaction.template.description}
                  </div>
                )}
              </div>
              <div className="ml-4">
                <button
                  onClick={() => createTransaction(dueTransaction.template)}
                  disabled={creating.has(dueTransaction.template.id)}
                  className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {creating.has(dueTransaction.template.id) ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-3 w-3 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Creating...
                    </div>
                  ) : (
                    'Create'
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}