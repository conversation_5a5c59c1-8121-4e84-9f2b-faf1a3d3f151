import React, { useState } from 'react'
import { BudgetForm } from './BudgetForm'
import { BudgetList } from './BudgetList'
import type { IBudget } from '@repo/shared'

export const BudgetDashboard: React.FC = () => {
  const [showForm, setShowForm] = useState(false)
  const [editingBudget, setEditingBudget] = useState<IBudget | null>(null)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleCreateNew = () => {
    setEditingBudget(null)
    setShowForm(true)
  }

  const handleEdit = (budget: IBudget) => {
    setEditingBudget(budget)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingBudget(null)
    setRefreshTrigger(prev => prev + 1)
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingBudget(null)
  }

  const handleDelete = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Budget Management</h1>
        {!showForm && (
          <button
            onClick={handleCreateNew}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Create New Budget
          </button>
        )}
      </div>

      {showForm && (
        <div className="mb-8">
          <BudgetForm
            budget={editingBudget || undefined}
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
          />
        </div>
      )}

      {!showForm && (
        <BudgetList
          onEdit={handleEdit}
          onDelete={handleDelete}
          refreshTrigger={refreshTrigger}
        />
      )}
    </div>
  )
}