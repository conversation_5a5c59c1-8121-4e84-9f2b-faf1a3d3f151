import React, { useState, useEffect } from 'react'
import { View, Text, Switch, Alert, Platform } from 'react-native'
import { useAuth } from '../contexts/AuthContext'

interface BiometricToggleProps {
  onToggle?: (enabled: boolean) => void
  containerStyle?: any
  textStyle?: any
  switchStyle?: any
}

export function BiometricToggle({ 
  onToggle, 
  containerStyle, 
  textStyle, 
  switchStyle 
}: BiometricToggleProps) {
  const { isBiometricAvailable, isBiometricEnabled, setBiometricEnabled } = useAuth()
  const [isAvailable, setIsAvailable] = useState(false)
  const [isEnabled, setIsEnabled] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    checkBiometricStatus()
  }, [])

  const checkBiometricStatus = async () => {
    try {
      setIsLoading(true)
      const available = isBiometricAvailable ? await isBiometricAvailable() : false
      const enabled = isBiometricEnabled ? await isBiometricEnabled() : false
      
      setIsAvailable(available)
      setIsEnabled(enabled)
    } catch (error) {
      console.error('Error checking biometric status:', error)
      setIsAvailable(false)
      setIsEnabled(false)
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggle = async (value: boolean) => {
    if (!isAvailable) {
      Alert.alert(
        'Biometric Authentication Unavailable',
        'Your device does not support biometric authentication or you have not set up biometrics in your device settings.',
        [{ text: 'OK' }]
      )
      return
    }

    try {
      if (value) {
        // Enabling biometric login
        Alert.alert(
          'Enable Biometric Login',
          'This will allow you to sign in using your fingerprint, face, or other biometric authentication methods. Your login credentials will be securely stored on your device.',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Enable',
              onPress: async () => {
                try {
                  if (setBiometricEnabled) {
                    await setBiometricEnabled(true)
                    setIsEnabled(true)
                    onToggle?.(true)
                  }
                } catch (error) {
                  console.error('Error enabling biometric login:', error)
                  Alert.alert('Error', 'Failed to enable biometric login. Please try again.')
                }
              }
            }
          ]
        )
      } else {
        // Disabling biometric login
        Alert.alert(
          'Disable Biometric Login',
          'This will remove your stored credentials and disable biometric authentication for this app.',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Disable',
              style: 'destructive',
              onPress: async () => {
                try {
                  if (setBiometricEnabled) {
                    await setBiometricEnabled(false)
                    setIsEnabled(false)
                    onToggle?.(false)
                  }
                } catch (error) {
                  console.error('Error disabling biometric login:', error)
                  Alert.alert('Error', 'Failed to disable biometric login. Please try again.')
                }
              }
            }
          ]
        )
      }
    } catch (error) {
      console.error('Error toggling biometric setting:', error)
      Alert.alert('Error', 'An unexpected error occurred. Please try again.')
    }
  }

  // Don't render on web or if not available
  if (Platform.OS === 'web' || (!isAvailable && !isLoading)) {
    return null
  }

  return (
    <View style={[
      {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 12,
        paddingHorizontal: 16,
      },
      containerStyle
    ]}>
      <View style={{ flex: 1 }}>
        <Text style={[
          {
            fontSize: 16,
            fontWeight: '500',
            color: '#000',
          },
          textStyle
        ]}>
          Biometric Login
        </Text>
        <Text style={[
          {
            fontSize: 14,
            color: '#666',
            marginTop: 2,
          },
          textStyle && { color: textStyle.color ? `${textStyle.color}80` : '#666' }
        ]}>
          {isAvailable 
            ? 'Use fingerprint or face recognition to sign in'
            : 'Not available on this device'
          }
        </Text>
      </View>
      
      <Switch
        value={isEnabled}
        onValueChange={handleToggle}
        disabled={!isAvailable || isLoading}
        style={switchStyle}
        trackColor={{ false: '#e0e0e0', true: '#007AFF' }}
        thumbColor={isEnabled ? '#ffffff' : '#ffffff'}
      />
    </View>
  )
}