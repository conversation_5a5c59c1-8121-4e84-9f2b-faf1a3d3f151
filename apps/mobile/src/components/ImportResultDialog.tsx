import React from 'react'
import type { ImportResult } from '@repo/shared'

export interface ImportResultDialogProps {
  result: ImportResult
  onClose: () => void
  className?: string
}

export function ImportResultDialog({ result, onClose, className = '' }: ImportResultDialogProps) {
  const hasErrors = result.errors.length > 0

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`bg-background rounded-lg shadow-lg w-full max-w-lg max-h-[80vh] overflow-hidden ${className}`}>
        <div className="p-4 border-b border-border">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-bold text-text-primary">
              Import Results
            </h2>
            <button
              onClick={onClose}
              className="text-text-secondary hover:text-text-primary"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="p-4 overflow-y-auto">
          {/* Success Summary */}
          <div className="mb-4">
            <div className="flex items-center space-x-3 mb-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                hasErrors ? 'bg-warning-light' : 'bg-success-light'
              }`}>
                {hasErrors ? (
                  <span className="text-warning-dark text-lg">⚠</span>
                ) : (
                  <span className="text-success-dark text-lg">✓</span>
                )}
              </div>
              <div>
                <h3 className="text-base font-semibold text-text-primary">
                  {hasErrors ? 'Completed with Issues' : 'Import Successful'}
                </h3>
                <p className="text-text-secondary text-sm">
                  {result.success} imported
                  {hasErrors && `, ${result.errors.length} errors`}
                </p>
              </div>
            </div>
          </div>

          {/* Success Details */}
          {result.success > 0 && (
            <div className="mb-4">
              <h4 className="font-medium text-text-primary mb-2">Imported Transactions</h4>
              <div className="bg-success-light border border-success rounded-md p-3">
                <div className="space-y-2">
                  {result.transactions.slice(0, 3).map((transaction, index) => (
                    <div key={transaction.id} className="flex justify-between items-center text-sm">
                      <div>
                        <span className="font-medium text-success-dark">{transaction.investment_symbol}</span>
                        <span className="text-success-dark ml-1 text-xs">
                          {transaction.transaction_type === 'investment_buy' ? 'BUY' : 'SELL'}
                        </span>
                      </div>
                      <span className="text-success-dark text-xs">
                        ${transaction.amount.toFixed(0)}
                      </span>
                    </div>
                  ))}
                  {result.transactions.length > 3 && (
                    <div className="text-xs text-success-dark text-center pt-2 border-t border-success">
                      +{result.transactions.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Error Details */}
          {hasErrors && (
            <div className="mb-4">
              <h4 className="font-medium text-text-primary mb-2">Errors ({result.errors.length})</h4>
              <div className="bg-error-light border border-error rounded-md p-3 max-h-40 overflow-y-auto">
                <div className="space-y-2">
                  {result.errors.slice(0, 3).map((error, index) => (
                    <div key={index} className="text-sm">
                      <div className="flex justify-between items-start mb-1">
                        <span className="font-medium text-error-dark">
                          Row {error.row}
                        </span>
                      </div>
                      <p className="text-error-dark text-xs">
                        {error.error}
                      </p>
                    </div>
                  ))}
                  {result.errors.length > 3 && (
                    <div className="text-xs text-error-dark text-center pt-2 border-t border-error">
                      +{result.errors.length - 3} more errors
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Next Steps */}
          <div className="bg-info-light border border-info rounded-md p-3">
            <h4 className="font-medium text-info-dark mb-2">Next Steps</h4>
            <ul className="text-sm text-info-dark space-y-1">
              <li>• Check Investments tab</li>
              {hasErrors && (
                <li>• Fix CSV errors and re-import</li>
              )}
              <li>• Review portfolio metrics</li>
            </ul>
          </div>
        </div>

        <div className="p-4 border-t border-border bg-surface">
          <button
            onClick={onClose}
            className="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}

export default ImportResultDialog
