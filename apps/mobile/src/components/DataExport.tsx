import React, { useState } from 'react'
import { useProfile } from '../contexts/ProfileContext'

export interface DataExportProps {
  className?: string
}

export function DataExport({ className = '' }: DataExportProps) {
  const { exportData } = useProfile()
  const [exporting, setExporting] = useState(false)
  const [error, setError] = useState('')

  const downloadJSON = (data: any, filename: string) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json',
    })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const downloadCSV = (data: any[], filename: string) => {
    if (data.length === 0) return

    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        headers.map(header => {
          const value = row[header]
          // Escape commas and quotes in CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`
          }
          return value || ''
        }).join(',')
      )
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const handleExportJSON = async () => {
    setExporting(true)
    setError('')

    try {
      const result = await exportData()
      if (result.error) {
        setError(result.error)
        return
      }

      const timestamp = new Date().toISOString().split('T')[0]
      downloadJSON(result.data, `portfolio-tracker-data-${timestamp}.json`)
    } catch (err) {
      setError('Failed to export data')
      console.error('Export error:', err)
    } finally {
      setExporting(false)
    }
  }

  const handleExportTransactionsCSV = async () => {
    setExporting(true)
    setError('')

    try {
      const result = await exportData()
      if (result.error) {
        setError(result.error)
        return
      }

      const transactions = result.data.transactions
      if (transactions.length === 0) {
        setError('No transactions to export')
        return
      }

      const timestamp = new Date().toISOString().split('T')[0]
      downloadCSV(transactions, `transactions-${timestamp}.csv`)
    } catch (err) {
      setError('Failed to export transactions')
      console.error('Export error:', err)
    } finally {
      setExporting(false)
    }
  }

  const handleExportBudgetsCSV = async () => {
    setExporting(true)
    setError('')

    try {
      const result = await exportData()
      if (result.error) {
        setError(result.error)
        return
      }

      const budgets = result.data.budgets
      if (budgets.length === 0) {
        setError('No budgets to export')
        return
      }

      const timestamp = new Date().toISOString().split('T')[0]
      downloadCSV(budgets, `budgets-${timestamp}.csv`)
    } catch (err) {
      setError('Failed to export budgets')
      console.error('Export error:', err)
    } finally {
      setExporting(false)
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Data Export</h3>
        <p className="text-sm text-gray-600 mb-4">
          Export your financial data for backup or analysis purposes.
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Complete Data Export (JSON) */}
        <div className="border border-gray-200 rounded-lg p-4 space-y-3">
          <div>
            <h4 className="font-medium text-gray-900">Complete Data</h4>
            <p className="text-sm text-gray-600">
              Export all your data including profile, transactions, budgets, and categories in JSON format.
            </p>
          </div>
          <button
            onClick={handleExportJSON}
            disabled={exporting}
            className="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {exporting ? 'Exporting...' : 'Export JSON'}
          </button>
        </div>

        {/* Transactions CSV */}
        <div className="border border-gray-200 rounded-lg p-4 space-y-3">
          <div>
            <h4 className="font-medium text-gray-900">Transactions</h4>
            <p className="text-sm text-gray-600">
              Export all your transactions in CSV format for spreadsheet analysis.
            </p>
          </div>
          <button
            onClick={handleExportTransactionsCSV}
            disabled={exporting}
            className="w-full px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {exporting ? 'Exporting...' : 'Export CSV'}
          </button>
        </div>

        {/* Budgets CSV */}
        <div className="border border-gray-200 rounded-lg p-4 space-y-3">
          <div>
            <h4 className="font-medium text-gray-900">Budgets</h4>
            <p className="text-sm text-gray-600">
              Export all your budgets in CSV format for analysis.
            </p>
          </div>
          <button
            onClick={handleExportBudgetsCSV}
            disabled={exporting}
            className="w-full px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {exporting ? 'Exporting...' : 'Export CSV'}
          </button>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Data Privacy Notice
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                Your exported data contains sensitive financial information. Please store it securely 
                and avoid sharing it with unauthorized parties.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}