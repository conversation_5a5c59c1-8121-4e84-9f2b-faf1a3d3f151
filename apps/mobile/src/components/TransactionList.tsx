import * as React from 'react'
import { useState, useRef, useCallback } from 'react'
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
  StyleSheet,
  Animated,
  Dimensions,
  TextInput,
  ActivityIndicator
} from 'react-native'

// Optional gesture handler imports for swipe functionality
let PanGestureHandler: any
let State: any
let Picker: any

try {
  const gestureHandler = require('react-native-gesture-handler')
  PanGestureHandler = gestureHandler.PanGestureHandler
  State = gestureHandler.State
  
  const pickerModule = require('@react-native-picker/picker')
  Picker = pickerModule.Picker
} catch (e) {
  // Fallback if gesture handler is not available
  console.warn('React Native gesture handler or picker not available:', e)
}
// TODO: Move TransactionList logic to shared package
// import { TransactionList, type TransactionListProps, type TransactionFilters } from '@repo/shared'

// Temporary local types until shared hook is implemented
interface TransactionListProps {
  onTransactionEdit?: (transaction: ITransaction) => void
}

interface TransactionFilters {
  searchQuery?: string
  categoryId?: string
  startDate?: string
  endDate?: string
  transactionType: 'all' | 'income' | 'expense'
}

// Temporary mock hook - this should be moved to @repo/shared
function TransactionList(props: TransactionListProps) {
  // Mock implementation - replace with shared hook
  const { formatCurrency: storeCurrencyFormatter } = useCurrencyStore()

  return {
    transactions: [] as ITransaction[],
    categories: [] as any[],
    loading: false,
    loadingMore: false,
    error: null,
    totalCount: 0,
    filters: { transactionType: 'all' as const } as TransactionFilters,
    hasMoreItems: false,
    refreshing: false,
    handleRefresh: () => {},
    handleLoadMore: () => {},
    handleSearch: (query: string) => {},
    handleFilterChange: (filters: Partial<TransactionFilters>) => {},
    handleEdit: (transaction: ITransaction) => {},
    handleDelete: (id: string) => {},
    formatCurrency: storeCurrencyFormatter,
    formatDate: (date: string) => new Date(date).toLocaleDateString()
  }
}
import type { ITransaction } from '@repo/shared'
import { useCurrencyStore } from '@repo/shared'

interface TransactionListMobileProps extends TransactionListProps {
  onEditTransaction?: (transaction: ITransaction) => void
}

const { width: screenWidth } = Dimensions.get('window')
const SWIPE_THRESHOLD = screenWidth * 0.25

export function TransactionListMobile({ 
  onEditTransaction,
  ...props 
}: TransactionListMobileProps) {
  const {
    transactions,
    categories,
    loading,
    loadingMore,
    error,
    totalCount,
    filters,
    hasMoreItems,
    handleRefresh,
    handleLoadMore,
    handleSearch,
    handleFilterChange,
    handleEdit,
    handleDelete,
    formatCurrency,
    formatDate,
    refreshing
  } = TransactionList({
    ...props,
    onTransactionEdit: onEditTransaction
  })

  const [showFilters, setShowFilters] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const handleSwipeDelete = useCallback((transactionId: string) => {
    Alert.alert(
      'Delete Transaction',
      'Are you sure you want to delete this transaction?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => handleDelete(transactionId)
        }
      ]
    )
  }, [handleDelete])

  const renderTransactionItem = useCallback(({ item: transaction }: { item: ITransaction }) => {
    return (
      <SwipeableTransactionItem
        transaction={transaction}
        onEdit={() => handleEdit(transaction)}
        onDelete={() => handleSwipeDelete(transaction.id)}
        formatCurrency={formatCurrency}
        formatDate={formatDate}
      />
    )
  }, [handleEdit, handleSwipeDelete, formatCurrency, formatDate])

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <Text style={styles.title}>
          Transactions {totalCount > 0 && `(${totalCount})`}
        </Text>
        <TouchableOpacity
          onPress={() => setShowFilters(!showFilters)}
          style={[styles.filterButton, showFilters && styles.filterButtonActive]}
        >
          <Text style={[styles.filterButtonText, showFilters && styles.filterButtonTextActive]}>
            Filters
          </Text>
        </TouchableOpacity>
      </View>

      {showFilters && (
        <View style={styles.filtersContainer}>
          {/* Search Input */}
          <View style={styles.searchContainer}>
            <TextInput
              style={styles.searchInput}
              placeholder="Search transactions..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={() => handleSearch(searchQuery)}
            />
          </View>

          {/* Category Filter */}
          <View style={styles.filterRow}>
            <Text style={styles.filterLabel}>Category</Text>
            <Picker
              selectedValue={filters.categoryId}
              onValueChange={(value: string) => handleFilterChange({ categoryId: value })}
              style={styles.picker}
            >
              <Picker.Item label="All Categories" value="" />
              {categories.map((category) => (
                <Picker.Item 
                  key={category.id} 
                  label={`${category.icon} ${category.name}`} 
                  value={category.id} 
                />
              ))}
            </Picker>
          </View>

          {/* Transaction Type Filter */}
          <View style={styles.filterRow}>
            <Text style={styles.filterLabel}>Type</Text>
            <Picker
              selectedValue={filters.transactionType}
              onValueChange={(value: string) => handleFilterChange({ 
                transactionType: value as 'all' | 'income' | 'expense' 
              })}
              style={styles.picker}
            >
              <Picker.Item label="All Types" value="all" />
              <Picker.Item label="Income" value="income" />
              <Picker.Item label="Expense" value="expense" />
            </Picker>
          </View>

          {/* Clear Filters Button */}
          {(filters.searchQuery || filters.categoryId || 
            filters.startDate || filters.endDate || filters.transactionType !== 'all') && (
            <TouchableOpacity
              onPress={() => {
                setSearchQuery('')
                handleFilterChange({
                  searchQuery: '',
                  categoryId: '',
                  startDate: '',
                  endDate: '',
                  transactionType: 'all'
                })
              }}
              style={styles.clearFiltersButton}
            >
              <Text style={styles.clearFiltersText}>Clear Filters</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  )

  const renderFooter = () => {
    if (!hasMoreItems) return null

    return (
      <View style={styles.footer}>
        {loadingMore ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#3B82F6" />
            <Text style={styles.loadingText}>Loading more...</Text>
          </View>
        ) : (
          <TouchableOpacity onPress={handleLoadMore} style={styles.loadMoreButton}>
            <Text style={styles.loadMoreText}>Load More</Text>
          </TouchableOpacity>
        )}
      </View>
    )
  }

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateTitle}>No transactions found</Text>
      <Text style={styles.emptyStateSubtitle}>
        {(filters.searchQuery || filters.categoryId || 
          filters.startDate || filters.endDate || filters.transactionType !== 'all')
          ? 'Try adjusting your filters'
          : 'Add your first expense or income to get started!'
        }
      </Text>
    </View>
  )

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity onPress={() => handleRefresh()} style={styles.retryButton}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={transactions}
        renderItem={renderTransactionItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={!loading ? renderEmptyState : null}
        refreshControl={
          <RefreshControl
            refreshing={refreshing || loading}
            onRefresh={handleRefresh}
            colors={['#3B82F6']}
            tintColor="#3B82F6"
          />
        }
        onEndReached={hasMoreItems ? handleLoadMore : undefined}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />
    </View>
  )
}

// Swipeable Transaction Item Component
interface SwipeableTransactionItemProps {
  transaction: ITransaction
  onEdit: () => void
  onDelete: () => void
  formatCurrency: (amount: number) => string
  formatDate: (date: string) => string
}

function SwipeableTransactionItem({
  transaction,
  onEdit,
  onDelete,
  formatCurrency,
  formatDate
}: SwipeableTransactionItemProps) {
  const translateX = useRef(new Animated.Value(0)).current
  const [isSwipeActive, setIsSwipeActive] = useState(false)

  const handlePanGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: false }
  )

  const handlePanHandlerStateChange = (event: any) => {
    if (event.nativeEvent.state === State.END) {
      const { translationX } = event.nativeEvent

      if (translationX < -SWIPE_THRESHOLD) {
        // Swipe left - show actions
        setIsSwipeActive(true)
        Animated.spring(translateX, {
          toValue: -120,
          useNativeDriver: false,
        }).start()
      } else if (translationX > SWIPE_THRESHOLD) {
        // Swipe right - quick delete
        onDelete()
        resetSwipe()
      } else {
        // Reset to original position
        resetSwipe()
      }
    }
  }

  const resetSwipe = () => {
    setIsSwipeActive(false)
    Animated.spring(translateX, {
      toValue: 0,
      useNativeDriver: false,
    }).start()
  }

  return (
    <View style={styles.swipeContainer}>
      {/* Action Buttons (behind the item) */}
      <View style={styles.actionButtons}>
        <TouchableOpacity onPress={onEdit} style={[styles.actionButton, styles.editButton]}>
          <Text style={styles.actionButtonText}>Edit</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={onDelete} style={[styles.actionButton, styles.deleteButton]}>
          <Text style={styles.actionButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>

      {/* Main Transaction Item */}
      <PanGestureHandler
        onGestureEvent={handlePanGestureEvent}
        onHandlerStateChange={handlePanHandlerStateChange}
      >
        <Animated.View
          style={[
            styles.transactionItem,
            { transform: [{ translateX }] }
          ]}
        >
          <TouchableOpacity onPress={resetSwipe} style={styles.transactionContent}>
            <View style={styles.transactionLeft}>
              <Text style={styles.categoryIcon}>
                {transaction.category?.icon || '💰'}
              </Text>
              <View style={styles.transactionDetails}>
                <Text style={styles.categoryName}>
                  {transaction.category?.name || 'Uncategorized'}
                </Text>
                {transaction.description && (
                  <Text style={styles.description} numberOfLines={1}>
                    {transaction.description}
                  </Text>
                )}
                <Text style={styles.date}>
                  {formatDate(transaction.transaction_date)}
                </Text>
              </View>
            </View>
            
            <View style={styles.transactionRight}>
              <Text style={[
                styles.amount,
                transaction.transaction_type === 'income' 
                  ? styles.incomeAmount 
                  : styles.expenseAmount
              ]}>
                {transaction.transaction_type === 'income' ? '+' : '-'}
                {formatCurrency(transaction.amount)}
              </Text>
              <Text style={styles.transactionType}>
                {transaction.transaction_type}
              </Text>
            </View>
          </TouchableOpacity>
        </Animated.View>
      </PanGestureHandler>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  listContent: {
    flexGrow: 1,
  },
  header: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#F3F4F6',
    borderRadius: 6,
  },
  filterButtonActive: {
    backgroundColor: '#3B82F6',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  filterButtonTextActive: {
    color: 'white',
  },
  filtersContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
  },
  searchContainer: {
    marginBottom: 12,
  },
  searchInput: {
    height: 40,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    paddingHorizontal: 12,
    backgroundColor: 'white',
  },
  filterRow: {
    marginBottom: 8,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 4,
  },
  picker: {
    height: 40,
    backgroundColor: 'white',
    borderRadius: 6,
  },
  clearFiltersButton: {
    marginTop: 8,
    alignSelf: 'flex-start',
  },
  clearFiltersText: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500',
  },
  swipeContainer: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 8,
    overflow: 'hidden',
  },
  actionButtons: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 60,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editButton: {
    backgroundColor: '#3B82F6',
  },
  deleteButton: {
    backgroundColor: '#EF4444',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  transactionItem: {
    backgroundColor: 'white',
  },
  transactionContent: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  transactionLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  description: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  date: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 2,
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: 16,
    fontWeight: '600',
  },
  incomeAmount: {
    color: '#059669',
  },
  expenseAmount: {
    color: '#DC2626',
  },
  transactionType: {
    fontSize: 12,
    color: '#6B7280',
    textTransform: 'capitalize',
    marginTop: 2,
  },
  footer: {
    padding: 16,
  },
  loadingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginLeft: 8,
    color: '#6B7280',
  },
  loadMoreButton: {
    padding: 12,
    backgroundColor: '#3B82F6',
    borderRadius: 6,
    alignItems: 'center',
  },
  loadMoreText: {
    color: 'white',
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    fontSize: 16,
    color: '#DC2626',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#3B82F6',
    borderRadius: 6,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '500',
  },
})

export default TransactionListMobile