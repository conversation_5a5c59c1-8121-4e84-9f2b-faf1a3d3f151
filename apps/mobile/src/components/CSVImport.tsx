import React, { useState, useRef } from 'react'
import { CSVImportService, type ColumnMapping, type ParsedInvestmentTransaction, type ImportResult, AccountService, type IAccount } from '@repo/shared'

export interface CSVImportProps {
  onImportComplete?: (result: ImportResult) => void
  onCancel?: () => void
  className?: string
}

export function CSVImport({ onImportComplete, onCancel, className = '' }: CSVImportProps) {
  const [file, setFile] = useState<File | null>(null)
  const [csvContent, setCsvContent] = useState('')
  const [headers, setHeaders] = useState<string[]>([])
  const [mapping, setMapping] = useState<Partial<ColumnMapping>>({})
  const [previewData, setPreviewData] = useState<ParsedInvestmentTransaction[]>([])
  const [accounts, setAccounts] = useState<IAccount[]>([])
  const [selectedAccountId, setSelectedAccountId] = useState('')
  const [step, setStep] = useState<'upload' | 'mapping' | 'preview' | 'importing'>('upload')
  const [error, setError] = useState('')
  const [importing, setImporting] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  React.useEffect(() => {
    loadAccounts()
  }, [])

  const loadAccounts = async () => {
    try {
      const investmentAccounts = await AccountService.getAccounts({ account_type: 'investment' })
      setAccounts(investmentAccounts)
      if (investmentAccounts.length > 0) {
        setSelectedAccountId(investmentAccounts[0].id)
      }
    } catch (err) {
      console.error('Failed to load accounts:', err)
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (!selectedFile) return

    if (!selectedFile.name.toLowerCase().endsWith('.csv')) {
      setError('Please select a CSV file')
      return
    }

    if (selectedFile.size > 10 * 1024 * 1024) { // 10MB limit
      setError('File size must be less than 10MB')
      return
    }

    setFile(selectedFile)
    setError('')
    
    // Read file content
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setCsvContent(content)
      
      try {
        const csvHeaders = CSVImportService.getCSVHeaders(content)
        setHeaders(csvHeaders)
        
        // Auto-detect mapping
        const autoMapping = CSVImportService.autoDetectMapping(csvHeaders)
        setMapping(autoMapping)
        
        setStep('mapping')
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to parse CSV')
      }
    }
    reader.readAsText(selectedFile)
  }

  const handleMappingChange = (field: keyof ColumnMapping, value: string) => {
    setMapping(prev => ({ ...prev, [field]: value }))
  }

  const validateMapping = (): boolean => {
    const required = ['date', 'symbol', 'quantity', 'price', 'type']
    for (const field of required) {
      if (!mapping[field as keyof ColumnMapping]) {
        setError(`${field} mapping is required`)
        return false
      }
    }
    return true
  }

  const handlePreview = () => {
    if (!validateMapping()) return

    try {
      const rows = CSVImportService.parseCSV(csvContent)
      const transactions = CSVImportService.parseInvestmentTransactions(rows, mapping as ColumnMapping)
      setPreviewData(transactions.slice(0, 5)) // Show first 5 for mobile
      setStep('preview')
      setError('')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to parse transactions')
    }
  }

  const handleImport = async () => {
    if (!selectedAccountId) {
      setError('Please select an investment account')
      return
    }

    setImporting(true)
    setStep('importing')
    
    try {
      const rows = CSVImportService.parseCSV(csvContent)
      const transactions = CSVImportService.parseInvestmentTransactions(rows, mapping as ColumnMapping)
      const result = await CSVImportService.importInvestmentTransactions(transactions, selectedAccountId)
      
      onImportComplete?.(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to import transactions')
      setStep('preview')
    } finally {
      setImporting(false)
    }
  }

  const renderUploadStep = () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-2 text-text-primary">Upload CSV File</h3>
        <p className="text-text-secondary text-sm mb-4">
          Upload a CSV file containing your investment transactions.
        </p>
      </div>

      <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
        <input
          ref={fileInputRef}
          type="file"
          accept=".csv"
          onChange={handleFileSelect}
          className="hidden"
        />
        <button
          onClick={() => fileInputRef.current?.click()}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
        >
          Select CSV File
        </button>
        {file && (
          <p className="mt-2 text-sm text-text-secondary">
            Selected: {file.name}
          </p>
        )}
      </div>
    </div>
  )

  const renderMappingStep = () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-2 text-text-primary">Map CSV Columns</h3>
        <p className="text-text-secondary text-sm mb-4">
          Map your CSV columns to transaction fields. * = required
        </p>
      </div>

      <div className="space-y-3">
        {[
          { key: 'date', label: 'Date *', required: true },
          { key: 'symbol', label: 'Symbol *', required: true },
          { key: 'quantity', label: 'Quantity *', required: true },
          { key: 'price', label: 'Price *', required: true },
          { key: 'type', label: 'Type *', required: true },
          { key: 'description', label: 'Description', required: false },
          { key: 'fees', label: 'Fees', required: false },
        ].map(({ key, label, required }) => (
          <div key={key}>
            <label className="block text-sm font-medium text-text-primary mb-1">
              {label}
            </label>
            <select
              value={mapping[key as keyof ColumnMapping] || ''}
              onChange={(e) => handleMappingChange(key as keyof ColumnMapping, e.target.value)}
              className={`w-full px-3 py-2 border rounded-md bg-background ${
                required && !mapping[key as keyof ColumnMapping] 
                  ? 'border-error' 
                  : 'border-border'
              }`}
            >
              <option value="">Select column...</option>
              {headers.map(header => (
                <option key={header} value={header}>{header}</option>
              ))}
            </select>
          </div>
        ))}
      </div>

      <div className="flex space-x-3">
        <button
          onClick={() => setStep('upload')}
          className="px-4 py-2 border border-border rounded-md hover:bg-surface text-text-primary"
        >
          Back
        </button>
        <button
          onClick={handlePreview}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
        >
          Preview
        </button>
      </div>
    </div>
  )

  const renderPreviewStep = () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-2 text-text-primary">Preview</h3>
        <p className="text-text-secondary text-sm mb-4">
          Review transactions before importing.
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-text-primary mb-1">
          Investment Account *
        </label>
        <select
          value={selectedAccountId}
          onChange={(e) => setSelectedAccountId(e.target.value)}
          className="w-full px-3 py-2 border border-border rounded-md bg-background"
        >
          <option value="">Select account...</option>
          {accounts.map(account => (
            <option key={account.id} value={account.id}>
              {account.name}
            </option>
          ))}
        </select>
      </div>

      <div className="space-y-2">
        {previewData.map((transaction, index) => (
          <div key={index} className="p-3 border border-border rounded-md bg-surface">
            <div className="flex justify-between items-start mb-2">
              <div>
                <span className="font-medium text-text-primary">{transaction.symbol}</span>
                <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                  transaction.type === 'buy' 
                    ? 'bg-success-light text-success-dark' 
                    : 'bg-error-light text-error-dark'
                }`}>
                  {transaction.type.toUpperCase()}
                </span>
              </div>
              <span className="text-sm text-text-secondary">{transaction.date}</span>
            </div>
            <div className="text-sm text-text-secondary">
              {transaction.quantity} shares @ ${transaction.price.toFixed(2)} = ${(transaction.quantity * transaction.price).toFixed(2)}
            </div>
          </div>
        ))}
      </div>

      <div className="flex space-x-3">
        <button
          onClick={() => setStep('mapping')}
          className="px-4 py-2 border border-border rounded-md hover:bg-surface text-text-primary"
        >
          Back
        </button>
        <button
          onClick={handleImport}
          disabled={!selectedAccountId}
          className="px-4 py-2 bg-success text-white rounded-md hover:bg-success-dark disabled:opacity-50"
        >
          Import
        </button>
      </div>
    </div>
  )

  const renderImportingStep = () => (
    <div className="text-center py-8">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
      <h3 className="text-lg font-semibold mb-2 text-text-primary">Importing...</h3>
      <p className="text-text-secondary">Processing your CSV file.</p>
    </div>
  )

  return (
    <div className={`p-4 ${className}`}>
      <div className="bg-background rounded-lg shadow-sm border border-border p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-text-primary">Import Transactions</h2>
          {onCancel && (
            <button
              onClick={onCancel}
              className="text-text-secondary hover:text-text-primary"
            >
              ✕
            </button>
          )}
        </div>

        {error && (
          <div className="mb-4 p-3 bg-error-light border border-error rounded-md">
            <p className="text-error-dark text-sm">{error}</p>
          </div>
        )}

        {step === 'upload' && renderUploadStep()}
        {step === 'mapping' && renderMappingStep()}
        {step === 'preview' && renderPreviewStep()}
        {step === 'importing' && renderImportingStep()}
      </div>
    </div>
  )
}

export default CSVImport
