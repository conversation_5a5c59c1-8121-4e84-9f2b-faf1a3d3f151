import React, { useState, useEffect } from 'react'
import { TouchableOpacity, Text, View, Alert, Platform } from 'react-native'
import { useAuth } from '../contexts/AuthContext'

interface BiometricLoginButtonProps {
  onSuccess?: () => void
  onError?: (error: string) => void
  buttonStyle?: any
  textStyle?: any
  iconComponent?: React.ReactNode
  title?: string
  disabled?: boolean
}

export function BiometricLoginButton({
  onSuccess,
  onError,
  buttonStyle,
  textStyle,
  iconComponent,
  title = 'Sign in with Biometrics',
  disabled = false
}: BiometricLoginButtonProps) {
  const { signInWithBiometrics, isBiometricAvailable, isBiometricEnabled } = useAuth()
  const [isAvailable, setIsAvailable] = useState(false)
  const [isEnabled, setIsEnabled] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    checkBiometricStatus()
  }, [])

  const checkBiometricStatus = async () => {
    try {
      const available = isBiometricAvailable ? await isBiometricAvailable() : false
      const enabled = isBiometricEnabled ? await isBiometricEnabled() : false
      
      setIsAvailable(available)
      setIsEnabled(enabled)
    } catch (error) {
      console.error('Error checking biometric status:', error)
      setIsAvailable(false)
      setIsEnabled(false)
    }
  }

  const handleBiometricLogin = async () => {
    if (!isAvailable || !isEnabled || isLoading || disabled) {
      return
    }

    try {
      setIsLoading(true)
      
      if (!signInWithBiometrics) {
        throw new Error('Biometric authentication not available')
      }
      
      const { error } = await signInWithBiometrics()
      
      if (error) {
        const errorMessage = error.message || 'Biometric authentication failed'
        onError?.(errorMessage)
        
        // Don't show alert for user cancellation
        if (!errorMessage.toLowerCase().includes('cancel')) {
          Alert.alert('Authentication Failed', errorMessage)
        }
      } else {
        onSuccess?.()
      }
    } catch (error) {
      console.error('Biometric login error:', error)
      const errorMessage = 'An unexpected error occurred during biometric authentication'
      onError?.(errorMessage)
      Alert.alert('Error', errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  // Don't render on web or if not available/enabled
  if (Platform.OS === 'web' || !isAvailable || !isEnabled) {
    return null
  }

  return (
    <TouchableOpacity
      style={[
        {
          backgroundColor: '#007AFF',
          paddingVertical: 12,
          paddingHorizontal: 24,
          borderRadius: 8,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 44,
          opacity: (disabled || isLoading) ? 0.6 : 1,
        },
        buttonStyle
      ]}
      onPress={handleBiometricLogin}
      disabled={disabled || isLoading}
      activeOpacity={0.7}
    >
      {iconComponent && (
        <View style={{ marginRight: 8 }}>
          {iconComponent}
        </View>
      )}
      
      <Text style={[
        {
          color: '#ffffff',
          fontSize: 16,
          fontWeight: '600',
          textAlign: 'center',
        },
        textStyle
      ]}>
        {isLoading ? 'Authenticating...' : title}
      </Text>
    </TouchableOpacity>
  )
}