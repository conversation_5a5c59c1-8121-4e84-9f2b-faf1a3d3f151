import React, { useState, useRef } from 'react'
import { supabaseMobile as supabase } from '@repo/shared'
import { useAuth } from '../contexts/AuthContext'

export interface PhotoAttachmentProps {
  transactionId?: string
  onPhotoUploaded?: (url: string) => void
  onPhotoRemoved?: () => void
  initialPhotoUrl?: string
  className?: string
}

export function PhotoAttachment({ 
  transactionId, 
  onPhotoUploaded, 
  onPhotoRemoved,
  initialPhotoUrl,
  className = '' 
}: PhotoAttachmentProps) {
  const { user } = useAuth()
  const [photoUrl, setPhotoUrl] = useState(initialPhotoUrl || '')
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      uploadPhoto(file)
    }
  }

  const uploadPhoto = async (file: File) => {
    if (!user) {
      setError('User not authenticated')
      return
    }

    // Validate file
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file')
      return
    }

    if (file.size > 5 * 1024 * 1024) {
      setError('File size must be less than 5MB')
      return
    }

    try {
      setUploading(true)
      setError('')

      // Generate unique filename
      const fileExt = file.name.split('.').pop()
      const fileName = `${user.id}/${transactionId || 'temp'}_${Date.now()}.${fileExt}`
      const filePath = `receipts/${fileName}`

      // Upload to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('receipts')
        .upload(filePath, file, { upsert: true })

      if (uploadError) {
        setError(uploadError.message)
        return
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('receipts')
        .getPublicUrl(filePath)

      const publicUrl = urlData.publicUrl
      setPhotoUrl(publicUrl)
      onPhotoUploaded?.(publicUrl)

      // Update transaction with photo URL if transactionId is provided
      if (transactionId) {
        const { error: updateError } = await supabase
          .from('transactions')
          .update({ receipt_url: publicUrl })
          .eq('id', transactionId)

        if (updateError) {
          console.error('Error updating transaction with photo:', updateError)
        }
      }

    } catch (err) {
      setError('Failed to upload photo')
      console.error('Photo upload error:', err)
    } finally {
      setUploading(false)
    }
  }

  const removePhoto = async () => {
    if (!photoUrl) return

    try {
      // Extract file path from URL
      const urlParts = photoUrl.split('/')
      const fileName = urlParts[urlParts.length - 1]
      const filePath = `receipts/${user?.id}/${fileName}`

      // Delete from storage
      const { error } = await supabase.storage
        .from('receipts')
        .remove([filePath])

      if (error) {
        console.error('Error deleting photo:', error)
      }

      // Update transaction to remove photo URL
      if (transactionId) {
        const { error: updateError } = await supabase
          .from('transactions')
          .update({ receipt_url: null })
          .eq('id', transactionId)

        if (updateError) {
          console.error('Error removing photo from transaction:', updateError)
        }
      }

      setPhotoUrl('')
      onPhotoRemoved?.()

    } catch (err) {
      console.error('Error removing photo:', err)
    }
  }

  return (
    <div className={className}>
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Receipt Photo
        </label>

        {error && (
          <div className="text-red-600 dark:text-red-400 text-sm">
            {error}
          </div>
        )}

        {photoUrl ? (
          <div className="relative">
            <img
              src={photoUrl}
              alt="Receipt"
              className="w-full max-w-sm h-48 object-cover rounded-lg border border-gray-200 dark:border-gray-700"
            />
            <button
              onClick={removePhoto}
              className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white p-1 rounded-full"
              type="button"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        ) : (
          <div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
            />
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              disabled={uploading}
              className="w-full max-w-sm h-32 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex flex-col items-center justify-center hover:border-gray-400 dark:hover:border-gray-500 transition-colors disabled:opacity-50"
            >
              {uploading ? (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              ) : (
                <>
                  <svg className="w-8 h-8 text-gray-400 dark:text-gray-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {uploading ? 'Uploading...' : 'Add receipt photo'}
                  </span>
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  )
}