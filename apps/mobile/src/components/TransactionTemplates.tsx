import React, { useState, useEffect } from 'react'
import { supabaseMobile as supabase } from '@repo/shared'
import { RecurringTransactionService, type IRecurringTemplate, type RecurringFrequency, type Tables } from '@repo/shared'
import { useAuth } from '../contexts/AuthContext'

export type ITransactionTemplate = Tables<'transaction_templates'>

export interface TransactionTemplatesProps {
  onUseTemplate: (template: Omit<ITransactionTemplate, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => void
  categories?: any[]
  className?: string
}

const DEFAULT_TEMPLATES = [
  { name: 'Coffee', amount: 5, description: 'Daily coffee', transaction_type: 'expense' as const },
  { name: 'Lunch', amount: 15, description: 'Lunch break', transaction_type: 'expense' as const },
  { name: 'Groceries', amount: 50, description: 'Weekly groceries', transaction_type: 'expense' as const },
  { name: 'Gas', amount: 40, description: 'Fuel expense', transaction_type: 'expense' as const },
  { name: 'Salary', amount: 3000, description: 'Monthly salary', transaction_type: 'income' as const },
]

export function TransactionTemplates({ onUseTemplate, categories: propCategories = [], className = '' }: TransactionTemplatesProps) {
  const { user } = useAuth()
  const [templates, setTemplates] = useState<ITransactionTemplate[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    amount: 0,
    category_id: '',
    description: '',
    transaction_type: 'expense' as 'income' | 'expense',
    is_recurring: false,
    frequency: 'monthly' as RecurringFrequency,
    auto_create: false
  })

  useEffect(() => {
    if (user) {
      fetchTemplates()
    }
  }, [user])

  useEffect(() => {
    if (propCategories.length > 0) {
      setCategories(propCategories)
    } else if (user) {
      fetchCategories()
    }
  }, [propCategories, user])

  const fetchTemplates = async () => {
    try {
      const { data, error } = await supabase
        .from('transaction_templates')
        .select('*')
        .eq('user_id', user!.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching templates:', error)
        return
      }

      setTemplates(data || [])
    } catch (error) {
      console.error('Error fetching templates:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('user_id', user!.id)
        .order('name')

      if (error) {
        console.error('Error fetching categories:', error)
        return
      }

      setCategories(data || [])
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const createTemplate = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !newTemplate.name || !newTemplate.category_id) return

    try {
      const { data, error } = await supabase
        .from('transaction_templates')
        .insert({
          ...newTemplate,
          user_id: user.id
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating template:', error)
        return
      }

      setTemplates(prev => [data as ITransactionTemplate, ...prev])
      setNewTemplate({
        name: '',
        amount: 0,
        category_id: '',
        description: '',
        transaction_type: 'expense',
        is_recurring: false,
        frequency: 'monthly',
        auto_create: false
      })
      setShowCreateForm(false)
    } catch (error) {
      console.error('Error creating template:', error)
    }
  }

  const deleteTemplate = async (templateId: string) => {
    try {
      const { error } = await supabase
        .from('transaction_templates')
        .delete()
        .eq('id', templateId)

      if (error) {
        console.error('Error deleting template:', error)
        return
      }

      setTemplates(prev => prev.filter(t => t.id !== templateId))
    } catch (error) {
      console.error('Error deleting template:', error)
    }
  }

  const createDefaultTemplatesForUser = async () => {
    if (!user || !categories.length) return

    const defaultCategory = categories.find(c => c.type === 'expense') || categories[0]
    const incomeCategory = categories.find(c => c.type === 'income') || categories[0]

    const templatesWithCategories = DEFAULT_TEMPLATES.map(template => ({
      ...template,
      category_id: template.transaction_type === 'income' ? incomeCategory.id : defaultCategory.id,
      user_id: user.id
    }))

    try {
      const { data, error } = await supabase
        .from('transaction_templates')
        .insert(templatesWithCategories)
        .select()

      if (error) {
        console.error('Error creating default templates:', error)
        return
      }

      setTemplates(prev => [...(data || [] as ITransactionTemplate[]), ...prev])
    } catch (error) {
      console.error('Error creating default templates:', error)
    }
  }

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-text-primary">Quick Templates</h3>
        <button
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="text-sm bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent hover:from-blue-600 hover:to-purple-700 font-medium"
        >
          {showCreateForm ? 'Cancel' : 'Add Template'}
        </button>
      </div>

      {showCreateForm && (
        <form onSubmit={createTemplate} className="bg-surface-elevated p-4 rounded-lg mb-4 border border-border-light">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <input
              type="text"
              placeholder="Template name"
              value={newTemplate.name}
              onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
              className="px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary"
              required
            />
            <input
              type="number"
              placeholder="Amount"
              step="0.01"
              value={newTemplate.amount}
              onChange={(e) => setNewTemplate(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
              className="px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary"
              required
            />
            <select
              value={newTemplate.category_id}
              onChange={(e) => setNewTemplate(prev => ({ ...prev, category_id: e.target.value }))}
              className="px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary"
              required
            >
              <option value="">Select category</option>
              {categories.filter(category => category.type === newTemplate.transaction_type).map(category => (
                <option key={category.id} value={category.id}>
                  {category.icon} {category.name}
                </option>
              ))}
            </select>
            <select
              value={newTemplate.transaction_type}
              onChange={(e) => setNewTemplate(prev => ({ ...prev, transaction_type: e.target.value as 'income' | 'expense' }))}
              className="px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary"
            >
              <option value="expense">Expense</option>
              <option value="income">Income</option>
            </select>
          </div>
          
          {/* Recurring Options */}
          <div className="col-span-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={newTemplate.is_recurring}
                onChange={(e) => setNewTemplate(prev => ({ ...prev, is_recurring: e.target.checked }))}
                className="rounded border-gray-300 dark:border-gray-600"
              />
              <span className="text-sm text-text-primary">Make this a recurring transaction</span>
            </label>
          </div>
          
          {newTemplate.is_recurring && (
            <div className="col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-surface rounded-lg border border-border-light">
              <select
                value={newTemplate.frequency}
                onChange={(e) => setNewTemplate(prev => ({ ...prev, frequency: e.target.value as RecurringFrequency }))}
                className="px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary"
              >
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
              </select>
              
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={newTemplate.auto_create}
                  onChange={(e) => setNewTemplate(prev => ({ ...prev, auto_create: e.target.checked }))}
                  className="rounded border-gray-300 dark:border-gray-600"
                />
                <span className="text-sm text-text-primary">Auto-create transactions</span>
              </label>
            </div>
          )}
          
          <input
            type="text"
            placeholder="Description (optional)"
            value={newTemplate.description}
            onChange={(e) => setNewTemplate(prev => ({ ...prev, description: e.target.value }))}
            className="mt-2 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          />
          <button
            type="submit"
            className="mt-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-md hover:from-blue-600 hover:to-purple-700 text-sm font-medium transition-all"
          >
            Create Template
          </button>
        </form>
      )}

      {templates.length === 0 && (
        <div className="text-center py-8">
          <p className="text-text-secondary mb-4">No templates yet</p>
          <button
            onClick={createDefaultTemplatesForUser}
            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-md hover:from-blue-600 hover:to-purple-700 text-sm font-medium transition-all"
          >
            Create Default Templates
          </button>
        </div>
      )}

      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
        {templates.map((template) => (
          <div
            key={template.id}
            className="relative group p-3 bg-surface border border-border-light rounded-lg hover:shadow-md hover:border-blue-300 transition-all cursor-pointer"
            onClick={() => onUseTemplate(template)}
          >
            <button
              onClick={(e) => {
                e.stopPropagation()
                deleteTemplate(template.id)
              }}
              className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 transition-opacity"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium text-text-primary">{template.name}</div>
              {template.is_recurring && (
                <div className="flex items-center text-xs bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                  </svg>
                  {template.frequency}
                </div>
              )}
            </div>
            <div className={`text-lg font-bold ${template.transaction_type === 'income' ? 'text-green-600' : 'text-red-600'}`}>
              {template.transaction_type === 'income' ? '+' : '-'}${template.amount.toFixed(2)}
            </div>
            {template.description && (
              <div className="text-xs text-text-secondary mt-1 truncate">
                {template.description}
              </div>
            )}
            {template.is_recurring && template.next_due_date && (
              <div className="text-xs text-text-secondary mt-1">
                Next due: {new Date(template.next_due_date).toLocaleDateString()}
                {template.auto_create && (
                  <span className="ml-2 px-1 py-0.5 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded text-xs">
                    Auto
                  </span>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}