import React from 'react'
import { useCurrencyStore } from '@repo/shared'

export interface AmountDisplayProps {
  amount: number
  size?: 'small' | 'medium' | 'large'
  showSign?: boolean
  showPercentage?: boolean
  percentage?: number
  className?: string
}

export function AmountDisplay({ 
  amount, 
  size = 'medium', 
  showSign = false, 
  showPercentage = false,
  percentage,
  className = '' 
}: AmountDisplayProps) {
  const { formatCurrency } = useCurrencyStore()

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'text-sm font-medium'
      case 'large':
        return 'text-3xl font-bold'
      default:
        return 'text-xl font-semibold'
    }
  }

  const getColorClasses = () => {
    if (showSign) {
      return amount >= 0 ? 'text-success-green' : 'text-error-red'
    }
    return 'text-text-primary'
  }

  const formatDisplayAmount = () => {
    if (showSign) {
      const sign = amount >= 0 ? '+' : ''
      return `${sign}${formatCurrency(Math.abs(amount))}`
    }
    return formatCurrency(amount)
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className={`${getSizeClasses()} ${getColorClasses()}`}>
        {formatDisplayAmount()}
      </span>
      
      {showPercentage && percentage !== undefined && (
        <span className={`text-xs font-medium flex items-center gap-1 ${
          percentage >= 0 ? 'text-success-green' : 'text-error-red'
        }`}>
          {percentage >= 0 ? (
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          ) : (
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
            </svg>
          )}
          {Math.abs(percentage).toFixed(1)}%
        </span>
      )}
    </div>
  )
}

export default AmountDisplay