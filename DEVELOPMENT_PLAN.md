# Portfolio Tracker - Development Plan Reference

## Documentation Structure

This project uses a modular documentation approach for better organization:

### Core Files
- **`TODO.md`** - Current task tracking and progress
- **`PHASES_OVERVIEW.md`** - High-level overview of all development phases
- **`CLAUDE.md`** - Project coding standards and custom instructions

### Phase-Specific Files
- **`PHASE_1_MVP.md`** - Complete MVP development guide (current focus)
- **`PHASE_2_BANKING.md`** - Bank integration features (future)
- **`PHASE_3_INVESTMENTS.md`** - Investment tracking features (future)
- **`PHASE_4_TAX.md`** - Tax planning and reporting (future)
- **`PHASE_5_FAMILY.md`** - Family features and monetization (future)

## Current Status
- **Active Phase**: Phase 1 - MVP
- **Active Tasks**: See `TODO.md` for current progress
- **Next Steps**: Follow tasks in `PHASE_1_MVP.md`

## Quick Start
1. Check `TODO.md` for current task status
2. Follow detailed steps in `PHASE_1_MVP.md`
3. Use `CLAUDE.md` for coding standards
4. Track progress using todo list system

This modular approach reduces context size while keeping all information organized and accessible.