export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      account_balance_history: {
        Row: {
          account_id: string | null
          balance: number
          balance_date: string
          created_at: string | null
          id: string
          transaction_id: string | null
        }
        Insert: {
          account_id?: string | null
          balance: number
          balance_date: string
          created_at?: string | null
          id?: string
          transaction_id?: string | null
        }
        Update: {
          account_id?: string | null
          balance?: number
          balance_date?: string
          created_at?: string | null
          id?: string
          transaction_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "account_balance_history_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_balance_history_transaction_id_fkey"
            columns: ["transaction_id"]
            isOneToOne: false
            referencedRelation: "transactions"
            referencedColumns: ["id"]
          },
        ]
      }
      accounts: {
        Row: {
          account_metadata: Json | null
          account_number: string | null
          account_type: string
          available_balance: number | null
          created_at: string | null
          credit_limit: number | null
          currency: string | null
          current_balance: number | null
          id: string
          institution_name: string | null
          interest_rate: number | null
          is_active: boolean | null
          is_primary: boolean | null
          name: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          account_metadata?: Json | null
          account_number?: string | null
          account_type: string
          available_balance?: number | null
          created_at?: string | null
          credit_limit?: number | null
          currency?: string | null
          current_balance?: number | null
          id?: string
          institution_name?: string | null
          interest_rate?: number | null
          is_active?: boolean | null
          is_primary?: boolean | null
          name: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          account_metadata?: Json | null
          account_number?: string | null
          account_type?: string
          available_balance?: number | null
          created_at?: string | null
          credit_limit?: number | null
          currency?: string | null
          current_balance?: number | null
          id?: string
          institution_name?: string | null
          interest_rate?: number | null
          is_active?: boolean | null
          is_primary?: boolean | null
          name?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      budgets: {
        Row: {
          amount: number
          category_id: string | null
          created_at: string | null
          end_date: string | null
          id: string
          name: string
          period: string | null
          start_date: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          amount: number
          category_id?: string | null
          created_at?: string | null
          end_date?: string | null
          id?: string
          name: string
          period?: string | null
          start_date: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          amount?: number
          category_id?: string | null
          created_at?: string | null
          end_date?: string | null
          id?: string
          name?: string
          period?: string | null
          start_date?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "budgets_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          category_metadata: Json | null
          color: string | null
          created_at: string | null
          icon: string | null
          id: string
          is_active: boolean | null
          is_default: boolean | null
          is_system: boolean | null
          name: string
          parent_category_id: string | null
          sort_order: number | null
          type: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          category_metadata?: Json | null
          color?: string | null
          created_at?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          is_default?: boolean | null
          is_system?: boolean | null
          name: string
          parent_category_id?: string | null
          sort_order?: number | null
          type?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          category_metadata?: Json | null
          color?: string | null
          created_at?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          is_default?: boolean | null
          is_system?: boolean | null
          name?: string
          parent_category_id?: string | null
          sort_order?: number | null
          type?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "categories_parent_category_id_fkey"
            columns: ["parent_category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      investment_holdings: {
        Row: {
          account_id: string | null
          average_cost: number
          created_at: string | null
          current_price: number | null
          id: string
          last_updated: string | null
          market_value: number | null
          quantity: number
          symbol: string
          unrealized_gain_loss: number | null
        }
        Insert: {
          account_id?: string | null
          average_cost?: number
          created_at?: string | null
          current_price?: number | null
          id?: string
          last_updated?: string | null
          market_value?: number | null
          quantity?: number
          symbol: string
          unrealized_gain_loss?: number | null
        }
        Update: {
          account_id?: string | null
          average_cost?: number
          created_at?: string | null
          current_price?: number | null
          id?: string
          last_updated?: string | null
          market_value?: number | null
          quantity?: number
          symbol?: string
          unrealized_gain_loss?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "investment_holdings_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      transaction_templates: {
        Row: {
          amount: number
          auto_create: boolean | null
          category_id: string | null
          created_at: string | null
          description: string | null
          frequency: string | null
          id: string
          is_recurring: boolean | null
          last_created_date: string | null
          name: string
          next_due_date: string | null
          transaction_type: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          amount: number
          auto_create?: boolean | null
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          frequency?: string | null
          id?: string
          is_recurring?: boolean | null
          last_created_date?: string | null
          name: string
          next_due_date?: string | null
          transaction_type?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          amount?: number
          auto_create?: boolean | null
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          frequency?: string | null
          id?: string
          is_recurring?: boolean | null
          last_created_date?: string | null
          name?: string
          next_due_date?: string | null
          transaction_type?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transaction_templates_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      transactions: {
        Row: {
          account_id: string | null
          amount: number
          balance_after: number | null
          category_id: string | null
          created_at: string | null
          description: string | null
          fees: number | null
          id: string
          investment_price: number | null
          investment_quantity: number | null
          investment_symbol: string | null
          receipt_url: string | null
          to_account_id: string | null
          transaction_date: string
          transaction_status: string | null
          transaction_type: string | null
          transfer_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          account_id?: string | null
          amount: number
          balance_after?: number | null
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          fees?: number | null
          id?: string
          investment_price?: number | null
          investment_quantity?: number | null
          investment_symbol?: string | null
          receipt_url?: string | null
          to_account_id?: string | null
          transaction_date: string
          transaction_status?: string | null
          transaction_type?: string | null
          transfer_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          account_id?: string | null
          amount?: number
          balance_after?: number | null
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          fees?: number | null
          id?: string
          investment_price?: number | null
          investment_quantity?: number | null
          investment_symbol?: string | null
          receipt_url?: string | null
          to_account_id?: string | null
          transaction_date?: string
          transaction_status?: string | null
          transaction_type?: string | null
          transfer_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transactions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transactions_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transactions_to_account_id_fkey"
            columns: ["to_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      user_profiles: {
        Row: {
          avatar_url: string | null
          country: string | null
          created_at: string | null
          currency_preference: string | null
          display_name: string | null
          id: string
          notification_preferences: Json | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          avatar_url?: string | null
          country?: string | null
          created_at?: string | null
          currency_preference?: string | null
          display_name?: string | null
          id?: string
          notification_preferences?: Json | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          avatar_url?: string | null
          country?: string | null
          created_at?: string | null
          currency_preference?: string | null
          display_name?: string | null
          id?: string
          notification_preferences?: Json | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const