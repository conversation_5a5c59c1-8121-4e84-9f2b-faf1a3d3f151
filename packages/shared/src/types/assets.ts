// Asset class types and interfaces

export type AssetClass = 
  | 'stocks'
  | 'bonds'
  | 'mutual_funds'
  | 'etfs'
  | 'real_estate'
  | 'commodities'
  | 'crypto'
  | 'cash'
  | 'other'

export type AssetSubClass = 
  // Stock subcategories
  | 'large_cap'
  | 'mid_cap'
  | 'small_cap'
  | 'international'
  | 'emerging_markets'
  // Bond subcategories
  | 'government_bonds'
  | 'corporate_bonds'
  | 'municipal_bonds'
  | 'treasury_bills'
  | 'high_yield_bonds'
  // Mutual fund subcategories
  | 'equity_funds'
  | 'debt_funds'
  | 'hybrid_funds'
  | 'index_funds'
  | 'sector_funds'
  // ETF subcategories
  | 'equity_etfs'
  | 'bond_etfs'
  | 'commodity_etfs'
  | 'international_etfs'
  // Real estate subcategories
  | 'reits'
  | 'real_estate_funds'
  // Commodity subcategories
  | 'precious_metals'
  | 'energy'
  | 'agriculture'
  // Crypto subcategories
  | 'bitcoin'
  | 'altcoins'
  | 'stablecoins'

export interface IAssetClassInfo {
  id: string
  name: string
  class: AssetClass
  sub_class?: AssetSubClass
  description?: string
  risk_level: 'low' | 'medium' | 'high' | 'very_high'
  liquidity: 'high' | 'medium' | 'low'
  typical_holding_period: 'short_term' | 'medium_term' | 'long_term' // < 1 year, 1-5 years, > 5 years
  tax_treatment: 'equity' | 'debt' | 'other'
  // Tax holding periods for capital gains
  ltcg_period_months: number // Long-term capital gains period in months
  ltcg_tax_rate?: number // Long-term capital gains tax rate (%)
  stcg_tax_rate?: number // Short-term capital gains tax rate (%)
  dividend_tax_rate?: number // Dividend tax rate (%)
  created_at: string
  updated_at: string
}

export interface IAsset {
  id: string
  symbol: string
  name: string
  asset_class_id: string
  asset_class?: IAssetClassInfo
  exchange?: string
  currency: string
  isin?: string // International Securities Identification Number
  sector?: string
  industry?: string
  market_cap?: number
  current_price?: number
  last_updated?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface IHolding {
  id: string
  user_id: string
  account_id: string
  asset_id: string
  asset?: IAsset
  account?: any // IAccount type
  quantity: number
  average_cost: number
  current_value?: number
  unrealized_gain_loss?: number
  unrealized_gain_loss_percentage?: number
  total_invested: number
  last_updated: string
  created_at: string
  updated_at: string
}

export interface IInvestmentTransactionWithAsset extends IInvestmentTransaction {
  asset?: IAsset
  holding?: IHolding
  realized_gain_loss?: number
  tax_implications?: {
    holding_period_months: number
    is_long_term: boolean
    applicable_tax_rate: number
    tax_amount: number
  }
}

// Asset class form interfaces
export interface IAssetClassForm {
  name: string
  class: AssetClass
  sub_class?: AssetSubClass
  description?: string
  risk_level: 'low' | 'medium' | 'high' | 'very_high'
  liquidity: 'high' | 'medium' | 'low'
  typical_holding_period: 'short_term' | 'medium_term' | 'long_term'
  tax_treatment: 'equity' | 'debt' | 'other'
  ltcg_period_months: number
  ltcg_tax_rate?: number
  stcg_tax_rate?: number
  dividend_tax_rate?: number
}

export interface IAssetForm {
  symbol: string
  name: string
  asset_class_id: string
  exchange?: string
  currency: string
  isin?: string
  sector?: string
  industry?: string
  market_cap?: number
  current_price?: number
}

// Predefined asset classes with Indian tax implications
export const DEFAULT_ASSET_CLASSES: Omit<IAssetClassInfo, 'id' | 'created_at' | 'updated_at'>[] = [
  {
    name: 'Equity Shares',
    class: 'stocks',
    sub_class: 'large_cap',
    description: 'Listed equity shares on stock exchanges',
    risk_level: 'high',
    liquidity: 'high',
    typical_holding_period: 'long_term',
    tax_treatment: 'equity',
    ltcg_period_months: 12,
    ltcg_tax_rate: 10, // 10% above ₹1 lakh
    stcg_tax_rate: 15,
    dividend_tax_rate: 0 // Dividend income taxed as per slab
  },
  {
    name: 'Equity Mutual Funds',
    class: 'mutual_funds',
    sub_class: 'equity_funds',
    description: 'Mutual funds investing primarily in equity',
    risk_level: 'high',
    liquidity: 'high',
    typical_holding_period: 'long_term',
    tax_treatment: 'equity',
    ltcg_period_months: 12,
    ltcg_tax_rate: 10,
    stcg_tax_rate: 15
  },
  {
    name: 'Debt Mutual Funds',
    class: 'mutual_funds',
    sub_class: 'debt_funds',
    description: 'Mutual funds investing primarily in debt instruments',
    risk_level: 'medium',
    liquidity: 'high',
    typical_holding_period: 'medium_term',
    tax_treatment: 'debt',
    ltcg_period_months: 36,
    ltcg_tax_rate: 20, // With indexation
    stcg_tax_rate: 30 // As per income tax slab
  },
  {
    name: 'Government Bonds',
    class: 'bonds',
    sub_class: 'government_bonds',
    description: 'Government issued bonds and securities',
    risk_level: 'low',
    liquidity: 'medium',
    typical_holding_period: 'long_term',
    tax_treatment: 'debt',
    ltcg_period_months: 36,
    ltcg_tax_rate: 20,
    stcg_tax_rate: 30
  },
  {
    name: 'Corporate Bonds',
    class: 'bonds',
    sub_class: 'corporate_bonds',
    description: 'Corporate issued bonds and debentures',
    risk_level: 'medium',
    liquidity: 'medium',
    typical_holding_period: 'medium_term',
    tax_treatment: 'debt',
    ltcg_period_months: 36,
    ltcg_tax_rate: 20,
    stcg_tax_rate: 30
  },
  {
    name: 'Exchange Traded Funds',
    class: 'etfs',
    sub_class: 'equity_etfs',
    description: 'Exchange traded funds tracking various indices',
    risk_level: 'medium',
    liquidity: 'high',
    typical_holding_period: 'long_term',
    tax_treatment: 'equity',
    ltcg_period_months: 12,
    ltcg_tax_rate: 10,
    stcg_tax_rate: 15
  },
  {
    name: 'Real Estate Investment Trusts',
    class: 'real_estate',
    sub_class: 'reits',
    description: 'REITs investing in real estate properties',
    risk_level: 'medium',
    liquidity: 'medium',
    typical_holding_period: 'long_term',
    tax_treatment: 'other',
    ltcg_period_months: 36,
    ltcg_tax_rate: 20,
    stcg_tax_rate: 30
  },
  {
    name: 'Gold ETFs',
    class: 'commodities',
    sub_class: 'precious_metals',
    description: 'Gold exchange traded funds',
    risk_level: 'medium',
    liquidity: 'high',
    typical_holding_period: 'long_term',
    tax_treatment: 'other',
    ltcg_period_months: 36,
    ltcg_tax_rate: 20,
    stcg_tax_rate: 30
  }
]

// Import the base transaction interface
import type { IInvestmentTransaction } from '../types'
