import { supabase } from './supabase'
import { AssetClassService } from './assets'
import type { IAssetClassInfo } from '../types/assets'

export interface ITaxCalculationInput {
  asset_class_id: string
  purchase_date: string
  sale_date: string
  purchase_price: number
  sale_price: number
  quantity: number
  fees?: number
}

export interface ITaxCalculationResult {
  holding_period_days: number
  holding_period_months: number
  is_long_term: boolean
  purchase_value: number
  sale_value: number
  total_fees: number
  gross_capital_gain_loss: number
  indexation_benefit?: number
  indexed_cost?: number
  taxable_capital_gain_loss: number
  applicable_tax_rate: number
  tax_amount: number
  cess_amount: number
  total_tax_liability: number
  net_proceeds: number
  effective_tax_rate: number
  asset_class: IAssetClassInfo
}

export interface ITaxSummary {
  financial_year: string
  total_stcg: number
  total_ltcg: number
  total_stcg_tax: number
  total_ltcg_tax: number
  total_tax_liability: number
  exemption_used: number
  exemption_available: number
  transactions_count: number
}

export class TaxCalculatorService {
  /**
   * Calculate capital gains tax for a transaction
   */
  static async calculateCapitalGainsTax(input: ITaxCalculationInput): Promise<ITaxCalculationResult> {
    // Get asset class information
    const assetClass = await AssetClassService.getAssetClass(input.asset_class_id)

    // Calculate holding period
    const purchaseDate = new Date(input.purchase_date)
    const saleDate = new Date(input.sale_date)
    const holdingPeriodDays = Math.floor((saleDate.getTime() - purchaseDate.getTime()) / (1000 * 60 * 60 * 24))
    const holdingPeriodMonths = Math.floor(holdingPeriodDays / 30.44)

    // Determine if it's long-term
    const isLongTerm = holdingPeriodMonths >= assetClass.ltcg_period_months

    // Calculate basic values
    const purchaseValue = input.purchase_price * input.quantity
    const saleValue = input.sale_price * input.quantity
    const totalFees = input.fees || 0
    const grossCapitalGainLoss = saleValue - purchaseValue - totalFees

    // Calculate indexation benefit for debt instruments (if applicable)
    let indexationBenefit = 0
    let indexedCost = purchaseValue

    if (isLongTerm && assetClass.tax_treatment === 'debt') {
      // Simplified indexation calculation
      // In reality, this would use Cost Inflation Index (CII) from Income Tax Department
      const inflationRate = 0.04 // 4% annual inflation assumption
      const years = holdingPeriodDays / 365.25
      indexationBenefit = purchaseValue * (Math.pow(1 + inflationRate, years) - 1)
      indexedCost = purchaseValue + indexationBenefit
    }

    // Calculate taxable capital gain/loss
    const taxableCapitalGainLoss = isLongTerm && assetClass.tax_treatment === 'debt'
      ? saleValue - indexedCost - totalFees
      : grossCapitalGainLoss

    // Determine applicable tax rate
    let applicableTaxRate = 0
    if (taxableCapitalGainLoss > 0) { // Only tax on gains
      applicableTaxRate = isLongTerm 
        ? (assetClass.ltcg_tax_rate || 0)
        : (assetClass.stcg_tax_rate || 0)
    }

    // Apply exemption for LTCG on equity (₹1 lakh exemption)
    let exemptionApplied = 0
    let taxableAmount = Math.max(0, taxableCapitalGainLoss)

    if (isLongTerm && assetClass.tax_treatment === 'equity' && taxableAmount > 0) {
      const exemptionLimit = 100000 // ₹1 lakh
      exemptionApplied = Math.min(taxableAmount, exemptionLimit)
      taxableAmount = Math.max(0, taxableAmount - exemptionApplied)
    }

    // Calculate tax amount
    const taxAmount = taxableAmount * (applicableTaxRate / 100)

    // Calculate cess (4% on tax amount for income above certain threshold)
    const cessRate = 0.04 // 4% cess
    const cessAmount = taxAmount * cessRate

    // Total tax liability
    const totalTaxLiability = taxAmount + cessAmount

    // Net proceeds after tax
    const netProceeds = saleValue - totalFees - totalTaxLiability

    // Effective tax rate
    const effectiveTaxRate = grossCapitalGainLoss > 0 
      ? (totalTaxLiability / grossCapitalGainLoss) * 100 
      : 0

    return {
      holding_period_days: holdingPeriodDays,
      holding_period_months: holdingPeriodMonths,
      is_long_term: isLongTerm,
      purchase_value: purchaseValue,
      sale_value: saleValue,
      total_fees: totalFees,
      gross_capital_gain_loss: grossCapitalGainLoss,
      indexation_benefit: indexationBenefit > 0 ? indexationBenefit : undefined,
      indexed_cost: indexationBenefit > 0 ? indexedCost : undefined,
      taxable_capital_gain_loss: taxableCapitalGainLoss,
      applicable_tax_rate: applicableTaxRate,
      tax_amount: taxAmount,
      cess_amount: cessAmount,
      total_tax_liability: totalTaxLiability,
      net_proceeds: netProceeds,
      effective_tax_rate: effectiveTaxRate,
      asset_class: assetClass
    }
  }

  /**
   * Get tax summary for a financial year
   */
  static async getTaxSummary(financialYear: string, accountId?: string): Promise<ITaxSummary> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Financial year in India runs from April 1 to March 31
    const startDate = `${financialYear}-04-01`
    const endDate = `${parseInt(financialYear) + 1}-03-31`

    // Get all sell transactions for the financial year
    let query = supabase
      .from('investment_transactions')
      .select(`
        *,
        asset:assets(*,asset_class:asset_classes(*))
      `)
      .eq('user_id', user.id)
      .eq('transaction_type', 'investment_sell')
      .gte('transaction_date', startDate)
      .lte('transaction_date', endDate)

    if (accountId) {
      query = query.eq('account_id', accountId)
    }

    const { data: sellTransactions, error } = await query

    if (error) {
      throw new Error(`Failed to fetch transactions: ${error.message}`)
    }

    let totalSTCG = 0
    let totalLTCG = 0
    let totalSTCGTax = 0
    let totalLTCGTax = 0
    let totalTaxLiability = 0
    let exemptionUsed = 0

    // Calculate tax for each transaction
    for (const transaction of sellTransactions || []) {
      if (!transaction.asset?.asset_class) continue

      // Get corresponding buy transaction (simplified - assumes FIFO)
      const { data: buyTransaction } = await supabase
        .from('investment_transactions')
        .select('*')
        .eq('user_id', user.id)
        .eq('account_id', transaction.account_id)
        .eq('asset_id', transaction.asset_id)
        .eq('transaction_type', 'investment_buy')
        .lt('transaction_date', transaction.transaction_date)
        .order('transaction_date', { ascending: true })
        .limit(1)
        .single()

      if (!buyTransaction) continue

      const taxCalculation = await this.calculateCapitalGainsTax({
        asset_class_id: transaction.asset.asset_class_id,
        purchase_date: buyTransaction.transaction_date,
        sale_date: transaction.transaction_date,
        purchase_price: buyTransaction.investment_price || 0,
        sale_price: transaction.investment_price || 0,
        quantity: transaction.investment_quantity || 0,
        fees: (buyTransaction.fees || 0) + (transaction.fees || 0)
      })

      if (taxCalculation.is_long_term) {
        totalLTCG += taxCalculation.taxable_capital_gain_loss
        totalLTCGTax += taxCalculation.total_tax_liability
      } else {
        totalSTCG += taxCalculation.taxable_capital_gain_loss
        totalSTCGTax += taxCalculation.total_tax_liability
      }

      totalTaxLiability += taxCalculation.total_tax_liability

      // Track exemption usage for equity LTCG
      if (taxCalculation.is_long_term && 
          taxCalculation.asset_class.tax_treatment === 'equity' &&
          taxCalculation.taxable_capital_gain_loss > 0) {
        exemptionUsed += Math.min(100000, taxCalculation.taxable_capital_gain_loss)
      }
    }

    // Calculate available exemption
    const exemptionAvailable = Math.max(0, 100000 - exemptionUsed)

    return {
      financial_year: financialYear,
      total_stcg: totalSTCG,
      total_ltcg: totalLTCG,
      total_stcg_tax: totalSTCGTax,
      total_ltcg_tax: totalLTCGTax,
      total_tax_liability: totalTaxLiability,
      exemption_used: exemptionUsed,
      exemption_available: exemptionAvailable,
      transactions_count: sellTransactions?.length || 0
    }
  }

  /**
   * Get detailed tax report for multiple financial years
   */
  static async getTaxReport(options?: {
    start_year?: string
    end_year?: string
    account_id?: string
    asset_class_id?: string
  }) {
    const currentYear = new Date().getFullYear()
    const startYear = options?.start_year ? parseInt(options.start_year) : currentYear - 2
    const endYear = options?.end_year ? parseInt(options.end_year) : currentYear

    const taxSummaries: ITaxSummary[] = []

    for (let year = startYear; year <= endYear; year++) {
      const summary = await this.getTaxSummary(year.toString(), options?.account_id)
      taxSummaries.push(summary)
    }

    // Calculate totals across all years
    const totalSTCG = taxSummaries.reduce((sum, s) => sum + s.total_stcg, 0)
    const totalLTCG = taxSummaries.reduce((sum, s) => sum + s.total_ltcg, 0)
    const totalTax = taxSummaries.reduce((sum, s) => sum + s.total_tax_liability, 0)
    const totalTransactions = taxSummaries.reduce((sum, s) => sum + s.transactions_count, 0)

    return {
      yearly_summaries: taxSummaries,
      overall_summary: {
        total_stcg: totalSTCG,
        total_ltcg: totalLTCG,
        total_tax_liability: totalTax,
        total_transactions: totalTransactions,
        average_tax_rate: (totalSTCG + totalLTCG) > 0 ? (totalTax / (totalSTCG + totalLTCG)) * 100 : 0
      }
    }
  }

  /**
   * Get current financial year
   */
  static getCurrentFinancialYear(): string {
    const now = new Date()
    const currentYear = now.getFullYear()
    const currentMonth = now.getMonth() + 1 // JavaScript months are 0-indexed

    // Financial year in India starts from April
    if (currentMonth >= 4) {
      return currentYear.toString()
    } else {
      return (currentYear - 1).toString()
    }
  }

  /**
   * Validate asset class tax configuration
   */
  static validateAssetClassTaxConfig(assetClass: IAssetClassInfo): string[] {
    const errors: string[] = []

    if (!assetClass.ltcg_period_months || assetClass.ltcg_period_months < 0) {
      errors.push('LTCG period must be specified and non-negative')
    }

    if (assetClass.tax_treatment === 'equity') {
      if (!assetClass.ltcg_tax_rate && assetClass.ltcg_tax_rate !== 0) {
        errors.push('LTCG tax rate must be specified for equity assets')
      }
      if (!assetClass.stcg_tax_rate && assetClass.stcg_tax_rate !== 0) {
        errors.push('STCG tax rate must be specified for equity assets')
      }
    }

    if (assetClass.tax_treatment === 'debt') {
      if (!assetClass.ltcg_tax_rate && assetClass.ltcg_tax_rate !== 0) {
        errors.push('LTCG tax rate must be specified for debt assets')
      }
      if (!assetClass.stcg_tax_rate && assetClass.stcg_tax_rate !== 0) {
        errors.push('STCG tax rate must be specified for debt assets')
      }
    }

    return errors
  }
}
