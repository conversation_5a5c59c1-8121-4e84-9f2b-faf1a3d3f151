import { supabase } from './supabase'
import type { ITransaction, ICategory } from '../types'
import type { Tables, TablesInsert } from '../database.types'

export type IRecurringTemplate = Tables<'transaction_templates'>
export type RecurringFrequency = 'weekly' | 'monthly' | 'yearly'

export interface DueRecurringTransaction {
  template: IRecurringTemplate
  daysOverdue: number
  nextDueDate: Date
  category?: ICategory
}

export class RecurringTransactionService {
  /**
   * Get all recurring templates for the current user
   */
  static async getRecurringTemplates(): Promise<IRecurringTemplate[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('transaction_templates')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_recurring', true)
      .order('name')

    if (error) {
      throw new Error(`Failed to fetch recurring templates: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get all due recurring transactions for the current user
   */
  static async getDueRecurringTransactions(): Promise<DueRecurringTransaction[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('transaction_templates')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('user_id', user.id)
      .eq('is_recurring', true)
      .not('next_due_date', 'is', null)
      .lte('next_due_date', new Date().toISOString().split('T')[0])

    if (error) {
      throw new Error(`Failed to fetch due recurring transactions: ${error.message}`)
    }

    const templates = data || []
    const today = new Date()

    return templates.map(template => {
      const dueDate = new Date(template.next_due_date!)
      const daysOverdue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24))
      
      return {
        template: template as IRecurringTemplate,
        daysOverdue: Math.max(0, daysOverdue),
        nextDueDate: dueDate,
        category: template.category as ICategory
      }
    })
  }

  /**
   * Calculate the next due date based on frequency
   */
  static calculateNextDueDate(currentDate: Date, frequency: RecurringFrequency): Date {
    const nextDate = new Date(currentDate)
    
    switch (frequency) {
      case 'weekly':
        nextDate.setDate(nextDate.getDate() + 7)
        break
      case 'monthly':
        // Handle month-end edge cases
        const originalDay = nextDate.getDate()
        nextDate.setMonth(nextDate.getMonth() + 1)
        
        // If the day doesn't exist in the next month (e.g., Jan 31 -> Feb 28)
        if (nextDate.getDate() !== originalDay) {
          nextDate.setDate(0) // Set to last day of previous month
        }
        break
      case 'yearly':
        nextDate.setFullYear(nextDate.getFullYear() + 1)
        
        // Handle leap year edge case (Feb 29 -> Feb 28)
        if (nextDate.getMonth() === 1 && nextDate.getDate() === 29) {
          // Check if next year is not a leap year
          const nextYear = nextDate.getFullYear()
          if (!this.isLeapYear(nextYear)) {
            nextDate.setDate(28)
          }
        }
        break
      default:
        throw new Error(`Invalid frequency: ${frequency}`)
    }
    
    return nextDate
  }

  /**
   * Check if a year is a leap year
   */
  private static isLeapYear(year: number): boolean {
    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0)
  }

  /**
   * Create a transaction from a recurring template
   */
  static async createTransactionFromTemplate(
    template: IRecurringTemplate, 
    transactionDate?: Date
  ): Promise<ITransaction> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const date = transactionDate || new Date()
    
    // Create the transaction
    const transactionData: TablesInsert<'transactions'> = {
      amount: template.amount,
      category_id: template.category_id,
      description: template.description,
      transaction_type: template.transaction_type as 'income' | 'expense',
      transaction_date: date.toISOString().split('T')[0],
      user_id: user.id
    }

    const { data: transaction, error: transactionError } = await supabase
      .from('transactions')
      .insert(transactionData)
      .select(`
        *,
        category:categories(*)
      `)
      .single()

    if (transactionError) {
      throw new Error(`Failed to create transaction: ${transactionError.message}`)
    }

    // Update the template with the next due date and last created date
    if (template.frequency) {
      const nextDueDate = this.calculateNextDueDate(date, template.frequency as RecurringFrequency)
      
      const { error: updateError } = await supabase
        .from('transaction_templates')
        .update({
          next_due_date: nextDueDate.toISOString().split('T')[0],
          last_created_date: date.toISOString().split('T')[0]
        })
        .eq('id', template.id)

      if (updateError) {
        console.error('Failed to update template next due date:', updateError)
        // Don't throw error as transaction was created successfully
      }
    }

    return transaction as ITransaction
  }

  /**
   * Create transactions from multiple templates
   */
  static async createTransactionsFromTemplates(
    templates: IRecurringTemplate[],
    transactionDate?: Date
  ): Promise<{ success: ITransaction[]; errors: Array<{ template: IRecurringTemplate; error: string }> }> {
    const success: ITransaction[] = []
    const errors: Array<{ template: IRecurringTemplate; error: string }> = []

    for (const template of templates) {
      try {
        const transaction = await this.createTransactionFromTemplate(template, transactionDate)
        success.push(transaction)
      } catch (error) {
        errors.push({
          template,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return { success, errors }
  }

  /**
   * Update a recurring template
   */
  static async updateRecurringTemplate(
    templateId: string,
    updates: Partial<IRecurringTemplate>
  ): Promise<IRecurringTemplate> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // If frequency changed, recalculate next due date
    if (updates.frequency && updates.last_created_date) {
      const lastCreated = new Date(updates.last_created_date)
      updates.next_due_date = this.calculateNextDueDate(
        lastCreated, 
        updates.frequency as RecurringFrequency
      ).toISOString().split('T')[0]
    }

    const { data, error } = await supabase
      .from('transaction_templates')
      .update(updates)
      .eq('id', templateId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update recurring template: ${error.message}`)
    }

    return data as IRecurringTemplate
  }

  /**
   * Create a new recurring template
   */
  static async createRecurringTemplate(
    templateData: Omit<IRecurringTemplate, 'id' | 'user_id' | 'created_at' | 'updated_at'>
  ): Promise<IRecurringTemplate> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Calculate initial next due date if recurring
    let nextDueDate: string | null = null
    if (templateData.is_recurring && templateData.frequency) {
      const startDate = new Date()
      nextDueDate = this.calculateNextDueDate(
        startDate, 
        templateData.frequency as RecurringFrequency
      ).toISOString().split('T')[0]
    }

    const insertData: TablesInsert<'transaction_templates'> = {
      ...templateData,
      user_id: user.id,
      next_due_date: nextDueDate
    }

    const { data, error } = await supabase
      .from('transaction_templates')
      .insert(insertData)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create recurring template: ${error.message}`)
    }

    return data as IRecurringTemplate
  }

  /**
   * Disable recurring for a template
   */
  static async disableRecurring(templateId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { error } = await supabase
      .from('transaction_templates')
      .update({
        is_recurring: false,
        frequency: null,
        next_due_date: null,
        auto_create: false
      })
      .eq('id', templateId)
      .eq('user_id', user.id)

    if (error) {
      throw new Error(`Failed to disable recurring: ${error.message}`)
    }
  }

  /**
   * Process all due recurring transactions for auto-create templates
   * This should be called on app startup/login
   */
  static async processAutoCreateRecurringTransactions(): Promise<{
    created: number
    errors: Array<{ template: IRecurringTemplate; error: string }>
  }> {
    try {
      const dueTransactions = await this.getDueRecurringTransactions()
      const autoCreateTemplates = dueTransactions
        .filter(({ template }) => template.auto_create)
        .map(({ template }) => template)

      if (autoCreateTemplates.length === 0) {
        return { created: 0, errors: [] }
      }

      const result = await this.createTransactionsFromTemplates(autoCreateTemplates)
      
      return {
        created: result.success.length,
        errors: result.errors
      }
    } catch (error) {
      console.error('Error processing auto-create recurring transactions:', error)
      return {
        created: 0,
        errors: [{
          template: {} as IRecurringTemplate,
          error: error instanceof Error ? error.message : 'Unknown error'
        }]
      }
    }
  }

  /**
   * Get upcoming recurring transactions (next 30 days)
   */
  static async getUpcomingRecurringTransactions(days: number = 30): Promise<Array<{
    template: IRecurringTemplate
    dueDate: Date
    daysUntilDue: number
    category?: ICategory
  }>> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const today = new Date()
    const futureDate = new Date()
    futureDate.setDate(today.getDate() + days)

    const { data, error } = await supabase
      .from('transaction_templates')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('user_id', user.id)
      .eq('is_recurring', true)
      .not('next_due_date', 'is', null)
      .gte('next_due_date', today.toISOString().split('T')[0])
      .lte('next_due_date', futureDate.toISOString().split('T')[0])

    if (error) {
      throw new Error(`Failed to fetch upcoming recurring transactions: ${error.message}`)
    }

    const templates = data || []

    return templates.map(template => {
      const dueDate = new Date(template.next_due_date!)
      const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
      
      return {
        template: template as IRecurringTemplate,
        dueDate,
        daysUntilDue,
        category: template.category as ICategory
      }
    }).sort((a, b) => a.daysUntilDue - b.daysUntilDue)
  }
}