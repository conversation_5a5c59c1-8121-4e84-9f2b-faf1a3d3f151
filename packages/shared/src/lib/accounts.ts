import { supabase } from './supabase'
import type { IAccount, IAccountForm, AccountType } from '../types'
import type { TablesInsert, TablesUpdate } from '../database.types'

export class AccountService {
  /**
   * Get all accounts for the current user
   */
  static async getAccounts(options?: {
    account_type?: AccountType
    is_active?: boolean
  }): Promise<IAccount[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    let query = supabase
      .from('accounts')
      .select('*')
      .eq('user_id', user.id)
      .order('is_primary', { ascending: false })
      .order('account_type')
      .order('name')

    if (options?.account_type) {
      query = query.eq('account_type', options.account_type)
    }

    if (options?.is_active !== undefined) {
      query = query.eq('is_active', options.is_active)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch accounts: ${error.message}`)
    }

    return data as IAccount[]
  }

  /**
   * Get a specific account by ID
   */
  static async getAccount(id: string): Promise<IAccount> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('accounts')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (error) {
      throw new Error(`Failed to fetch account: ${error.message}`)
    }

    return data as IAccount
  }

  /**
   * Create a new account
   */
  static async createAccount(accountData: IAccountForm): Promise<IAccount> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Check if account name already exists for this user
    const { data: existingAccount } = await supabase
      .from('accounts')
      .select('id')
      .eq('user_id', user.id)
      .eq('name', accountData.name)
      .single()

    if (existingAccount) {
      throw new Error('Account name already exists')
    }

    // If this is set as primary, unset other primary accounts of the same type
    if (accountData.is_primary) {
      await supabase
        .from('accounts')
        .update({ is_primary: false })
        .eq('user_id', user.id)
        .eq('account_type', accountData.account_type)
    }

    const insertData: TablesInsert<'accounts'> = {
      ...accountData,
      user_id: user.id,
      currency: accountData.currency || 'USD',
      current_balance: accountData.current_balance || 0,
      is_active: true,
      is_primary: accountData.is_primary || false,
    }

    const { data, error } = await supabase
      .from('accounts')
      .insert(insertData)
      .select('*')
      .single()

    if (error) {
      throw new Error(`Failed to create account: ${error.message}`)
    }

    return data as IAccount
  }

  /**
   * Update an existing account
   */
  static async updateAccount(id: string, updates: Partial<IAccountForm>): Promise<IAccount> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // If updating to primary, unset other primary accounts of the same type
    if (updates.is_primary) {
      const account = await this.getAccount(id)
      await supabase
        .from('accounts')
        .update({ is_primary: false })
        .eq('user_id', user.id)
        .eq('account_type', account.account_type)
        .neq('id', id)
    }

    const updateData: TablesUpdate<'accounts'> = {
      ...updates,
      updated_at: new Date().toISOString(),
    }

    const { data, error } = await supabase
      .from('accounts')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select('*')
      .single()

    if (error) {
      throw new Error(`Failed to update account: ${error.message}`)
    }

    return data as IAccount
  }

  /**
   * Delete an account (soft delete by setting is_active to false)
   */
  static async deleteAccount(id: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Check if account has transactions
    const { data: transactions } = await supabase
      .from('transactions')
      .select('id')
      .or(`account_id.eq.${id},to_account_id.eq.${id}`)
      .limit(1)

    if (transactions && transactions.length > 0) {
      // Soft delete if account has transactions
      const { error } = await supabase
        .from('accounts')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('user_id', user.id)

      if (error) {
        throw new Error(`Failed to deactivate account: ${error.message}`)
      }
    } else {
      // Hard delete if no transactions
      const { error } = await supabase
        .from('accounts')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id)

      if (error) {
        throw new Error(`Failed to delete account: ${error.message}`)
      }
    }
  }

  /**
   * Get account balance history
   */
  static async getAccountBalanceHistory(
    accountId: string,
    options?: {
      startDate?: string
      endDate?: string
      limit?: number
    }
  ): Promise<any[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    let query = supabase
      .from('account_balance_history')
      .select(`
        *,
        transaction:transactions(*)
      `)
      .eq('account_id', accountId)
      .order('balance_date', { ascending: false })

    if (options?.startDate) {
      query = query.gte('balance_date', options.startDate)
    }

    if (options?.endDate) {
      query = query.lte('balance_date', options.endDate)
    }

    if (options?.limit) {
      query = query.limit(options.limit)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch balance history: ${error.message}`)
    }

    return data || []
  }

  /**
   * Create default accounts for a new user
   */
  static async createDefaultAccounts(): Promise<IAccount[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const defaultAccounts: IAccountForm[] = [
      {
        name: 'Cash',
        account_type: 'cash',
        currency: 'USD',
        current_balance: 0,
        is_primary: true,
      }
    ]

    const createdAccounts: IAccount[] = []

    for (const accountData of defaultAccounts) {
      try {
        const account = await this.createAccount(accountData)
        createdAccounts.push(account)
      } catch (error) {
        console.error('Failed to create default account:', error)
      }
    }

    return createdAccounts
  }

  /**
   * Get account summary with balances by type
   */
  static async getAccountSummary(): Promise<{
    totalAssets: number
    totalLiabilities: number
    netWorth: number
    accountsByType: Record<AccountType, { count: number; balance: number }>
  }> {
    const accounts = await this.getAccounts({ is_active: true })

    let totalAssets = 0
    let totalLiabilities = 0
    const accountsByType: Record<AccountType, { count: number; balance: number }> = {
      bank: { count: 0, balance: 0 },
      investment: { count: 0, balance: 0 },
      savings: { count: 0, balance: 0 },
      credit_card: { count: 0, balance: 0 },
      cash: { count: 0, balance: 0 },
    }

    accounts.forEach(account => {
      const balance = account.current_balance || 0
      
      accountsByType[account.account_type].count++
      accountsByType[account.account_type].balance += balance

      if (account.account_type === 'credit_card') {
        // Credit card balances are liabilities (negative is debt)
        totalLiabilities += Math.abs(balance)
      } else {
        totalAssets += balance
      }
    })

    return {
      totalAssets,
      totalLiabilities,
      netWorth: totalAssets - totalLiabilities,
      accountsByType,
    }
  }
}
