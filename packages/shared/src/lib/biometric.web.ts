export interface BiometricCapability {
  isAvailable: boolean;
  biometricType: string[];
  hasHardware: boolean;
  isEnrolled: boolean;
}

export interface BiometricAuthResult {
  success: boolean;
  error?: string;
  cancelled?: boolean;
}

export class BiometricService {
  /**
   * Check if biometric authentication is available on the device
   * Web implementation - always returns unavailable
   */
  static async getCapabilities(): Promise<BiometricCapability> {
    return {
      isAvailable: false,
      biometricType: [],
      hasHardware: false,
      isEnrolled: false,
    };
  }

  /**
   * Authenticate user with biometrics
   * Web implementation - always returns unavailable
   */
  static async authenticate(
    reason: string = 'Please authenticate to continue'
  ): Promise<BiometricAuthResult> {
    return {
      success: false,
      error: 'Biometric authentication is not available on web',
    };
  }

  /**
   * Check if biometric login is enabled by the user
   * Web implementation - always returns false
   */
  static async isBiometricEnabled(): Promise<boolean> {
    return false;
  }

  /**
   * Enable or disable biometric login
   * Web implementation - no-op
   */
  static async setBiometricEnabled(enabled: boolean): Promise<void> {
    // No-op for web
  }

  /**
   * Store encrypted credentials for biometric login
   * Web implementation - no-op
   */
  static async storeCredentials(email: string, hashedPassword: string): Promise<void> {
    // No-op for web
  }

  /**
   * Retrieve stored credentials after successful biometric authentication
   * Web implementation - always returns null
   */
  static async getStoredCredentials(): Promise<{email: string; hashedPassword: string} | null> {
    return null;
  }

  /**
   * Clear all biometric data
   * Web implementation - no-op
   */
  static async clearBiometricData(): Promise<void> {
    // No-op for web
  }
}