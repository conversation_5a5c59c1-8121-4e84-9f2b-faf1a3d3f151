import { supabase } from './supabase'
import type { ITransaction, ICategory } from '../types'
import type { ExpenseData } from '../schemas/expense'

export class ExpenseService {
  static async createTransaction(data: ExpenseData): Promise<ITransaction> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const transactionData = {
      amount: data.amount,
      description: data.description || null,
      category_id: data.category_id,
      transaction_type: data.transaction_type,
      transaction_date: data.transaction_date.toISOString().split('T')[0],
      user_id: user.id,
    }

    const { data: transaction, error } = await supabase
      .from('transactions')
      .insert(transactionData)
      .select(`
        *,
        category:categories(*)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to create transaction: ${error.message}`)
    }

    return transaction as ITransaction
  }

  static async getTransactions(options?: {
    limit?: number
    offset?: number
    categoryId?: string
    startDate?: string
    endDate?: string
    searchQuery?: string
    transactionType?: 'income' | 'expense'
  }): Promise<{ data: ITransaction[]; count: number }> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    let query = supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*)
      `, { count: 'exact' })
      .eq('user_id', user.id)
      .order('transaction_date', { ascending: false })
      .order('created_at', { ascending: false })

    // Apply filters
    if (options?.categoryId) {
      query = query.eq('category_id', options.categoryId)
    }

    if (options?.startDate) {
      query = query.gte('transaction_date', options.startDate)
    }

    if (options?.endDate) {
      query = query.lte('transaction_date', options.endDate)
    }

    if (options?.transactionType) {
      query = query.eq('transaction_type', options.transactionType)
    }

    if (options?.searchQuery) {
      // Search in both description and category name
      query = query.or(
        `description.ilike.%${options.searchQuery}%,` +
        `category.name.ilike.%${options.searchQuery}%`
      )
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit)
    }

    if (options?.offset) {
      query = query.range(options.offset, (options.offset + (options.limit || 10)) - 1)
    }

    const { data, error, count } = await query

    if (error) {
      throw new Error(`Failed to fetch transactions: ${error.message}`)
    }

    return {
      data: data as ITransaction[],
      count: count || 0
    }
  }

  static async updateTransaction(id: string, data: Partial<ExpenseData>): Promise<ITransaction> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const updateData: any = {}

    if (data.amount !== undefined) updateData.amount = data.amount
    if (data.description !== undefined) updateData.description = data.description
    if (data.category_id !== undefined) updateData.category_id = data.category_id
    if (data.transaction_type !== undefined) updateData.transaction_type = data.transaction_type
    if (data.transaction_date !== undefined) {
      updateData.transaction_date = data.transaction_date.toISOString().split('T')[0]
    }

    updateData.updated_at = new Date().toISOString()

    const { data: transaction, error } = await supabase
      .from('transactions')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select(`
        *,
        category:categories(*)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to update transaction: ${error.message}`)
    }

    return transaction as ITransaction
  }

  static async deleteTransaction(id: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { error } = await supabase
      .from('transactions')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      throw new Error(`Failed to delete transaction: ${error.message}`)
    }
  }

  static async getCategories(): Promise<ICategory[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .or(`user_id.eq.${user.id},and(is_default.eq.true,user_id.is.null)`)
      .order('name')

    if (error) {
      throw new Error(`Failed to fetch categories: ${error.message}`)
    }

    // If no categories exist for the user, create default categories
    if (!data || data.length === 0) {
      await this.createDefaultCategories()
      // Fetch categories again after creating defaults
      const { data: newData, error: newError } = await supabase
        .from('categories')
        .select('*')
        .or(`user_id.eq.${user.id},and(is_default.eq.true,user_id.is.null)`)
        .order('name')

      if (newError) {
        throw new Error(`Failed to fetch categories after creating defaults: ${newError.message}`)
      }

      return newData as ICategory[]
    }

    return data as ICategory[]
  }

  static async createDefaultCategories(): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Check if user already has categories to prevent duplicates
    const { data: existingCategories, error: checkError } = await supabase
      .from('categories')
      .select('id')
      .eq('user_id', user.id)
      .limit(1)

    if (checkError) {
      throw new Error(`Failed to check existing categories: ${checkError.message}`)
    }

    // If user already has categories, don't create defaults
    if (existingCategories && existingCategories.length > 0) {
      return
    }

    const defaultCategories = [
      // Expense categories
      { name: 'Food & Dining', icon: '🍽️', color: '#FF6B6B', type: 'expense' },
      { name: 'Transportation', icon: '🚗', color: '#4ECDC4', type: 'expense' },
      { name: 'Shopping', icon: '🛍️', color: '#45B7D1', type: 'expense' },
      { name: 'Entertainment', icon: '🎬', color: '#96CEB4', type: 'expense' },
      { name: 'Bills & Utilities', icon: '💡', color: '#FFEAA7', type: 'expense' },
      { name: 'Healthcare', icon: '🏥', color: '#DDA0DD', type: 'expense' },
      { name: 'Education', icon: '📚', color: '#98D8C8', type: 'expense' },
      { name: 'Travel', icon: '✈️', color: '#F7DC6F', type: 'expense' },
      { name: 'Groceries', icon: '🛒', color: '#82E0AA', type: 'expense' },
      { name: 'Other', icon: '📦', color: '#BDC3C7', type: 'expense' },
      
      // Income categories
      { name: 'Salary', icon: '💰', color: '#27AE60', type: 'income' },
      { name: 'Freelance', icon: '💻', color: '#2ECC71', type: 'income' },
      { name: 'Business', icon: '🏢', color: '#58D68D', type: 'income' },
      { name: 'Investment', icon: '📈', color: '#85C1E9', type: 'income' },
      { name: 'Gift', icon: '🎁', color: '#F8C471', type: 'income' },
      { name: 'Other Income', icon: '💎', color: '#D5DBDB', type: 'income' },
    ]

    const categoriesToInsert = defaultCategories.map(category => ({
      ...category,
      user_id: user.id,
      is_default: false,
    }))

    const { error } = await supabase
      .from('categories')
      .insert(categoriesToInsert)

    if (error) {
      throw new Error(`Failed to create default categories: ${error.message}`)
    }
  }

  static async createCategory(categoryData: {
    name: string
    icon: string
    color: string
    type: 'income' | 'expense'
  }): Promise<ICategory> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('categories')
      .insert({
        ...categoryData,
        user_id: user.id,
        is_default: false,
      })
      .select('*')
      .single()

    if (error) {
      throw new Error(`Failed to create category: ${error.message}`)
    }

    return data as ICategory
  }

  static async getMonthlySpending(year: number, month: number): Promise<{
    totalIncome: number
    totalExpenses: number
    categoryBreakdown: Array<{ category: ICategory; total: number }>
  }> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const startDate = new Date(year, month - 1, 1).toISOString().split('T')[0]
    const endDate = new Date(year, month, 0).toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('user_id', user.id)
      .gte('transaction_date', startDate)
      .lte('transaction_date', endDate)

    if (error) {
      throw new Error(`Failed to fetch monthly spending: ${error.message}`)
    }

    const transactions = data as ITransaction[]
    
    const totalIncome = transactions
      .filter(t => t.transaction_type === 'income')
      .reduce((sum, t) => sum + t.amount, 0)

    const totalExpenses = transactions
      .filter(t => t.transaction_type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0)

    // Group by category
    const categoryMap: Record<string, { category: ICategory; total: number }> = {}
    
    transactions.forEach(transaction => {
      if (transaction.category) {
        const categoryId = transaction.category.id
        if (!categoryMap[categoryId]) {
          categoryMap[categoryId] = {
            category: transaction.category,
            total: 0
          }
        }
        categoryMap[categoryId].total += transaction.amount
      }
    })

    const categoryBreakdown = Object.values(categoryMap)

    return {
      totalIncome,
      totalExpenses,
      categoryBreakdown
    }
  }
}