import * as LocalAuthentication from 'expo-local-authentication';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface BiometricCapability {
  isAvailable: boolean;
  biometricType: string[];
  hasHardware: boolean;
  isEnrolled: boolean;
}

export interface BiometricAuthResult {
  success: boolean;
  error?: string;
  cancelled?: boolean;
}

const BIOMETRIC_ENABLED_KEY = '@biometric_enabled';
const BIOMETRIC_CREDENTIALS_KEY = '@biometric_credentials';

export class BiometricService {
  /**
   * Check if biometric authentication is available on the device
   */
  static async getCapabilities(): Promise<BiometricCapability> {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      
      const biometricType = supportedTypes.map(type => {
        switch (type) {
          case LocalAuthentication.AuthenticationType.FINGERPRINT:
            return 'fingerprint';
          case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
            return 'face';
          case LocalAuthentication.AuthenticationType.IRIS:
            return 'iris';
          default:
            return 'unknown';
        }
      });

      return {
        isAvailable: hasHardware && isEnrolled,
        biometricType,
        hasHardware,
        isEnrolled,
      };
    } catch (error) {
      console.error('Error checking biometric capabilities:', error);
      return {
        isAvailable: false,
        biometricType: [],
        hasHardware: false,
        isEnrolled: false,
      };
    }
  }

  /**
   * Authenticate user with biometrics
   */
  static async authenticate(
    reason: string = 'Please authenticate to continue'
  ): Promise<BiometricAuthResult> {
    try {
      const capabilities = await this.getCapabilities();
      
      if (!capabilities.isAvailable) {
        return {
          success: false,
          error: 'Biometric authentication is not available on this device',
        };
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: reason,
        fallbackLabel: 'Use passcode',
        disableDeviceFallback: false,
        cancelLabel: 'Cancel',
      });

      if (result.success) {
        return { success: true };
      } else {
        return {
          success: false,
          error: result.error || 'Authentication failed',
          cancelled: result.error === 'user_cancel' || result.error === 'app_cancel',
        };
      }
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return {
        success: false,
        error: 'An unexpected error occurred during authentication',
      };
    }
  }

  /**
   * Check if biometric login is enabled by the user
   */
  static async isBiometricEnabled(): Promise<boolean> {
    try {
      const enabled = await AsyncStorage.getItem(BIOMETRIC_ENABLED_KEY);
      return enabled === 'true';
    } catch (error) {
      console.error('Error checking biometric setting:', error);
      return false;
    }
  }

  /**
   * Enable or disable biometric login
   */
  static async setBiometricEnabled(enabled: boolean): Promise<void> {
    try {
      await AsyncStorage.setItem(BIOMETRIC_ENABLED_KEY, enabled.toString());
      
      // If disabling, also remove stored credentials
      if (!enabled) {
        await AsyncStorage.removeItem(BIOMETRIC_CREDENTIALS_KEY);
      }
    } catch (error) {
      console.error('Error setting biometric preference:', error);
      throw new Error('Failed to update biometric settings');
    }
  }

  /**
   * Store encrypted credentials for biometric login
   * Note: In a production app, you'd want to use more secure encryption
   */
  static async storeCredentials(email: string, hashedPassword: string): Promise<void> {
    try {
      const credentials = {
        email,
        hashedPassword,
        timestamp: Date.now(),
      };
      
      await AsyncStorage.setItem(
        BIOMETRIC_CREDENTIALS_KEY,
        JSON.stringify(credentials)
      );
    } catch (error) {
      console.error('Error storing biometric credentials:', error);
      throw new Error('Failed to store credentials for biometric login');
    }
  }

  /**
   * Retrieve stored credentials after successful biometric authentication
   */
  static async getStoredCredentials(): Promise<{email: string; hashedPassword: string} | null> {
    try {
      const stored = await AsyncStorage.getItem(BIOMETRIC_CREDENTIALS_KEY);
      if (!stored) return null;

      const credentials = JSON.parse(stored);
      
      // Check if credentials are not too old (30 days max)
      const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
      if (Date.now() - credentials.timestamp > maxAge) {
        await AsyncStorage.removeItem(BIOMETRIC_CREDENTIALS_KEY);
        return null;
      }

      return {
        email: credentials.email,
        hashedPassword: credentials.hashedPassword,
      };
    } catch (error) {
      console.error('Error retrieving stored credentials:', error);
      return null;
    }
  }

  /**
   * Clear all biometric data
   */
  static async clearBiometricData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        BIOMETRIC_ENABLED_KEY,
        BIOMETRIC_CREDENTIALS_KEY,
      ]);
    } catch (error) {
      console.error('Error clearing biometric data:', error);
      throw new Error('Failed to clear biometric data');
    }
  }
}