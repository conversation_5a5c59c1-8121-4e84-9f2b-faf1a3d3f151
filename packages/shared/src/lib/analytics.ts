import { supabase } from './supabase'
import type { ITransaction, ICategory, IAnalyticsData, IDateRange } from '../types'

export class AnalyticsService {
  static async getAnalyticsData(dateRange?: IDateRange): Promise<IAnalyticsData> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Default to last 6 months if no range provided
    const endDate = dateRange?.endDate || new Date().toISOString().split('T')[0]
    const startDate = dateRange?.startDate || new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('user_id', user.id)
      .gte('transaction_date', startDate)
      .lte('transaction_date', endDate)
      .order('transaction_date', { ascending: true })

    if (error) {
      throw new Error(`Failed to fetch analytics data: ${error.message}`)
    }

    const transactions = data as ITransaction[]

    return this.processAnalyticsData(transactions)
  }

  private static processAnalyticsData(transactions: ITransaction[]): IAnalyticsData {
    // Calculate totals
    const totalIncome = transactions
      .filter(t => t.transaction_type === 'income')
      .reduce((sum, t) => sum + t.amount, 0)

    const totalExpenses = transactions
      .filter(t => t.transaction_type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0)

    const netIncome = totalIncome - totalExpenses

    // Category breakdown
    const categoryMap: Record<string, { category: ICategory; total: number; count: number }> = {}
    
    transactions.forEach(transaction => {
      if (transaction.category) {
        const categoryId = transaction.category.id
        if (!categoryMap[categoryId]) {
          categoryMap[categoryId] = {
            category: transaction.category,
            total: 0,
            count: 0
          }
        }
        categoryMap[categoryId].total += transaction.amount
        categoryMap[categoryId].count += 1
      }
    })

    const categoryBreakdown = Object.values(categoryMap)
      .map(item => ({
        category: item.category,
        total: item.total,
        percentage: totalExpenses > 0 ? (item.total / totalExpenses) * 100 : 0
      }))
      .sort((a, b) => b.total - a.total)

    // Monthly trends
    const monthlyMap: Record<string, { income: number; expenses: number }> = {}
    
    transactions.forEach(transaction => {
      const monthKey = transaction.transaction_date.substring(0, 7) // YYYY-MM
      if (!monthlyMap[monthKey]) {
        monthlyMap[monthKey] = { income: 0, expenses: 0 }
      }
      
      if (transaction.transaction_type === 'income') {
        monthlyMap[monthKey].income += transaction.amount
      } else {
        monthlyMap[monthKey].expenses += transaction.amount
      }
    })

    const monthlyTrends = Object.entries(monthlyMap)
      .map(([month, data]) => ({
        month,
        income: data.income,
        expenses: data.expenses,
        net: data.income - data.expenses
      }))
      .sort((a, b) => a.month.localeCompare(b.month))

    // Top categories
    const topCategories = Object.values(categoryMap)
      .map(item => ({
        category: item.category,
        total: item.total,
        transactionCount: item.count
      }))
      .sort((a, b) => b.total - a.total)
      .slice(0, 5)

    return {
      totalIncome,
      totalExpenses,
      netIncome,
      categoryBreakdown,
      monthlyTrends,
      topCategories
    }
  }

  static async getSpendingTrends(months: number = 12): Promise<Array<{
    month: string
    expenses: number
    income: number
  }>> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const endDate = new Date()
    const startDate = new Date()
    startDate.setMonth(startDate.getMonth() - months)

    const { data, error } = await supabase
      .from('transactions')
      .select('amount, transaction_type, transaction_date')
      .eq('user_id', user.id)
      .gte('transaction_date', startDate.toISOString().split('T')[0])
      .lte('transaction_date', endDate.toISOString().split('T')[0])
      .order('transaction_date', { ascending: true })

    if (error) {
      throw new Error(`Failed to fetch spending trends: ${error.message}`)
    }

    const transactions = data as Array<{
      amount: number
      transaction_type: 'income' | 'expense'
      transaction_date: string
    }>

    const monthlyData: Record<string, { income: number; expenses: number }> = {}
    
    transactions.forEach(transaction => {
      const monthKey = transaction.transaction_date.substring(0, 7)
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { income: 0, expenses: 0 }
      }
      
      if (transaction.transaction_type === 'income') {
        monthlyData[monthKey].income += transaction.amount
      } else {
        monthlyData[monthKey].expenses += transaction.amount
      }
    })

    return Object.entries(monthlyData)
      .map(([month, data]) => ({
        month,
        expenses: data.expenses,
        income: data.income
      }))
      .sort((a, b) => a.month.localeCompare(b.month))
  }


  static formatMonth(monthString: string): string {
    const date = new Date(monthString + '-01')
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      year: 'numeric' 
    })
  }
}