import { supabase } from './supabase'
import type { IBudget, ICategory, ITransaction } from '../types'
import type { BudgetData } from '../schemas/budget'

export interface IBudgetWithProgress extends IBudget {
  spent: number
  remaining: number
  progress: number
  isOverBudget: boolean
  transactions?: ITransaction[]
}

export class BudgetService {
  static async createBudget(data: BudgetData): Promise<IBudget> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const budgetData = {
      name: data.name,
      amount: data.amount,
      period: data.period,
      category_id: data.category_id || null,
      start_date: data.start_date.toISOString().split('T')[0],
      end_date: data.end_date ? data.end_date.toISOString().split('T')[0] : null,
      user_id: user.id,
    }

    const { data: budget, error } = await supabase
      .from('budgets')
      .insert(budgetData)
      .select(`
        *,
        category:categories(*)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to create budget: ${error.message}`)
    }

    return budget as IBudget
  }

  static async getBudgets(): Promise<IBudget[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('budgets')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch budgets: ${error.message}`)
    }

    return data as IBudget[]
  }

  static async getBudgetWithProgress(budgetId: string): Promise<IBudgetWithProgress> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Fetch budget
    const { data: budget, error: budgetError } = await supabase
      .from('budgets')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('id', budgetId)
      .eq('user_id', user.id)
      .single()

    if (budgetError) {
      throw new Error(`Failed to fetch budget: ${budgetError.message}`)
    }

    // Calculate progress
    const progress = await this.calculateBudgetProgress(budget as IBudget)
    
    return progress
  }

  static async getBudgetsWithProgress(): Promise<IBudgetWithProgress[]> {
    const budgets = await this.getBudgets()
    
    const budgetsWithProgress = await Promise.all(
      budgets.map((budget) => this.calculateBudgetProgress(budget))
    )

    return budgetsWithProgress
  }

  static async updateBudget(id: string, data: Partial<BudgetData>): Promise<IBudget> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const updateData: any = {}

    if (data.name !== undefined) updateData.name = data.name
    if (data.amount !== undefined) updateData.amount = data.amount
    if (data.period !== undefined) updateData.period = data.period
    if (data.category_id !== undefined) updateData.category_id = data.category_id || null
    if (data.start_date !== undefined) {
      updateData.start_date = data.start_date.toISOString().split('T')[0]
    }
    if (data.end_date !== undefined) {
      updateData.end_date = data.end_date ? data.end_date.toISOString().split('T')[0] : null
    }

    updateData.updated_at = new Date().toISOString()

    const { data: budget, error } = await supabase
      .from('budgets')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select(`
        *,
        category:categories(*)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to update budget: ${error.message}`)
    }

    return budget as IBudget
  }

  static async deleteBudget(id: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { error } = await supabase
      .from('budgets')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      throw new Error(`Failed to delete budget: ${error.message}`)
    }
  }

  private static async calculateBudgetProgress(budget: IBudget): Promise<IBudgetWithProgress> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Calculate date range for budget period
    const { startDate, endDate } = this.getBudgetDateRange(budget)

    // Build query for transactions
    let query = supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('user_id', user.id)
      .eq('transaction_type', 'expense')
      .gte('transaction_date', startDate)
      .lte('transaction_date', endDate)

    // Filter by category if budget is category-specific
    if (budget.category_id) {
      query = query.eq('category_id', budget.category_id)
    }

    const { data: transactions, error } = await query

    if (error) {
      throw new Error(`Failed to fetch transactions for budget: ${error.message}`)
    }

    // Calculate spending
    const spent = transactions?.reduce((sum, transaction) => sum + transaction.amount, 0) || 0
    const remaining = Math.max(0, budget.amount - spent)
    const progress = Math.min(100, (spent / budget.amount) * 100)
    const isOverBudget = spent > budget.amount

    return {
      ...budget,
      spent,
      remaining,
      progress,
      isOverBudget,
      transactions: transactions as ITransaction[]
    }
  }

  private static getBudgetDateRange(budget: IBudget): { startDate: string; endDate: string } {
    const budgetStartDate = new Date(budget.start_date)
    const now = new Date()
    
    // If budget has specific end date, use it
    if (budget.end_date) {
      return {
        startDate: budget.start_date,
        endDate: budget.end_date
      }
    }

    // Calculate end date based on period
    let endDate: Date

    switch (budget.period) {
      case 'weekly':
        endDate = new Date(budgetStartDate)
        endDate.setDate(endDate.getDate() + 7)
        break
      case 'monthly':
        endDate = new Date(budgetStartDate)
        endDate.setMonth(endDate.getMonth() + 1)
        break
      case 'yearly':
        endDate = new Date(budgetStartDate)
        endDate.setFullYear(endDate.getFullYear() + 1)
        break
      default:
        endDate = new Date(budgetStartDate)
        endDate.setMonth(endDate.getMonth() + 1)
    }

    // For recurring budgets, find the current period
    while (endDate < now) {
      switch (budget.period) {
        case 'weekly':
          budgetStartDate.setDate(budgetStartDate.getDate() + 7)
          endDate.setDate(endDate.getDate() + 7)
          break
        case 'monthly':
          budgetStartDate.setMonth(budgetStartDate.getMonth() + 1)
          endDate.setMonth(endDate.getMonth() + 1)
          break
        case 'yearly':
          budgetStartDate.setFullYear(budgetStartDate.getFullYear() + 1)
          endDate.setFullYear(endDate.getFullYear() + 1)
          break
      }
    }

    return {
      startDate: budgetStartDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    }
  }

  static async checkBudgetAlerts(): Promise<IBudgetWithProgress[]> {
    const budgetsWithProgress = await this.getBudgetsWithProgress()
    
    // Return budgets that are at 80% or over their limit
    return budgetsWithProgress.filter(budget => budget.progress >= 80)
  }

  static async createRecurringBudgets(): Promise<IBudget[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Get all budgets that don't have an end_date (recurring)
    const { data: recurringBudgets, error } = await supabase
      .from('budgets')
      .select('*')
      .eq('user_id', user.id)
      .is('end_date', null)

    if (error) {
      throw new Error(`Failed to fetch recurring budgets: ${error.message}`)
    }

    const now = new Date()
    const newBudgets: IBudget[] = []

    for (const budget of recurringBudgets || []) {
      const { endDate } = this.getBudgetDateRange(budget as IBudget)
      
      // If current period has ended, create new budget for next period
      if (new Date(endDate) < now) {
        const newStartDate = new Date(endDate)
        newStartDate.setDate(newStartDate.getDate() + 1)

        const newBudgetData = {
          name: budget.name,
          amount: budget.amount,
          period: budget.period,
          category_id: budget.category_id,
          start_date: newStartDate.toISOString().split('T')[0],
          end_date: null,
          user_id: user.id,
        }

        const { data: newBudget, error: createError } = await supabase
          .from('budgets')
          .insert(newBudgetData)
          .select(`
            *,
            category:categories(*)
          `)
          .single()

        if (createError) {
          console.error(`Failed to create recurring budget: ${createError.message}`)
          continue
        }

        newBudgets.push(newBudget as IBudget)
      }
    }

    return newBudgets
  }
}