import { z } from 'zod'

export const budgetSchema = z.object({
  name: z.string().min(1, 'Budget name is required').max(100, 'Budget name must be under 100 characters'),
  amount: z.number().positive('Budget amount must be greater than 0'),
  period: z.enum(['weekly', 'monthly', 'yearly']),
  category_id: z.string().uuid('Please select a category').optional(),
  start_date: z.date(),
  end_date: z.date().optional(),
})

// Base form schema without transform for form state
export const budgetFormInputSchema = z.object({
  name: z.string().min(1, 'Budget name is required').max(100, 'Budget name must be under 100 characters'),
  amount: z.string().min(1, 'Budget amount is required'),
  period: z.enum(['weekly', 'monthly', 'yearly']),
  category_id: z.string().optional(),
  start_date: z.date(),
  end_date: z.union([z.date(), z.null(), z.undefined()]).optional(),
})

// Schema with transform for final validation
export const budgetFormSchema = budgetFormInputSchema.transform((data) => ({
  ...data,
  amount: (() => {
    const num = parseFloat(data.amount.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num <= 0) {
      throw new Error('Budget amount must be a positive number')
    }
    return num
  })(),
  category_id: data.category_id || undefined,
  end_date: data.end_date || undefined,
}))

export type BudgetFormInputData = z.infer<typeof budgetFormInputSchema>
export type BudgetFormData = z.infer<typeof budgetFormSchema>
export type BudgetData = z.infer<typeof budgetSchema>