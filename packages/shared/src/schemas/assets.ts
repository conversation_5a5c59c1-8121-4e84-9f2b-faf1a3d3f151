import { z } from 'zod'

// Asset class enums
export const assetClassEnum = z.enum([
  'stocks',
  'bonds', 
  'mutual_funds',
  'etfs',
  'real_estate',
  'commodities',
  'crypto',
  'cash',
  'other'
])

export const assetSubClassEnum = z.enum([
  // Stock subcategories
  'large_cap',
  'mid_cap', 
  'small_cap',
  'international',
  'emerging_markets',
  // Bond subcategories
  'government_bonds',
  'corporate_bonds',
  'municipal_bonds',
  'treasury_bills',
  'high_yield_bonds',
  // Mutual fund subcategories
  'equity_funds',
  'debt_funds',
  'hybrid_funds',
  'index_funds',
  'sector_funds',
  // ETF subcategories
  'equity_etfs',
  'bond_etfs',
  'commodity_etfs',
  'international_etfs',
  // Real estate subcategories
  'reits',
  'real_estate_funds',
  // Commodity subcategories
  'precious_metals',
  'energy',
  'agriculture',
  // Crypto subcategories
  'bitcoin',
  'altcoins',
  'stablecoins'
])

// Asset class schema
export const assetClassSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, 'Asset class name is required'),
  class: assetClassEnum,
  sub_class: assetSubClassEnum.optional(),
  description: z.string().optional(),
  risk_level: z.enum(['low', 'medium', 'high', 'very_high']),
  liquidity: z.enum(['high', 'medium', 'low']),
  typical_holding_period: z.enum(['short_term', 'medium_term', 'long_term']),
  tax_treatment: z.enum(['equity', 'debt', 'other']),
  ltcg_period_months: z.number().min(0, 'LTCG period must be non-negative'),
  ltcg_tax_rate: z.number().min(0).max(100).optional(),
  stcg_tax_rate: z.number().min(0).max(100).optional(),
  dividend_tax_rate: z.number().min(0).max(100).optional(),
  created_at: z.string(),
  updated_at: z.string()
})

// Asset class form schemas
export const assetClassFormInputSchema = z.object({
  name: z.string().min(1, 'Asset class name is required'),
  class: assetClassEnum,
  sub_class: assetSubClassEnum.optional(),
  description: z.string().optional(),
  risk_level: z.enum(['low', 'medium', 'high', 'very_high']),
  liquidity: z.enum(['high', 'medium', 'low']),
  typical_holding_period: z.enum(['short_term', 'medium_term', 'long_term']),
  tax_treatment: z.enum(['equity', 'debt', 'other']),
  ltcg_period_months: z.string().min(1, 'LTCG period is required'),
  ltcg_tax_rate: z.string().optional(),
  stcg_tax_rate: z.string().optional(),
  dividend_tax_rate: z.string().optional()
})

export const assetClassFormSchema = assetClassFormInputSchema.transform((data) => ({
  ...data,
  ltcg_period_months: parseInt(data.ltcg_period_months),
  ltcg_tax_rate: data.ltcg_tax_rate ? parseFloat(data.ltcg_tax_rate) : undefined,
  stcg_tax_rate: data.stcg_tax_rate ? parseFloat(data.stcg_tax_rate) : undefined,
  dividend_tax_rate: data.dividend_tax_rate ? parseFloat(data.dividend_tax_rate) : undefined
}))

// Asset schema
export const assetSchema = z.object({
  id: z.string().uuid(),
  symbol: z.string().min(1, 'Symbol is required'),
  name: z.string().min(1, 'Asset name is required'),
  asset_class_id: z.string().uuid('Valid asset class is required'),
  exchange: z.string().optional(),
  currency: z.string().min(1, 'Currency is required'),
  isin: z.string().optional(),
  sector: z.string().optional(),
  industry: z.string().optional(),
  market_cap: z.number().positive().optional(),
  current_price: z.number().positive().optional(),
  last_updated: z.string().optional(),
  is_active: z.boolean(),
  created_at: z.string(),
  updated_at: z.string()
})

// Asset form schemas
export const assetFormInputSchema = z.object({
  symbol: z.string().min(1, 'Symbol is required').max(20, 'Symbol too long'),
  name: z.string().min(1, 'Asset name is required'),
  asset_class_id: z.string().uuid('Please select an asset class'),
  exchange: z.string().optional(),
  currency: z.string().min(1, 'Currency is required'),
  isin: z.string().optional(),
  sector: z.string().optional(),
  industry: z.string().optional(),
  market_cap: z.string().optional(),
  current_price: z.string().optional()
})

export const assetFormSchema = assetFormInputSchema.transform((data) => ({
  ...data,
  market_cap: data.market_cap ? parseFloat(data.market_cap) : undefined,
  current_price: data.current_price ? parseFloat(data.current_price) : undefined
}))

// Holding schema
export const holdingSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  account_id: z.string().uuid(),
  asset_id: z.string().uuid(),
  quantity: z.number().positive('Quantity must be positive'),
  average_cost: z.number().positive('Average cost must be positive'),
  current_value: z.number().optional(),
  unrealized_gain_loss: z.number().optional(),
  unrealized_gain_loss_percentage: z.number().optional(),
  total_invested: z.number().positive('Total invested must be positive'),
  last_updated: z.string(),
  created_at: z.string(),
  updated_at: z.string()
})

// Enhanced investment transaction schema with asset information
export const investmentTransactionWithAssetSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  account_id: z.string().uuid(),
  asset_id: z.string().uuid(),
  transaction_type: z.enum(['investment_buy', 'investment_sell']),
  quantity: z.number().positive('Quantity must be positive'),
  price_per_unit: z.number().positive('Price must be positive'),
  total_amount: z.number().positive('Total amount must be positive'),
  fees: z.number().min(0, 'Fees cannot be negative'),
  transaction_date: z.string(),
  description: z.string().optional(),
  realized_gain_loss: z.number().optional(),
  tax_implications: z.object({
    holding_period_months: z.number().min(0),
    is_long_term: z.boolean(),
    applicable_tax_rate: z.number().min(0).max(100),
    tax_amount: z.number().min(0)
  }).optional(),
  created_at: z.string(),
  updated_at: z.string()
})

// Tax calculation schema
export const taxCalculationSchema = z.object({
  asset_class_id: z.string().uuid(),
  purchase_date: z.string(),
  sale_date: z.string(),
  purchase_price: z.number().positive(),
  sale_price: z.number().positive(),
  quantity: z.number().positive()
})

export const taxCalculationResultSchema = z.object({
  holding_period_months: z.number(),
  is_long_term: z.boolean(),
  capital_gain_loss: z.number(),
  applicable_tax_rate: z.number(),
  tax_amount: z.number(),
  net_proceeds: z.number()
})

// Type exports
export type AssetClass = z.infer<typeof assetClassEnum>
export type AssetSubClass = z.infer<typeof assetSubClassEnum>
export type AssetClassData = z.infer<typeof assetClassSchema>
export type AssetClassFormInputData = z.infer<typeof assetClassFormInputSchema>
export type AssetClassFormData = z.infer<typeof assetClassFormSchema>
export type AssetData = z.infer<typeof assetSchema>
export type AssetFormInputData = z.infer<typeof assetFormInputSchema>
export type AssetFormData = z.infer<typeof assetFormSchema>
export type HoldingData = z.infer<typeof holdingSchema>
export type InvestmentTransactionWithAssetData = z.infer<typeof investmentTransactionWithAssetSchema>
export type TaxCalculationData = z.infer<typeof taxCalculationSchema>
export type TaxCalculationResult = z.infer<typeof taxCalculationResultSchema>
