import { z } from 'zod'

export const expenseSchema = z.object({
  amount: z.number().positive('Amount must be greater than 0'),
  category_id: z.string().uuid('Please select a category'),
  description: z.string().optional(),
  transaction_date: z.date(),
  transaction_type: z.enum(['income', 'expense']),
  attachments: z.array(z.any()).optional(),
})

// Base form schema without transform for form state
export const expenseFormInputSchema = z.object({
  amount: z.string().min(1, 'Amount is required'),
  category_id: z.string().min(1, 'Please select a category'),
  description: z.string().optional(),
  transaction_date: z.date(),
  transaction_type: z.enum(['income', 'expense']),
})

// Schema with transform for final validation
export const expenseFormSchema = expenseFormInputSchema.transform((data) => ({
  ...data,
  amount: (() => {
    const num = parseFloat(data.amount.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num <= 0) {
      throw new Error('Amount must be a positive number')
    }
    return num
  })(),
}))

export type ExpenseFormInputData = z.infer<typeof expenseFormInputSchema>
export type ExpenseFormData = z.infer<typeof expenseFormSchema>
export type ExpenseData = z.infer<typeof expenseSchema>