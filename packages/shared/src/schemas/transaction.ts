import { z } from 'zod'

// Main transaction schema for basic transactions (expense, income, transfer)
export const mainTransactionSchema = z.object({
  amount: z.number().positive('Amount must be greater than 0'),
  description: z.string().optional(),
  transaction_date: z.date(),
  transaction_type: z.enum(['income', 'expense', 'transfer']),
  fees: z.number().min(0, 'Fees cannot be negative').optional(),

  // Basic transaction fields
  category_id: z.string().uuid('Please select a category').optional(),
  account_id: z.string().uuid('Please select an account').optional(),

  // Transfer-specific fields
  to_account_id: z.string().uuid('Please select destination account').optional(),

  // Optional fields
  attachments: z.array(z.any()).optional(),
})

// Enhanced transaction schema that supports all transaction types (for backward compatibility)
export const transactionSchema = z.object({
  amount: z.number().positive('Amount must be greater than 0'),
  description: z.string().optional(),
  transaction_date: z.date(),
  transaction_type: z.enum(['income', 'expense', 'transfer', 'investment_buy', 'investment_sell', 'dividend']),
  fees: z.number().min(0, 'Fees cannot be negative').optional(),

  // Basic transaction fields
  category_id: z.string().uuid('Please select a category').optional(),
  account_id: z.string().uuid('Please select an account').optional(),

  // Transfer-specific fields
  to_account_id: z.string().uuid('Please select destination account').optional(),

  // Investment-specific fields
  investment_symbol: z.string().min(1, 'Investment symbol is required').max(10, 'Symbol too long').optional(),
  investment_quantity: z.number().positive('Quantity must be positive').optional(),
  investment_price: z.number().positive('Price must be positive').optional(),

  // Optional fields
  attachments: z.array(z.any()).optional(),
}).superRefine((data, ctx) => {
  // Validation rules based on transaction type
  switch (data.transaction_type) {
    case 'income':
    case 'expense':
      if (!data.category_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Category is required for income and expense transactions',
          path: ['category_id'],
        })
      }
      if (!data.account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Account is required for income and expense transactions',
          path: ['account_id'],
        })
      }
      break

    case 'transfer':
      if (!data.account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Source account is required for transfers',
          path: ['account_id'],
        })
      }
      if (!data.to_account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Destination account is required for transfers',
          path: ['to_account_id'],
        })
      }
      if (data.account_id === data.to_account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Source and destination accounts must be different',
          path: ['to_account_id'],
        })
      }
      break
  }
})

// Add validation for main transaction schema
export const mainTransactionValidatedSchema = mainTransactionSchema.superRefine((data, ctx) => {
  // Validation rules based on transaction type
  switch (data.transaction_type) {
    case 'income':
    case 'expense':
      if (!data.category_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Category is required for income and expense transactions',
          path: ['category_id'],
        })
      }
      if (!data.account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Account is required for income and expense transactions',
          path: ['account_id'],
        })
      }
      break

    case 'transfer':
      if (!data.account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Source account is required for transfers',
          path: ['account_id'],
        })
      }
      if (!data.to_account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Destination account is required for transfers',
          path: ['to_account_id'],
        })
      }
      if (data.account_id === data.to_account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Source and destination accounts must be different',
          path: ['to_account_id'],
        })
      }
      break
  }
})

// Enhanced transaction schema validation (for backward compatibility)
export const transactionValidatedSchema = transactionSchema.superRefine((data, ctx) => {
  // Validation rules based on transaction type
  switch (data.transaction_type) {
    case 'income':
    case 'expense':
      if (!data.category_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Category is required for income and expense transactions',
          path: ['category_id'],
        })
      }
      if (!data.account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Account is required for income and expense transactions',
          path: ['account_id'],
        })
      }
      break

    case 'transfer':
      if (!data.account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Source account is required for transfers',
          path: ['account_id'],
        })
      }
      if (!data.to_account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Destination account is required for transfers',
          path: ['to_account_id'],
        })
      }
      if (data.account_id === data.to_account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Source and destination accounts must be different',
          path: ['to_account_id'],
        })
      }
      break

    case 'investment_buy':
    case 'investment_sell':
      if (!data.account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Investment account is required',
          path: ['account_id'],
        })
      }
      if (!data.investment_symbol) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Investment symbol is required',
          path: ['investment_symbol'],
        })
      }
      if (!data.investment_quantity) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Investment quantity is required',
          path: ['investment_quantity'],
        })
      }
      if (!data.investment_price) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Investment price is required',
          path: ['investment_price'],
        })
      }
      break

    case 'dividend':
      if (!data.account_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Investment account is required for dividends',
          path: ['account_id'],
        })
      }
      if (!data.investment_symbol) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Investment symbol is required for dividends',
          path: ['investment_symbol'],
        })
      }
      break
  }
})

// Main transaction form input schema (for basic transactions only)
export const mainTransactionFormInputSchema = z.object({
  amount: z.string().min(1, 'Amount is required'),
  description: z.string().optional(),
  transaction_date: z.date(),
  transaction_type: z.enum(['income', 'expense', 'transfer']),
  fees: z.string().optional(),

  // Basic transaction fields
  category_id: z.string().optional(),
  account_id: z.string().optional(),

  // Transfer-specific fields
  to_account_id: z.string().optional(),
})

// Form input schema (before transformation) - for backward compatibility
export const transactionFormInputSchema = z.object({
  amount: z.string().min(1, 'Amount is required'),
  description: z.string().optional(),
  transaction_date: z.date(),
  transaction_type: z.enum(['income', 'expense', 'transfer', 'investment_buy', 'investment_sell', 'dividend']),
  fees: z.string().optional(),

  // Basic transaction fields
  category_id: z.string().optional(),
  account_id: z.string().optional(),

  // Transfer-specific fields
  to_account_id: z.string().optional(),

  // Investment-specific fields
  investment_symbol: z.string().optional(),
  investment_quantity: z.string().optional(),
  investment_price: z.string().optional(),

  // For investment purchases, we need a funding account
  funding_account_id: z.string().optional(),
})

// Main transaction form schema with transformation
export const mainTransactionFormSchema = mainTransactionFormInputSchema.transform((data) => ({
  ...data,
  amount: (() => {
    const num = parseFloat(data.amount.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num <= 0) {
      throw new Error('Amount must be a positive number')
    }
    return num
  })(),
  fees: data.fees ? (() => {
    const num = parseFloat(data.fees.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num < 0) {
      throw new Error('Fees must be a non-negative number')
    }
    return num
  })() : undefined,
})).pipe(mainTransactionValidatedSchema)

// Schema with transformation for final validation (backward compatibility)
export const transactionFormSchema = transactionFormInputSchema.transform((data) => ({
  ...data,
  amount: (() => {
    const num = parseFloat(data.amount.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num <= 0) {
      throw new Error('Amount must be a positive number')
    }
    return num
  })(),
  fees: data.fees ? (() => {
    const num = parseFloat(data.fees.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num < 0) {
      throw new Error('Fees must be a non-negative number')
    }
    return num
  })() : undefined,
  investment_quantity: data.investment_quantity ? (() => {
    const num = parseFloat(data.investment_quantity.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num <= 0) {
      throw new Error('Investment quantity must be a positive number')
    }
    return num
  })() : undefined,
  investment_price: data.investment_price ? (() => {
    const num = parseFloat(data.investment_price.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num <= 0) {
      throw new Error('Investment price must be a positive number')
    }
    return num
  })() : undefined,
})).pipe(transactionValidatedSchema)

// Transfer-specific schemas
export const transferFormInputSchema = z.object({
  amount: z.string().min(1, 'Amount is required'),
  description: z.string().optional(),
  transaction_date: z.date(),
  from_account_id: z.string().min(1, 'Source account is required'),
  to_account_id: z.string().min(1, 'Destination account is required'),
  fees: z.string().optional(),
}).refine(data => data.from_account_id !== data.to_account_id, {
  message: 'Source and destination accounts must be different',
  path: ['to_account_id'],
})

export const transferFormSchema = transferFormInputSchema.transform((data) => ({
  ...data,
  amount: (() => {
    const num = parseFloat(data.amount.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num <= 0) {
      throw new Error('Amount must be a positive number')
    }
    return num
  })(),
  fees: data.fees ? (() => {
    const num = parseFloat(data.fees.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num < 0) {
      throw new Error('Fees must be a non-negative number')
    }
    return num
  })() : undefined,
}))

// Investment-specific schemas
export const investmentFormInputSchema = z.object({
  amount: z.string().min(1, 'Amount is required'),
  description: z.string().optional(),
  transaction_date: z.date(),
  account_id: z.string().min(1, 'Investment account is required'),
  investment_symbol: z.string().min(1, 'Investment symbol is required'),
  investment_quantity: z.string().min(1, 'Quantity is required'),
  investment_price: z.string().min(1, 'Price is required'),
  transaction_type: z.enum(['investment_buy', 'investment_sell']),
  fees: z.string().optional(),
  funding_account_id: z.string().optional(), // Required for buy transactions
}).superRefine((data, ctx) => {
  if (data.transaction_type === 'investment_buy' && !data.funding_account_id) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Funding account is required for investment purchases',
      path: ['funding_account_id'],
    })
  }
})

export const investmentFormSchema = investmentFormInputSchema.transform((data) => ({
  ...data,
  amount: (() => {
    const num = parseFloat(data.amount.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num <= 0) {
      throw new Error('Amount must be a positive number')
    }
    return num
  })(),
  investment_quantity: (() => {
    const num = parseFloat(data.investment_quantity.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num <= 0) {
      throw new Error('Investment quantity must be a positive number')
    }
    return num
  })(),
  investment_price: (() => {
    const num = parseFloat(data.investment_price.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num <= 0) {
      throw new Error('Investment price must be a positive number')
    }
    return num
  })(),
  fees: data.fees ? (() => {
    const num = parseFloat(data.fees.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num < 0) {
      throw new Error('Fees must be a non-negative number')
    }
    return num
  })() : undefined,
}))

// Type exports
export type MainTransactionFormInputData = z.infer<typeof mainTransactionFormInputSchema>
export type MainTransactionFormData = z.infer<typeof mainTransactionFormSchema>
export type MainTransactionData = z.infer<typeof mainTransactionSchema>

export type TransactionFormInputData = z.infer<typeof transactionFormInputSchema>
export type TransactionFormData = z.infer<typeof transactionFormSchema>
export type TransactionData = z.infer<typeof transactionSchema>

export type TransferFormInputData = z.infer<typeof transferFormInputSchema>
export type TransferFormData = z.infer<typeof transferFormSchema>

export type InvestmentFormInputData = z.infer<typeof investmentFormInputSchema>
export type InvestmentFormData = z.infer<typeof investmentFormSchema>
