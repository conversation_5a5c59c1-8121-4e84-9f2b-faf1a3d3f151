// Shared utility functions
export const formatCurrency = (
  amount: number,
  currency?: string
): string => {
  // If no currency is provided, we should use the currency from the store
  // This function is kept for backward compatibility but components should use useCurrencyStore
  const currencyCode = currency || 'USD' // Fallback to USD only if no currency provided
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currencyCode,
  }).format(amount);
};

export const formatDate = (date: string | Date): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

export const calculateBudgetProgress = (
  spent: number,
  budget: number
): number => {
  return Math.min((spent / budget) * 100, 100);
};