import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface CurrencyStore {
  currency: string
  setCurrency: (currency: string) => void
  formatCurrency: (amount: number) => string
}

// Valid currency codes supported by the app
const VALID_CURRENCIES = [
  'USD', 'EUR', 'GBP', 'INR', 'JPY', 'CAD', 'AUD', 'CHF', 'SEK', 'NOK', 'DKK',
  'SGD', 'HKD', 'CNY', 'KRW', 'BRL', 'MXN', 'ARS', 'ZAR', 'RUB', 'AED', 'SAR',
  'TRY', 'ILS', 'EGP', 'NGN', 'KES', 'THB', 'MYR', 'IDR', 'PHP', 'VND', 'BDT',
  'PKR', 'LKR', 'NZD'
]

export const useCurrencyStore = create<CurrencyStore>()(
  persist(
    (set, get) => ({
      currency: 'USD', // Default currency

      setCurrency: (currency: string) => {
        // Validate currency code before setting
        const validCurrency = VALID_CURRENCIES.includes(currency) ? currency : 'USD'
        set({ currency: validCurrency })
      },

      formatCurrency: (amount: number) => {
        const { currency } = get()
        
        try {
          // Ensure amount is a valid number
          const validAmount = typeof amount === 'number' && !isNaN(amount) ? amount : 0
          
          return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(validAmount)
        } catch (error) {
          console.warn('Currency formatting error:', error)
          // Fallback if currency is invalid
          try {
            return new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD',
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(typeof amount === 'number' && !isNaN(amount) ? amount : 0)
          } catch (fallbackError) {
            console.error('Fallback currency formatting failed:', fallbackError)
            return `$${(typeof amount === 'number' && !isNaN(amount) ? amount : 0).toFixed(2)}`
          }
        }
      }
    }),
    {
      name: 'currency-store',
    }
  )
)