
## STYLING GUIDE & DESIGN SYSTEM

### Design Tokens & Colors
```css
/* Primary Brand Colors */
--primary-blue: #3B82F6;
--primary-purple: #8B5CF6;
--primary-gradient: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);

/* Semantic Colors */
--success-green: #10B981;     /* Income, positive changes */
--error-red: #EF4444;         /* Expenses, negative values */
--warning-orange: #F59E0B;    /* Budget alerts */
--info-blue: #3B82F6;         /* Information */

/* Light Mode */
--background: #FFFFFF;
--surface: #F9FAFB;
--surface-elevated: #FFFFFF;
--text-primary: #111827;
--text-secondary: #6B7280;
--text-tertiary: #9CA3AF;
--border: #E5E7EB;
--border-light: #F3F4F6;

/* Dark Mode */
--dark-background: #0A0A0B;
--dark-surface: #18181B;
--dark-surface-elevated: #27272A;
--dark-text-primary: #F9FAFB;
--dark-text-secondary: #A1A1AA;
--dark-text-tertiary: #71717A;
--dark-border: #27272A;
--dark-border-light: #18181B;
```

### Component Templates
```jsx
/* Primary Button */
className="bg-gradient-to-r from-primary-blue to-primary-purple text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5"

/* Base Card */
className="bg-surface-elevated rounded-xl border border-border-light p-6 shadow-sm hover:shadow-md transition-all duration-200"

/* Input Field */
className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20"

/* Tab Active */
className="border-primary-blue text-primary-blue"

/* Checkbox/Radio */
className="text-primary-blue focus:border-primary-blue focus:ring focus:ring-primary-blue/20"

/* Amount Positive */
className="text-success-green font-semibold"

/* Amount Negative */
className="text-error-red font-semibold"
```

### Icon Library
- **Library**: Lucide React
- **Default Size**: 20px (w-5 h-5)
- **Stroke Width**: 2px
- Common Icons: LayoutDashboard, Receipt, PiggyBank, TrendingUp, CreditCard, Settings, User, Plus, Trash2, etc.

### Typography Scale
- **Font**: Inter (primary), Roboto Mono (code)
- **Sizes**: xs(12px), sm(14px), base(16px), lg(18px), xl(20px), 2xl(24px), 3xl(30px)
- **Weights**: normal(400), medium(500), semibold(600), bold(700)

### Spacing & Layout
- **Spacing Scale**: 1(4px), 2(8px), 3(12px), 4(16px), 5(20px), 6(24px), 8(32px), 10(40px)
- **Border Radius**: sm(2px), base(4px), md(6px), lg(8px), xl(12px), 2xl(16px), 3xl(24px), full(9999px)
- **Shadows**: sm, base, md, lg, xl with proper opacity values

### Best Practices
1. Always use semantic color variables instead of hardcoded values
2. Maintain consistent spacing using the spacing scale
3. Use gradients sparingly for emphasis and premium features
4. Implement loading states for all async operations
5. Test dark mode thoroughly for all components
6. Ensure 4.5:1 contrast ratio for accessibility
7. Use consistent animation timing (200ms for most transitions)

## CURRENCY REQUIREMENT
**CRITICAL**: Always use user's selected currency for all amount displays. Never hardcode USD or any specific currency - fetch from currency store/context.

## PROJECT FOLDER STRUCTURE

```
portfolio_tracker/
├── .env                          # MCP configuration
├── CLAUDE.md                     # This file - project instructions
├── DEVELOPMENT_PLAN.md           # Overall project roadmap
├── PHASES_OVERVIEW.md            # Phase tracking
├── PHASE_*.md                    # Individual phase documentation
├── TODO.md                       # Current task list
├── finance-app-style-guide-llm.md # Complete styling guide
├── package.json                  # Root workspace configuration
├── turbo.json                    # Turborepo configuration
├── tsconfig.json                 # Root TypeScript config
│
├── apps/
│   ├── web/                      # Next.js Web Application
│   │   ├── .env.local           # Web environment variables
│   │   ├── src/
│   │   │   ├── app/             # Next.js App Router pages
│   │   │   │   ├── auth/        # Authentication pages
│   │   │   │   ├── dashboard/   # Dashboard page
│   │   │   │   ├── expenses/    # Expenses page
│   │   │   │   ├── budgets/     # Budgets page
│   │   │   │   ├── profile/     # Profile settings page
│   │   │   │   ├── layout.tsx   # Root layout
│   │   │   │   └── page.tsx     # Landing page
│   │   │   ├── components/      # Web-specific UI components
│   │   │   │   ├── AmountDisplay.tsx
│   │   │   │   ├── AnalyticsDashboard.tsx
│   │   │   │   ├── BudgetDashboard.tsx
│   │   │   │   ├── BudgetForm.tsx
│   │   │   │   ├── BudgetList.tsx
│   │   │   │   ├── CategoryBadge.tsx
│   │   │   │   ├── DataExport.tsx
│   │   │   │   ├── DueTransactionsNotification.tsx
│   │   │   │   ├── ExpenseForm.tsx
│   │   │   │   ├── Modal.tsx
│   │   │   │   ├── Navbar.tsx
│   │   │   │   ├── OnboardingFlow.tsx
│   │   │   │   ├── ProfileForm.tsx
│   │   │   │   ├── ProgressBar.tsx
│   │   │   │   ├── ProtectedRoute.tsx
│   │   │   │   ├── ThemeToggle.tsx
│   │   │   │   ├── TransactionList.tsx
│   │   │   │   └── TransactionTemplates.tsx
│   │   │   └── contexts/        # Web-specific contexts
│   │   │       ├── AuthContext.tsx
│   │   │       ├── ProfileContext.tsx
│   │   │       └── ThemeContext.tsx
│   │   ├── tailwind.config.ts   # Tailwind configuration
│   │   └── package.json         # Web app dependencies
│   │
│   └── mobile/                  # React Native Mobile App
│       ├── .env                 # Mobile environment variables
│       ├── App.tsx              # Main app component
│       ├── app.json            # Expo configuration
│       ├── src/
│       │   ├── components/      # Mobile-specific UI components
│       │   │   ├── AmountDisplay.tsx
│       │   │   ├── BiometricLoginButton.tsx
│       │   │   ├── BiometricToggle.tsx
│       │   │   ├── BudgetDashboard.tsx
│       │   │   ├── BudgetForm.tsx
│       │   │   ├── BudgetList.tsx
│       │   │   ├── CategoryBadge.tsx
│       │   │   ├── DataExport.tsx
│       │   │   ├── DueTransactionsNotification.tsx
│       │   │   ├── ExpenseForm.tsx
│       │   │   ├── Modal.tsx
│       │   │   ├── OnboardingFlow.tsx
│       │   │   ├── PhotoAttachment.tsx
│       │   │   ├── ProfileForm.tsx
│       │   │   ├── ProgressBar.tsx
│       │   │   ├── ThemeToggle.tsx
│       │   │   ├── TransactionList.tsx
│       │   │   └── TransactionTemplates.tsx
│       │   └── contexts/        # Mobile-specific contexts
│       │       ├── AuthContext.tsx
│       │       ├── ProfileContext.tsx
│       │       └── ThemeContext.tsx
│       └── package.json         # Mobile app dependencies
│
└── packages/
    └── shared/                  # Shared cross-platform code
        ├── src/
        │   ├── lib/             # Core business logic
        │   │   ├── analytics.ts
        │   │   ├── biometric.mobile.ts
        │   │   ├── biometric.web.ts
        │   │   ├── budget.ts
        │   │   ├── expenses.ts
        │   │   ├── recurring-transactions.ts
        │   │   ├── supabase.ts
        │   │   └── supabase.mobile.ts
        │   ├── stores/          # Zustand state management
        │   │   └── currencyStore.ts
        │   ├── schemas/         # Zod validation schemas
        │   │   ├── auth.ts
        │   │   ├── budget.ts
        │   │   └── expense.ts
        │   ├── database.types.ts # Supabase generated types
        │   ├── types.ts         # TypeScript type definitions
        │   ├── utils.ts         # Utility functions
        │   ├── validators.ts    # Input validation
        │   ├── index.ts         # Web exports
        │   └── index.mobile.ts  # Mobile exports
        └── package.json         # Shared package dependencies
```

### File Addition Tracking System
**IMPORTANT**: When adding new files to the project, remember to:

1. **Update this folder structure** in CLAUDE.local.md immediately
2. **Add the file path** to the appropriate section above
3. **Note any new patterns** or architectural decisions
4. **Update component documentation** if creating new UI patterns
5. **Record dependency changes** if adding new packages

### Key Architectural Decisions
- **Platform Separation**: UI components are platform-specific (apps/web vs apps/mobile)
- **Shared Logic**: Business logic, types, and utilities are in packages/shared
- **Context Pattern**: Each platform has its own context implementations
- **Styling**: Web uses Tailwind CSS, Mobile uses NativeWind
- **State Management**: Zustand for global state, Context for component state
- **Database**: Supabase with generated TypeScript types
- **Authentication**: Supabase Auth with platform-specific implementations

### Commands to Remember
- **Build**: `npm run build` (builds all packages)
- **Dev**: `npm run dev` (starts all dev servers)
- **Test**: `npm run test` (runs all tests)
- **Lint**: `npm run lint` (lints all packages)
- **Type Check**: `npm run type-check` (TypeScript checking)

## TOOL USAGE EFFICIENCY GUIDELINES

### Before Using Search Tools
**ALWAYS CHECK CLAUDE.local.md FIRST** - The project structure is already documented above.

### Efficient Tool Call Pattern
1. **Read CLAUDE.local.md** → Identify documented file paths
2. **Use known paths directly** → Go straight to specific files
3. **Make targeted reads** → Avoid unnecessary searching

**❌ Inefficient Approach:**
```
Task → Grep → LS → Multiple Read calls to discover structure
```

**✅ Efficient Approach:**
```
Read CLAUDE.local.md → Direct Read calls to documented paths
```

### Examples
**Instead of:** Using Task/Grep to find navigation components
**Do:** Check "components/" section in folder structure above

**Instead of:** Searching for page files with LS
**Do:** Go directly to `/apps/web/src/app/[page]/page.tsx`

**Instead of:** Multiple tool calls to discover project layout  
**Do:** Reference the documented folder structure

### File Addition Protocol
**MANDATORY**: When creating, deleting, or renaming ANY file:
1. **AUTOMATICALLY update the folder structure** in this document immediately
2. **DO NOT wait for user instruction** - this must happen proactively
3. **Follow documented architectural patterns** 
4. **Reference existing component lists** before creating duplicates
5. **Maintain the file tracking system** for future sessions

**CRITICAL RULE**: File operations (create/rename/delete) MUST trigger immediate CLAUDE.local.md updates without being asked.

### Benefits
- Reduces tool calls from ~15 to ~5 per task
- Faster task completion
- Leverages existing documentation properly
- Maintains project knowledge continuity