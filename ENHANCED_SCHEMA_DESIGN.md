# Enhanced Database Schema Design

## Overview
This document outlines the database schema enhancements needed to support:
1. Multiple Account Types (Bank, Investment, Savings, Credit Card)
2. Transfer Transactions between accounts
3. Investment as Transfer Logic
4. Enhanced Category Management

## Current Schema Analysis

### Existing Tables
- `categories` - Basic category management with user-specific and default categories
- `transactions` - Simple income/expense transactions with category references
- `budgets` - Budget tracking linked to categories
- `transaction_templates` - Recurring transaction templates
- `user_profiles` - User preferences and settings

### Current Limitations
1. **No Account Management**: Transactions exist without account context
2. **Limited Transaction Types**: Only 'income' and 'expense', no transfers
3. **No Balance Tracking**: No way to track account balances
4. **No Investment Logic**: No support for investment purchases/sales
5. **Limited Category Management**: Users cannot create/edit categories easily

## New Schema Design

### 1. Accounts Table
```sql
CREATE TABLE accounts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  account_type VARCHAR(20) NOT NULL CHECK (account_type IN ('bank', 'investment', 'savings', 'credit_card', 'cash')),
  account_number VARCHAR(50), -- Last 4 digits or identifier
  institution_name VARCHAR(100),
  currency VARCHAR(3) DEFAULT 'USD',
  current_balance DECIMAL(15,2) DEFAULT 0.00,
  available_balance DECIMAL(15,2), -- For credit cards (available credit)
  credit_limit DECIMAL(15,2), -- For credit cards
  interest_rate DECIMAL(5,4), -- For savings/investment accounts
  is_active BOOLEAN DEFAULT true,
  is_primary BOOLEAN DEFAULT false, -- One primary account per type
  account_metadata JSONB, -- Flexible storage for account-specific data
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(user_id, name), -- Account names must be unique per user
  CHECK (current_balance >= 0 OR account_type = 'credit_card'), -- Credit cards can have negative balance
  CHECK (NOT (account_type != 'credit_card' AND credit_limit IS NOT NULL)) -- Only credit cards have credit limits
);

-- Indexes
CREATE INDEX idx_accounts_user_id ON accounts(user_id);
CREATE INDEX idx_accounts_type ON accounts(user_id, account_type);
CREATE INDEX idx_accounts_active ON accounts(user_id, is_active);
```

### 2. Enhanced Transactions Table
```sql
-- Add new columns to existing transactions table
ALTER TABLE transactions 
ADD COLUMN account_id UUID REFERENCES accounts(id),
ADD COLUMN to_account_id UUID REFERENCES accounts(id), -- For transfers
ADD COLUMN transfer_id UUID, -- Links related transfer transactions
ADD COLUMN investment_symbol VARCHAR(10), -- For investment transactions
ADD COLUMN investment_quantity DECIMAL(15,6), -- For investment transactions
ADD COLUMN investment_price DECIMAL(10,4), -- For investment transactions
ADD COLUMN fees DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN balance_after DECIMAL(15,2), -- Account balance after transaction
ADD COLUMN transaction_status VARCHAR(20) DEFAULT 'completed' CHECK (transaction_status IN ('pending', 'completed', 'cancelled', 'failed'));

-- Update transaction_type to include new types
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_transaction_type_check;
ALTER TABLE transactions ADD CONSTRAINT transactions_transaction_type_check 
  CHECK (transaction_type IN ('income', 'expense', 'transfer', 'investment_buy', 'investment_sell', 'dividend'));

-- Add indexes
CREATE INDEX idx_transactions_account_id ON transactions(account_id);
CREATE INDEX idx_transactions_to_account_id ON transactions(to_account_id);
CREATE INDEX idx_transactions_transfer_id ON transactions(transfer_id);
CREATE INDEX idx_transactions_type_date ON transactions(transaction_type, transaction_date);
```

### 3. Account Balances History
```sql
CREATE TABLE account_balance_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
  balance DECIMAL(15,2) NOT NULL,
  balance_date DATE NOT NULL,
  transaction_id UUID REFERENCES transactions(id), -- Transaction that caused this balance
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(account_id, balance_date)
);

-- Indexes
CREATE INDEX idx_balance_history_account_date ON account_balance_history(account_id, balance_date);
```

### 4. Enhanced Categories Table
```sql
-- Add new columns to existing categories table
ALTER TABLE categories 
ADD COLUMN is_system BOOLEAN DEFAULT false, -- System categories (Transfer, Investment, etc.)
ADD COLUMN parent_category_id UUID REFERENCES categories(id), -- For subcategories
ADD COLUMN sort_order INTEGER DEFAULT 0,
ADD COLUMN is_active BOOLEAN DEFAULT true,
ADD COLUMN category_metadata JSONB; -- Flexible storage for category-specific data

-- Add indexes
CREATE INDEX idx_categories_parent ON categories(parent_category_id);
CREATE INDEX idx_categories_user_active ON categories(user_id, is_active);
CREATE INDEX idx_categories_type_active ON categories(type, is_active);
```

### 5. Investment Holdings Table
```sql
CREATE TABLE investment_holdings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
  symbol VARCHAR(10) NOT NULL,
  quantity DECIMAL(15,6) NOT NULL DEFAULT 0,
  average_cost DECIMAL(10,4) NOT NULL DEFAULT 0,
  current_price DECIMAL(10,4),
  market_value DECIMAL(15,2),
  unrealized_gain_loss DECIMAL(15,2),
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(account_id, symbol),
  CHECK (quantity >= 0)
);

-- Indexes
CREATE INDEX idx_holdings_account ON investment_holdings(account_id);
CREATE INDEX idx_holdings_symbol ON investment_holdings(symbol);
```

## Data Migration Strategy

### Phase 1: Add New Tables
1. Create `accounts` table
2. Create `account_balance_history` table  
3. Create `investment_holdings` table
4. Add new columns to existing tables

### Phase 2: Data Migration
1. Create default "Cash" account for existing users
2. Link existing transactions to default account
3. Calculate and set initial account balances
4. Create system categories (Transfer, Investment, etc.)

### Phase 3: Update Application Logic
1. Update transaction creation to require account_id
2. Implement transfer transaction logic
3. Add investment transaction handling
4. Update balance calculation triggers

## Database Triggers and Functions

### 1. Balance Update Trigger
```sql
CREATE OR REPLACE FUNCTION update_account_balance()
RETURNS TRIGGER AS $$
BEGIN
  -- Update account balance when transaction is inserted/updated/deleted
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    -- Update source account balance
    UPDATE accounts 
    SET current_balance = current_balance + 
      CASE 
        WHEN NEW.transaction_type = 'income' THEN NEW.amount
        WHEN NEW.transaction_type = 'expense' THEN -NEW.amount
        WHEN NEW.transaction_type = 'transfer' AND accounts.id = NEW.account_id THEN -NEW.amount
        WHEN NEW.transaction_type = 'transfer' AND accounts.id = NEW.to_account_id THEN NEW.amount
        WHEN NEW.transaction_type = 'investment_buy' THEN -NEW.amount
        WHEN NEW.transaction_type = 'investment_sell' THEN NEW.amount
        ELSE 0
      END,
    updated_at = NOW()
    WHERE id = NEW.account_id OR id = NEW.to_account_id;
    
    RETURN NEW;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_account_balance
  AFTER INSERT OR UPDATE OR DELETE ON transactions
  FOR EACH ROW EXECUTE FUNCTION update_account_balance();
```

### 2. Investment Holdings Update Trigger
```sql
CREATE OR REPLACE FUNCTION update_investment_holdings()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.transaction_type IN ('investment_buy', 'investment_sell') THEN
    INSERT INTO investment_holdings (account_id, symbol, quantity, average_cost)
    VALUES (NEW.account_id, NEW.investment_symbol, 
            CASE WHEN NEW.transaction_type = 'investment_buy' THEN NEW.investment_quantity ELSE -NEW.investment_quantity END,
            NEW.investment_price)
    ON CONFLICT (account_id, symbol)
    DO UPDATE SET
      quantity = investment_holdings.quantity + 
        CASE WHEN NEW.transaction_type = 'investment_buy' THEN NEW.investment_quantity ELSE -NEW.investment_quantity END,
      average_cost = CASE 
        WHEN NEW.transaction_type = 'investment_buy' THEN
          ((investment_holdings.quantity * investment_holdings.average_cost) + (NEW.investment_quantity * NEW.investment_price)) /
          (investment_holdings.quantity + NEW.investment_quantity)
        ELSE investment_holdings.average_cost
      END,
      last_updated = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_investment_holdings
  AFTER INSERT ON transactions
  FOR EACH ROW EXECUTE FUNCTION update_investment_holdings();
```

## Security Considerations

### Row Level Security (RLS)
```sql
-- Enable RLS on all new tables
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE account_balance_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE investment_holdings ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can only access their own accounts" ON accounts
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own balance history" ON account_balance_history
  FOR ALL USING (auth.uid() = (SELECT user_id FROM accounts WHERE id = account_id));

CREATE POLICY "Users can only access their own holdings" ON investment_holdings
  FOR ALL USING (auth.uid() = (SELECT user_id FROM accounts WHERE id = account_id));
```

## Next Steps
1. Implement database migrations
2. Update TypeScript types
3. Create service layer functions
4. Build UI components
5. Add comprehensive testing
